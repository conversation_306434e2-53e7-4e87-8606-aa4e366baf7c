<?xml version="1.0" encoding="UTF-8"?>
<ExternalServiceRegistration xmlns="http://soap.sforce.com/2006/04/metadata">
    <label>PartReserveCallout</label>
    <namedCredentialReference>MuleSoft_Credential</namedCredentialReference>
    <operations>
        <active>true</active>
        <name>partx20reserve</name>
    </operations>
    <registrationProviderType>Custom</registrationProviderType>
    <schema>{
  &quot;openapi&quot;: &quot;3.0.1&quot;,
  &quot;info&quot;: {
    &quot;title&quot;: &quot;PartReserveCallout&quot;,
    &quot;description&quot;: &quot;&quot;
  },
  &quot;paths&quot;: {
    &quot;/dms/api/v1/part-inventory&quot;: {
      &quot;post&quot;: {
        &quot;description&quot;: &quot;&quot;,
        &quot;operationId&quot;: &quot;Part Reserve&quot;,
        &quot;requestBody&quot;: {
          &quot;description&quot;: &quot;&quot;,
          &quot;content&quot;: {
            &quot;application/json&quot;: {
              &quot;schema&quot;: {
                &quot;type&quot;: &quot;array&quot;,
                &quot;items&quot;: {
                  &quot;type&quot;: &quot;object&quot;,
                  &quot;properties&quot;: {
                    &quot;repairOrderNumberItem&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;requestQuantity&quot;: {
                      &quot;type&quot;: &quot;integer&quot;,
                      &quot;format&quot;: &quot;int64&quot;
                    },
                    &quot;reservationID&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;requestID&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;dealer&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;repairOrderNumber&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;partCode&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;orderUnit&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;isDeleted&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;itemID&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;type&quot;: {
                      &quot;type&quot;: &quot;integer&quot;,
                      &quot;format&quot;: &quot;int64&quot;
                    }
                  }
                }
              }
            }
          },
          &quot;required&quot;: true
        },
        &quot;responses&quot;: {
          &quot;2XX&quot;: {
            &quot;description&quot;: &quot;&quot;,
            &quot;content&quot;: {
              &quot;application/json&quot;: {
                &quot;schema&quot;: {
                  &quot;type&quot;: &quot;object&quot;,
                  &quot;properties&quot;: {
                    &quot;result&quot;: {
                      &quot;type&quot;: &quot;object&quot;,
                      &quot;properties&quot;: {
                        &quot;code&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        },
                        &quot;description&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        },
                        &quot;number_of_records&quot;: {
                          &quot;type&quot;: &quot;integer&quot;,
                          &quot;format&quot;: &quot;int64&quot;
                        },
                        &quot;message&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        },
                        &quot;status&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        }
                      }
                    },
                    &quot;data&quot;: {
                      &quot;type&quot;: &quot;array&quot;,
                      &quot;items&quot;: {
                        &quot;type&quot;: &quot;object&quot;,
                        &quot;properties&quot;: {
                          &quot;repairOrderNumberItem&quot;: {
                            &quot;type&quot;: &quot;string&quot;
                          },
                          &quot;reservationID&quot;: {
                            &quot;type&quot;: &quot;string&quot;
                          },
                          &quot;ReservationQuantity&quot;: {
                            &quot;type&quot;: &quot;integer&quot;,
                            &quot;format&quot;: &quot;int64&quot;
                          },
                          &quot;partCode&quot;: {
                            &quot;type&quot;: &quot;string&quot;
                          },
                          &quot;orderUnit&quot;: {
                            &quot;type&quot;: &quot;string&quot;
                          },
                          &quot;message&quot;: {
                            &quot;type&quot;: &quot;string&quot;
                          },
                          &quot;isDeleted&quot;: {
                            &quot;type&quot;: &quot;string&quot;
                          },
                          &quot;itemID&quot;: {
                            &quot;type&quot;: &quot;string&quot;
                          },
                          &quot;status&quot;: {
                            &quot;type&quot;: &quot;string&quot;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}</schema>
    <schemaType>OpenApi3</schemaType>
    <serviceBinding>{&quot;host&quot;:&quot;&quot;,&quot;basePath&quot;:&quot;/&quot;,&quot;allowedSchemes&quot;:[],&quot;requestMediaTypes&quot;:[],&quot;responseMediaTypes&quot;:[],&quot;compatibleMediaTypes&quot;:{}}</serviceBinding>
    <status>Complete</status>
    <systemVersion>6</systemVersion>
</ExternalServiceRegistration>
