<?xml version="1.0" encoding="UTF-8"?>
<ExternalServiceRegistration xmlns="http://soap.sforce.com/2006/04/metadata">
    <label>SMSAPICallout</label>
    <namedCredentialReference>SMS_OTP</namedCredentialReference>
    <operations>
        <active>true</active>
        <name>requestx20otp</name>
    </operations>
    <operations>
        <active>true</active>
        <name>sendx20otp</name>
    </operations>
    <operations>
        <active>true</active>
        <name>otpx20validate</name>
    </operations>
    <registrationProviderType>Custom</registrationProviderType>
    <schema>{
  &quot;openapi&quot;: &quot;3.0.1&quot;,
  &quot;info&quot;: {
    &quot;title&quot;: &quot;SMSAPICallout&quot;,
    &quot;description&quot;: &quot;&quot;
  },
  &quot;paths&quot;: {
    &quot;/common-service/api/v1/otp/request&quot;: {
      &quot;post&quot;: {
        &quot;description&quot;: &quot;&quot;,
        &quot;operationId&quot;: &quot;Request OTP&quot;,
        &quot;parameters&quot;: [{
            &quot;name&quot;: &quot;expireIn&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;integer&quot;,
              &quot;format&quot;: &quot;int32&quot;
            }
          },
          {
            &quot;name&quot;: &quot;otpType&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;maxAttempt&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;integer&quot;,
              &quot;format&quot;: &quot;int32&quot;
            }
          },
          {
            &quot;name&quot;: &quot;otpLength&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;integer&quot;,
              &quot;format&quot;: &quot;int32&quot;
            }
          },
          {
            &quot;name&quot;: &quot;service&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;transId&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: false,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;function&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          }
        ],
        &quot;requestBody&quot;: {
          &quot;description&quot;: &quot;&quot;,
          &quot;content&quot;: {
            &quot;application/json&quot;: {
              &quot;schema&quot;: {
                &quot;type&quot;: &quot;object&quot;,
                &quot;properties&quot;: {
                  &quot;otpLength&quot;: {
                    &quot;type&quot;: &quot;integer&quot;,
                    &quot;format&quot;: &quot;int64&quot;
                  },
                  &quot;maxAttempt&quot;: {
                    &quot;type&quot;: &quot;integer&quot;,
                    &quot;format&quot;: &quot;int64&quot;
                  },
                  &quot;transId&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  },
                  &quot;service&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  },
                  &quot;function&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  },
                  &quot;expireIn&quot;: {
                    &quot;type&quot;: &quot;integer&quot;,
                    &quot;format&quot;: &quot;int64&quot;
                  },
                  &quot;otpType&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  }
                }
              }
            }
          },
          &quot;required&quot;: true
        },
        &quot;responses&quot;: {
          &quot;2XX&quot;: {
            &quot;description&quot;: &quot;&quot;,
            &quot;content&quot;: {
              &quot;application/json&quot;: {
                &quot;schema&quot;: {
                  &quot;type&quot;: &quot;object&quot;,
                  &quot;properties&quot;: {
                    &quot;maxAttempt&quot;: {
                      &quot;type&quot;: &quot;integer&quot;,
                      &quot;format&quot;: &quot;int64&quot;
                    },
                    &quot;result&quot;: {
                      &quot;type&quot;: &quot;object&quot;,
                      &quot;properties&quot;: {
                        &quot;code&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        },
                        &quot;description&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        },
                        &quot;message&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        }
                      }
                    },
                    &quot;transId&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;expirationTime&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;otp&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    &quot;/sms-gateway/api/v1/sms&quot;: {
      &quot;post&quot;: {
        &quot;description&quot;: &quot;&quot;,
        &quot;operationId&quot;: &quot;Send OTP&quot;,
        &quot;parameters&quot;: [{
            &quot;name&quot;: &quot;content&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;receivers&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;sender&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;templateID&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: false,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          }
        ],
        &quot;requestBody&quot;: {
          &quot;description&quot;: &quot;&quot;,
          &quot;content&quot;: {
            &quot;application/json&quot;: {
              &quot;schema&quot;: {
                &quot;type&quot;: &quot;object&quot;,
                &quot;properties&quot;: {
                  &quot;receivers&quot;: {
                    &quot;type&quot;: &quot;array&quot;,
                    &quot;items&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    }
                  },
                  &quot;sender&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  },
                  &quot;templateID&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  },
                  &quot;content&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  }
                }
              }
            }
          },
          &quot;required&quot;: true
        },
        &quot;responses&quot;: {
          &quot;2XX&quot;: {
            &quot;description&quot;: &quot;&quot;,
            &quot;content&quot;: {
              &quot;application/json&quot;: {
                &quot;schema&quot;: {
                  &quot;type&quot;: &quot;object&quot;,
                  &quot;properties&quot;: {
                    &quot;ackid&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;time&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    },
                    &quot;status&quot;: {
                      &quot;type&quot;: &quot;object&quot;,
                      &quot;properties&quot;: {
                        &quot;code&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        },
                        &quot;desc&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    &quot;/common-service/api/v1/otp/verify&quot;: {
      &quot;post&quot;: {
        &quot;description&quot;: &quot;&quot;,
        &quot;operationId&quot;: &quot;OTP Validate&quot;,
        &quot;parameters&quot;: [{
            &quot;name&quot;: &quot;transID&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;service&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;function&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          },
          {
            &quot;name&quot;: &quot;otp&quot;,
            &quot;in&quot;: &quot;query&quot;,
            &quot;description&quot;: &quot;&quot;,
            &quot;required&quot;: true,
            &quot;allowEmptyValue&quot;: false,
            &quot;schema&quot;: {
              &quot;type&quot;: &quot;string&quot;
            }
          }
        ],
        &quot;requestBody&quot;: {
          &quot;description&quot;: &quot;&quot;,
          &quot;content&quot;: {
            &quot;application/json&quot;: {
              &quot;schema&quot;: {
                &quot;type&quot;: &quot;object&quot;,
                &quot;properties&quot;: {
                  &quot;transId&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  },
                  &quot;service&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  },
                  &quot;function&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  },
                  &quot;otp&quot;: {
                    &quot;type&quot;: &quot;string&quot;
                  }
                }
              }
            }
          },
          &quot;required&quot;: true
        },
        &quot;responses&quot;: {
          &quot;2XX&quot;: {
            &quot;description&quot;: &quot;&quot;,
            &quot;content&quot;: {
              &quot;application/json&quot;: {
                &quot;schema&quot;: {
                  &quot;type&quot;: &quot;object&quot;,
                  &quot;properties&quot;: {
                    &quot;result&quot;: {
                      &quot;type&quot;: &quot;object&quot;,
                      &quot;properties&quot;: {
                        &quot;code&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        },
                        &quot;description&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        },
                        &quot;message&quot;: {
                          &quot;type&quot;: &quot;string&quot;
                        }
                      }
                    },
                    &quot;transId&quot;: {
                      &quot;type&quot;: &quot;string&quot;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}</schema>
    <schemaType>OpenApi3</schemaType>
    <serviceBinding>{&quot;host&quot;:&quot;&quot;,&quot;basePath&quot;:&quot;/&quot;,&quot;allowedSchemes&quot;:[],&quot;requestMediaTypes&quot;:[],&quot;responseMediaTypes&quot;:[],&quot;compatibleMediaTypes&quot;:{}}</serviceBinding>
    <status>Complete</status>
    <systemVersion>6</systemVersion>
</ExternalServiceRegistration>
