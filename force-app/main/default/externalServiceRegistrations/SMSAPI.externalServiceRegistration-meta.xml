<?xml version="1.0" encoding="UTF-8"?>
<ExternalServiceRegistration xmlns="http://soap.sforce.com/2006/04/metadata">
    <label>SMSAPI</label>
    <namedCredentialReference>SMS_OTP</namedCredentialReference>
    <operations>
        <active>true</active>
        <name>sendx20otp</name>
    </operations>
    <registrationProviderType>SchemaInferred</registrationProviderType>
    <schema>{&quot;openapi&quot;:&quot;3.0.1&quot;,&quot;info&quot;:{&quot;title&quot;:&quot;SMSAPI&quot;,&quot;description&quot;:&quot;&quot;},&quot;paths&quot;:{&quot;/sms-gateway/api/v1/sms&quot;:{&quot;post&quot;:{&quot;description&quot;:&quot;&quot;,&quot;operationId&quot;:&quot;Send OTP&quot;,&quot;parameters&quot;:[{&quot;name&quot;:&quot;content&quot;,&quot;in&quot;:&quot;query&quot;,&quot;description&quot;:&quot;&quot;,&quot;required&quot;:true,&quot;allowEmptyValue&quot;:false,&quot;schema&quot;:{&quot;type&quot;:&quot;string&quot;}},{&quot;name&quot;:&quot;receivers&quot;,&quot;in&quot;:&quot;query&quot;,&quot;description&quot;:&quot;&quot;,&quot;required&quot;:true,&quot;allowEmptyValue&quot;:false,&quot;schema&quot;:{&quot;type&quot;:&quot;string&quot;}},{&quot;name&quot;:&quot;sender&quot;,&quot;in&quot;:&quot;query&quot;,&quot;description&quot;:&quot;&quot;,&quot;required&quot;:true,&quot;allowEmptyValue&quot;:false,&quot;schema&quot;:{&quot;type&quot;:&quot;string&quot;}},{&quot;name&quot;:&quot;templateID&quot;,&quot;in&quot;:&quot;query&quot;,&quot;description&quot;:&quot;&quot;,&quot;required&quot;:true,&quot;allowEmptyValue&quot;:false,&quot;schema&quot;:{&quot;type&quot;:&quot;string&quot;}}],&quot;requestBody&quot;:{&quot;description&quot;:&quot;&quot;,&quot;content&quot;:{&quot;application/json&quot;:{&quot;schema&quot;:{&quot;type&quot;:&quot;object&quot;,&quot;properties&quot;:{&quot;receivers&quot;:{&quot;type&quot;:&quot;array&quot;,&quot;items&quot;:{&quot;type&quot;:&quot;string&quot;}},&quot;sender&quot;:{&quot;type&quot;:&quot;string&quot;},&quot;templateID&quot;:{&quot;type&quot;:&quot;string&quot;},&quot;content&quot;:{&quot;type&quot;:&quot;string&quot;}}}}},&quot;required&quot;:true},&quot;responses&quot;:{&quot;2XX&quot;:{&quot;description&quot;:&quot;&quot;,&quot;content&quot;:{&quot;application/json&quot;:{&quot;schema&quot;:{&quot;type&quot;:&quot;object&quot;,&quot;properties&quot;:{&quot;ackid&quot;:{&quot;type&quot;:&quot;string&quot;},&quot;time&quot;:{&quot;type&quot;:&quot;string&quot;},&quot;status&quot;:{&quot;type&quot;:&quot;object&quot;,&quot;properties&quot;:{&quot;code&quot;:{&quot;type&quot;:&quot;string&quot;},&quot;desc&quot;:{&quot;type&quot;:&quot;string&quot;}}}}}}}}}}}}}</schema>
    <schemaType>OpenApi3</schemaType>
    <serviceBinding>{&quot;host&quot;:&quot;&quot;,&quot;basePath&quot;:&quot;/&quot;,&quot;allowedSchemes&quot;:[],&quot;requestMediaTypes&quot;:[],&quot;responseMediaTypes&quot;:[],&quot;compatibleMediaTypes&quot;:{}}</serviceBinding>
    <status>Complete</status>
    <systemVersion>6</systemVersion>
</ExternalServiceRegistration>
