{"appPageId": "2650a30b-d46c-4ff7-bfa1-e7bd6488d85b", "componentName": "siteforce:sldsOneColLayout", "dataProviders": [], "id": "70d2613a-4df5-48bb-a1e0-fe5a525a2bf8", "label": "Campaign List", "regions": [{"id": "3504ad6b-18e6-4751-967f-8356ceefeab0", "regionName": "header", "type": "region"}, {"components": [{"componentAttributes": {"tabsetConfig": {"UUID": "61d26a59-289f-4f9b-86cb-5c5aa38bf5a7", "activeTab": "tab1", "tabs": [{"UUID": "052a7b80-f2b5-4639-b716-2fe6551004e4", "allowGuestUser": true, "locked": false, "seedComponents": [{"attributes": {"richTextValue": "                                  <p><br/></p>                                <p style=&quot;text-align:left;&quot;>                                     Use these campaigns to generate leads and grow your business                                 </p>                       "}, "fqn": "forceCommunity:richTextInline"}, {"attributes": {}, "fqn": "forceCommunity:campaignTileList"}], "tabKey": "tab1", "tabName": "Campaign Marketplace"}, {"UUID": "7be6585b-e2e9-43f4-9401-bcc52dc8c34f", "allowGuestUser": true, "locked": false, "seedComponents": [{"attributes": {"filterName": "Recent", "scope": "Campaign"}, "fqn": "forceCommunity:objectHome"}], "tabKey": "tab2", "tabName": "All Campaigns"}], "useOverflowMenu": false}}, "componentName": "forceCommunity:tabLayout", "id": "61d26a59-289f-4f9b-86cb-5c5aa38bf5a7", "regions": [{"components": [{"componentAttributes": {"richTextValue": "                                  <p><br/></p>                                <p style=&quot;text-align:left;&quot;>                                     Use these campaigns to generate leads and grow your business                                 </p>                       "}, "componentName": "forceCommunity:richTextInline", "id": "cc6402e1-85bc-41ac-adcd-26fde7e9748e", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"bodyField": "Description", "categoryField": "Type", "dataFieldsConfig": "", "highlight": "__", "highlightColor": "#007DB8", "listLabel": "", "listViewName": "AllActiveCampaigns", "showImage": true, "sortBy": "Name", "sortOrder": "Ascending", "titleField": "Name"}, "componentName": "forceCommunity:campaignTileList", "id": "8c31f29f-4da1-40cf-9447-f31be9fc025e", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "052a7b80-f2b5-4639-b716-2fe6551004e4", "regionLabel": "Campaign Marketplace", "regionName": "tab1", "renditionMap": {}, "type": "region"}, {"components": [{"componentAttributes": {"enableInlineEdit": true, "filterName": "Recent", "layout": "FULL", "pageSize": 25, "scope": "Campaign", "showActionBar": true, "showChartsPanel": true, "showDisplay": "showall", "showFilterPanel": true, "showImageIcon": true, "showManualRefreshButton": true, "showObjectName": true, "showPinnedList": true, "showSearchBar": true}, "componentName": "forceCommunity:objectHome", "id": "b001c124-11d3-49d4-b87f-a1b57f1f3765", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "7be6585b-e2e9-43f4-9401-bcc52dc8c34f", "regionLabel": "All Campaigns", "regionName": "tab2", "renditionMap": {}, "type": "region"}], "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "b2e025a9-b160-42c5-960d-aa538a951227", "regionName": "content", "type": "region"}, {"id": "0bc0807c-ae14-4b75-9451-d479a5efb85a", "regionName": "footer", "type": "region"}], "themeLayoutType": "Inner", "type": "view", "viewType": "list-701"}