{"appPageId": "2650a30b-d46c-4ff7-bfa1-e7bd6488d85b", "componentName": "siteforce:sldsTwoCol84Layout", "dataProviders": [], "id": "3b7fd352-4a8b-47eb-a190-4345eff80d5a", "label": "Home", "regions": [{"components": [{"componentAttributes": {"richTextValue": "             <h1 style=\"text-align: center;\">                 <b style=\"font-size: 28px; color: #1e2443;\">Welcome!</b>             </h1>             <p style=\"text-align: center;\">                 <span style=\"font-size: 20px;\">Work on leads, register deals, and track progress with analytics.</span>             </p>        "}, "componentName": "forceCommunity:richTextInline", "id": "5ae6c50a-db41-497f-afdf-21908ced230a", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "6feda4e9-45f0-4ca0-80d6-466a80c1e26c", "regionName": "header", "type": "region"}, {"components": [{"componentAttributes": {"richTextValue": "<img src='/sfsites/c/img/community/prm/prm-default-banner.png'          alt='Banner image for Partner Central template' style='width:100%;'/>"}, "componentName": "forceCommunity:richTextInline", "id": "16dfe413-a32d-4b02-922b-1bc37145a37f", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"tabsetConfig": {"UUID": "58d422ed-f390-4e65-8b75-c184ea630fe2", "activeTab": "tab1", "tabs": [{"UUID": "6c612596-18e8-4b2e-9660-c6e9141a26e9", "allowGuestUser": true, "locked": false, "seedComponents": [{"attributes": {}, "fqn": "forceCommunity:dashboard"}], "tabKey": "tab1", "tabName": "Business Summary"}, {"UUID": "e6e8b5da-6f0a-4f95-8560-34f4f63d719a", "allowGuestUser": true, "locked": false, "seedComponents": [{"attributes": {"type": "Home"}, "fqn": "forceCommunity:forceCommunityFeed"}], "tabKey": "tab2", "tabName": "News & Updates"}, {"UUID": "6bc90c61-1736-403f-8cf8-097bbaa69f01", "allowGuestUser": false, "locked": false, "seedComponents": [], "tabKey": "94277", "tabName": "My Feed"}], "useOverflowMenu": false}}, "componentName": "forceCommunity:tabLayout", "id": "58d422ed-f390-4e65-8b75-c184ea630fe2", "regions": [{"components": [{"componentAttributes": {"height": "485", "recordId": "prm_winter24_ch_sales_dashboardSPLIT1739874831654"}, "componentName": "forceCommunity:dashboard", "id": "05674b08-9031-4608-ba14-f27bc3b16fbb", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "6c612596-18e8-4b2e-9660-c6e9141a26e9", "regionLabel": "Business Summary", "regionName": "tab1", "renditionMap": {}, "type": "region"}, {"components": [{"componentAttributes": {"canChangeSorting": true, "defaultFilter": "", "defaultSortOrderHomeFeed": "Relevance", "defaultSortOrderTopicsFeed": "Relevance", "feedDesign": "DEFAULT", "hasFeedSearch": true, "subjectId": "{!recordId}", "type": "Home"}, "componentName": "forceCommunity:forceCommunityFeed", "id": "9b9cff73-ea60-496f-a217-6ae4b5dc3577", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "e6e8b5da-6f0a-4f95-8560-34f4f63d719a", "regionLabel": "News & Updates", "regionName": "tab2", "renditionMap": {}, "type": "region"}, {"components": [{"componentAttributes": {"canChangeSorting": true, "defaultFilter": "", "defaultSortOrderHomeFeed": "Relevance", "defaultSortOrderTopicsFeed": "Relevance", "feedDesign": "DEFAULT", "hasFeedSearch": true, "subjectId": "{!recordId}", "type": "News"}, "componentName": "forceCommunity:forceCommunityFeed", "id": "02e2c194-a426-4171-8c64-5fb6f74d87d7", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "6bc90c61-1736-403f-8cf8-097bbaa69f01", "regionLabel": "My Feed", "regionName": "94277", "renditionMap": {}, "type": "region"}], "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "2fed85b9-09f7-4e27-91f9-a61a8998ef18", "regionName": "content", "type": "region"}, {"components": [{"componentAttributes": {"actions": [{"isPublic": true, "name": "NewLead"}, {"isPublic": true, "name": "NewOpportunity"}, {"isPublic": true, "name": "NewTask"}, {"isPublic": true, "name": "NewCase"}], "label": "Quick Create"}, "componentName": "forceCommunity:createRecordButton", "id": "f266c0c4-8a57-48e2-89e9-4a0288497d16", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"listViewIdForNavigation": "AllOpenLeads", "navigateToListView": true, "pageSize": 5, "sortBy": "CreatedDate", "title": "Lead Inbox"}, "componentName": "forceCommunity:leadInbox", "id": "9b4f7a65-a6f6-4ef0-aad8-0ca7c5bee042", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"enableInlineEdit": true, "filterName": "MyOpportunities", "layout": "COMPACT", "pageSize": 5, "scope": "Opportunity", "showActionBar": true, "showChartsPanel": true, "showDisplay": "showall", "showFilterPanel": true, "showImageIcon": true, "showManualRefreshButton": true, "showObjectName": true, "showPinnedList": true, "showSearchBar": true}, "componentName": "forceCommunity:objectHome", "id": "64ea0fe7-440c-49cf-bd26-742d00a5112c", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"enableInlineEdit": true, "filterName": "OPEN", "layout": "COMPACT", "pageSize": 5, "scope": "Task", "showActionBar": true, "showChartsPanel": true, "showDisplay": "showall", "showFilterPanel": true, "showImageIcon": true, "showManualRefreshButton": true, "showObjectName": true, "showPinnedList": true, "showSearchBar": true}, "componentName": "forceCommunity:objectHome", "id": "6ee727e6-8358-48e1-8aa2-c3dc86711df7", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "54747617-ac1d-42d7-ba8f-fdecda488c5c", "regionName": "sidebar", "type": "region"}, {"id": "6b6f0be8-579e-4ad8-adb3-5323677d732b", "regionName": "footer", "type": "region"}, {"components": [{"componentAttributes": {"customHeadTags": "", "description": "", "title": "Home"}, "componentName": "forceCommunity:seoAssistant", "id": "34bb068b-fa4c-44d0-a54e-5e8cadada4cd", "renditionMap": {}, "type": "component"}], "id": "5285a30a-f003-4674-85d9-0c689b61664e", "regionName": "sfdcHiddenRegion", "type": "region"}], "themeLayoutType": "Inner", "type": "view", "viewType": "home"}