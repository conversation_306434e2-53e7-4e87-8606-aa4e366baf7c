{"appPageId": "9f43e56a-0624-4028-afa3-94c9db8a3cd5", "componentName": "siteforce:sldsOneColLayout", "dataProviders": [], "id": "38bbd4ad-19ae-4381-959e-022b51659787", "label": "Forgot Password", "regions": [{"components": [{"componentAttributes": {"richTextValue": "<div style=\"text-align: center;\"><span class=\"inverseTextColor\" style=\"font-size:22px\">PASSWORD RESET</span></div>"}, "componentName": "forceCommunity:richTextInline", "id": "1cca5fc0-98af-4942-9d48-1c57cff784df", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"richTextValue": "<div class=\"inverseTextColor\" style=\"text-align: center;\">To reset your password, we'll need your username.<br/> We'll send password reset instructions to the email address associated with your account.</div>"}, "componentName": "forceCommunity:richTextInline", "id": "49df8096-1327-47d3-b8d8-3ff998217410", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"checkEmailUrl": "./CheckPasswordResetEmail", "submitButtonLabel": "Reset Password", "usernameLabel": "Username"}, "componentName": "salesforceIdentity:forgotPassword2", "id": "7fb9fb3d-3e91-463f-b244-084f0a7c4e36", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"richTextValue": "<div style=\"text-align: center;\"><a class=\"inverseTextColor\" href=\"./\">Cancel</a></div>"}, "componentName": "forceCommunity:richTextInline", "id": "38e59f1f-18ff-48e7-9714-7c2e74139bcb", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "c37d4c49-062a-40ec-b730-35980de22bac", "regionName": "content", "type": "region"}, {"components": [{"componentAttributes": {"customHeadTags": "", "description": "", "title": "Forgot Password"}, "componentName": "forceCommunity:seoAssistant", "id": "0226f4c7-5378-41f4-8127-e7bcd66d128c", "renditionMap": {}, "type": "component"}], "id": "3184c7b0-14ad-486e-8dde-17a430691f00", "regionName": "sfdcHiddenRegion", "type": "region"}], "themeLayoutType": "<PERSON><PERSON>", "type": "view", "viewType": "forgot-password"}