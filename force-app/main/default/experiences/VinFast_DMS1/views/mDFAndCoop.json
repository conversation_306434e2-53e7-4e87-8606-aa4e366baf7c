{"appPageId": "2650a30b-d46c-4ff7-bfa1-e7bd6488d85b", "componentName": "siteforce:sldsOneColLayout", "dataProviders": [], "id": "d6e40e14-10c7-4214-88cb-c1bb350b2f18", "label": "MDF and Coop", "regions": [{"id": "6e184601-e0d0-48a6-b22e-227028fccdd9", "regionName": "header", "type": "region"}, {"components": [{"componentAttributes": {"richTextValue": "             <p>                 <span style=\"font-size: 20px;\">Request and Manage your Market Developments Funds.</span>             </p>        "}, "componentName": "forceCommunity:richTextInline", "id": "23e3e2f7-42f0-4011-8fd8-8b215145ec98", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}, {"componentAttributes": {"tabsetConfig": {"UUID": "3f0745ed-1c14-4da8-9441-e83ca1fda764", "activeTab": "tab1", "tabs": [{"UUID": "7b2f1816-647a-4db3-b2f6-e541aec877e0", "allowGuestUser": false, "locked": false, "seedComponents": [{"attributes": {"filterName": "Recent", "scope": "PartnerFundAllocation"}, "fqn": "forceCommunity:objectHome"}], "tabKey": "tab1", "tabName": "Fund Allocations"}, {"UUID": "2e256321-9717-4a58-b8e7-d51dfc451634", "allowGuestUser": false, "locked": false, "seedComponents": [{"attributes": {"filterName": "Recent", "scope": "PartnerFundRequest"}, "fqn": "forceCommunity:objectHome"}], "tabKey": "tab2", "tabName": "Fund Requests"}, {"UUID": "5dace73e-02e8-4fe6-a9c4-f0786818c522", "allowGuestUser": false, "locked": false, "seedComponents": [{"attributes": {"filterName": "Recent", "scope": "PartnerFundClaim"}, "fqn": "forceCommunity:objectHome"}], "tabKey": "tab3", "tabName": "Fund Claims"}], "useOverflowMenu": false}}, "componentName": "forceCommunity:tabLayout", "id": "3f0745ed-1c14-4da8-9441-e83ca1fda764", "regions": [{"components": [{"componentAttributes": {"enableInlineEdit": true, "filterName": "Recent", "layout": "FULL", "pageSize": 25, "scope": "PartnerFundAllocation", "showActionBar": true, "showChartsPanel": true, "showDisplay": "showall", "showFilterPanel": true, "showImageIcon": true, "showManualRefreshButton": true, "showObjectName": true, "showPinnedList": true, "showSearchBar": true}, "componentName": "forceCommunity:objectHome", "id": "61fb6515-ad88-4dab-ad0c-5a28aca6eb1a", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "7b2f1816-647a-4db3-b2f6-e541aec877e0", "regionLabel": "Fund Allocations", "regionName": "tab1", "renditionMap": {}, "type": "region"}, {"components": [{"componentAttributes": {"enableInlineEdit": true, "filterName": "Recent", "layout": "FULL", "pageSize": 25, "scope": "PartnerFundRequest", "showActionBar": true, "showChartsPanel": true, "showDisplay": "showall", "showFilterPanel": true, "showImageIcon": true, "showManualRefreshButton": true, "showObjectName": true, "showPinnedList": true, "showSearchBar": true}, "componentName": "forceCommunity:objectHome", "id": "10734ffc-39c5-49f4-884e-2ff21a0e9372", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "2e256321-9717-4a58-b8e7-d51dfc451634", "regionLabel": "Fund Requests", "regionName": "tab2", "renditionMap": {}, "type": "region"}, {"components": [{"componentAttributes": {"enableInlineEdit": true, "filterName": "Recent", "layout": "FULL", "pageSize": 25, "scope": "PartnerFundClaim", "showActionBar": true, "showChartsPanel": true, "showDisplay": "showall", "showFilterPanel": true, "showImageIcon": true, "showManualRefreshButton": true, "showObjectName": true, "showPinnedList": true, "showSearchBar": true}, "componentName": "forceCommunity:objectHome", "id": "4c6d2d8e-331e-4ef9-b739-48c10db1d336", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "5dace73e-02e8-4fe6-a9c4-f0786818c522", "regionLabel": "Fund Claims", "regionName": "tab3", "renditionMap": {}, "type": "region"}], "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "4710fe7e-d4fc-47a1-9902-0bf2086527e4", "regionName": "content", "type": "region"}, {"id": "30929dfa-cd72-40a5-90eb-f65dbeaa9d7f", "regionName": "footer", "type": "region"}], "themeLayoutType": "Inner", "type": "view", "viewType": "mdf"}