{"appPageId": "2650a30b-d46c-4ff7-bfa1-e7bd6488d85b", "componentName": "siteforce:sldsOneColLayout", "dataProviders": [], "id": "b39f1e93-910e-4d72-b886-5d1142230c1a", "label": "User Settings", "regions": [{"id": "d36cd152-d798-440d-8af4-eb76526965ce", "regionName": "header", "type": "region"}, {"components": [{"componentAttributes": {"accountSectionTitle": "Account", "accountTabName": "Account Management", "connectedSourcesTabName": "Connected Sources", "deleteAccountButtonTitle": "Deactivate My Account", "deleteAccountConfirmationPopupBody": "After you deactivate your account, you'll lose access to this site.", "deleteAccountConfirmationPopupButtonTitle": "Got It", "deleteAccountConfirmationPopupTitle": "Deactivate your account?", "deleteAccountErrorMessage": "Something went wrong. Contact your site administrator.", "deleteAccountSectionTitle": "Deactivate Account", "emailNotificationsTitle": "Email Notifications", "emailSectionDescText": "When email notifications are enabled, email me when someone:", "hideAccountDeleteSection": true, "hideChatterNotificationSettings": false, "hideConnectedSources": false, "hideEmailNotificationSettings": false, "hideProfileVisibilitySettings": false, "id": "{!recordId}", "locationTitle": "Location", "profileSectionDescText": "Customize who is able to see what on your profile page", "profileSectionToolTipText": "Restricted: Visible to the employees of the company that created the site. Members: Visible to logged-in members. Public: Visible to anyone viewing pages that don't require login.", "profileVisibilityTitle": "Profile Visibility", "settingsPageTitle": "My Settings"}, "componentName": "forceCommunity:userSettingsCustomizable", "id": "6c0b5f0b-c070-494c-a98d-6c335855a48e", "renderPriority": "NEUTRAL", "renditionMap": {}, "type": "component"}], "id": "8dbf92c4-8c9a-4664-8e57-944145550dda", "regionName": "content", "type": "region"}, {"id": "7c9770bc-dfe1-428e-b306-3c3f42b6642e", "regionName": "footer", "type": "region"}], "themeLayoutType": "Inner", "type": "view", "viewType": "usersettings"}