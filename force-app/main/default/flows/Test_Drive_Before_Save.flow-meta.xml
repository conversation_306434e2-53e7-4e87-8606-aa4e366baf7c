<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <name>Customer_has_dup_TD_Msg</name>
        <label>Customer has dup TD Msg</label>
        <locationX>314</locationX>
        <locationY>1643</locationY>
        <customErrorMessages>
            <errorMessage>A Test Drive Request already exists for this customer with the same vehicle model during the selected time slot. Please modify the existing request or select a different time slot.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Owner_has_dup_TD_Msg</name>
        <label>Owner has dup TD Msg</label>
        <locationX>50</locationX>
        <locationY>1427</locationY>
        <customErrorMessages>
            <errorMessage>A Test Drive Request already exists for this Sales Advisor within the selected time slot. Please click check box Aware of time confliction if you still want to create new record.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>U_Customer_has_dup_TD_Msg</name>
        <label>[U] Customer has dup TD Msg</label>
        <locationX>1370</locationX>
        <locationY>1043</locationY>
        <connector>
            <targetReference>Status_changed</targetReference>
        </connector>
        <customErrorMessages>
            <errorMessage>A Test Drive Request already exists for this customer with the same vehicle model during the selected time slot. Please modify the existing request or select a different time slot.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>U_Owner_has_dup_TD_Msg</name>
        <label>[U] Owner has dup TD Msg</label>
        <locationX>1106</locationX>
        <locationY>827</locationY>
        <connector>
            <targetReference>Status_changed</targetReference>
        </connector>
        <customErrorMessages>
            <errorMessage>A Test Drive Request already exists for this Sales Advisor within the selected time slot. Please click check box Aware of time confliction if you still want to create new record.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Check_Scenario</name>
        <label>Check Scenario</label>
        <locationX>391</locationX>
        <locationY>611</locationY>
        <defaultConnector>
            <targetReference>Is_Manually_Created</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Owned_by_Partner_User</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Owner:User.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_BU_Owner_BU</targetReference>
            </connector>
            <label>Owned by Partner User</label>
        </rules>
    </decisions>
    <decisions>
        <name>Customer_has_dup_TD</name>
        <label>Customer has dup TD</label>
        <locationX>446</locationX>
        <locationY>1535</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Dup_TB_by_Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Dup_TD_by_Customer</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Customer_has_dup_TD_Msg</targetReference>
            </connector>
            <label>Dup TB by Customer</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_BU_null</name>
        <label>Is BU null?</label>
        <locationX>545</locationX>
        <locationY>503</locationY>
        <defaultConnector>
            <targetReference>Is_Manually_Created</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_BU</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Business_Unit__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Scenario</targetReference>
            </connector>
            <label>No BU</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_change_Owner_or_Confirmed_Time</name>
        <label>Is change Owner or Confirmed Time</label>
        <locationX>1557</locationX>
        <locationY>503</locationY>
        <defaultConnector>
            <targetReference>Status_changed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Confirmed_Start__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Confirmed_End__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>U_Get_Dup_TD_by_SA</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Global_Switch_Off</name>
        <label>Is Global Switch Off?</label>
        <locationX>1518</locationX>
        <locationY>287</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>On</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.SYS_GlobalOnOffSwitch__c.SYS_IsFlowActive__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_New</targetReference>
            </connector>
            <label>On</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Manually_Created</name>
        <label>Is Manually Created?</label>
        <locationX>545</locationX>
        <locationY>995</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Manual_Create</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Assigned</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Confirmed_Start__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Confirmed_End__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Created_with_Status_Assigned_Set_Verified_By</targetReference>
            </connector>
            <label>Manual Create</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_New</name>
        <label>Is New?</label>
        <locationX>1051</locationX>
        <locationY>395</locationY>
        <defaultConnector>
            <targetReference>Is_change_Owner_or_Confirmed_Time</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Update</defaultConnectorLabel>
        <rules>
            <name>Insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_BU_null</targetReference>
            </connector>
            <label>Insert</label>
        </rules>
    </decisions>
    <decisions>
        <name>Owner_has_dup_TD</name>
        <label>Owner has dup TD</label>
        <locationX>248</locationX>
        <locationY>1319</locationY>
        <defaultConnector>
            <targetReference>Get_Dup_TD_by_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Dup_TD_by_Owner</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Dup_TD_by_SA</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Aware_of_time_confliction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Owner_has_dup_TD_Msg</targetReference>
            </connector>
            <label>Dup TD by Owner</label>
        </rules>
    </decisions>
    <decisions>
        <name>Status_changed</name>
        <label>Status changed?</label>
        <locationX>1557</locationX>
        <locationY>1403</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>From_New_Assigned</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Assigned</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Verified_By</targetReference>
            </connector>
            <label>From New &gt; Assigned</label>
        </rules>
    </decisions>
    <decisions>
        <name>U_Customer_has_dup_TD</name>
        <label>[U] Customer has dup TD</label>
        <locationX>1502</locationX>
        <locationY>935</locationY>
        <defaultConnector>
            <targetReference>Status_changed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>U_Dup_TB_by_Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>U_Get_Dup_TD_by_Customer</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>U_Customer_has_dup_TD_Msg</targetReference>
            </connector>
            <label>[U] Dup TB by Customer</label>
        </rules>
    </decisions>
    <decisions>
        <name>U_Owner_has_dup_TD</name>
        <label>[U] Owner has dup TD</label>
        <locationX>1304</locationX>
        <locationY>719</locationY>
        <defaultConnector>
            <targetReference>U_Get_Dup_TD_by_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>U_Dup_TD_by_Owner</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>U_Get_Dup_TD_by_SA</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Aware_of_time_confliction__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>U_Owner_has_dup_TD_Msg</targetReference>
            </connector>
            <label>[U] Dup TD by Owner</label>
        </rules>
    </decisions>
    <description>IDMS-18: Initial Version. Auto-populate BU and Verified By &lt;user&gt;</description>
    <environments>Default</environments>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <formulas>
        <name>Mobile</name>
        <dataType>String</dataType>
        <expression>IF(
{!$Record.Associated_Lead__c} != null, 
{!$Record.Associated_Lead__r.MobilePhone},  
IF( {!$Record.Associated_Account__r.IsPersonAccount}, {!$Record.Associated_Account__r.Phone},  {!$Record.Associated_Contact__r.MobilePhone})     
)</expression>
    </formulas>
    <interviewLabel>Test Drive - Before Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Test Drive - Before Save</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Dup_TD_by_Customer</name>
        <label>Get Dup TD by Customer</label>
        <locationX>446</locationX>
        <locationY>1427</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Customer_has_dup_TD</targetReference>
        </connector>
        <filterLogic>1 AND 10 AND ((2 AND 3) OR (4 AND 5) OR (6 AND 7) OR (8 AND 9))</filterLogic>
        <filters>
            <field>Mobile_Number__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Mobile</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Preferred_Vehicle__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Preferred_Vehicle__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Test_Drive__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Dup_TD_by_SA</name>
        <label>Get Dup TD by SA</label>
        <locationX>248</locationX>
        <locationY>1211</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Owner_has_dup_TD</targetReference>
        </connector>
        <filterLogic>1 AND ((2 AND 3) OR (4 AND 5) OR (6 AND 7) OR (8 AND 9))</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Test_Drive__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>U_Get_Dup_TD_by_Customer</name>
        <label>[U] Get Dup TD by Customer</label>
        <locationX>1502</locationX>
        <locationY>827</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>U_Customer_has_dup_TD</targetReference>
        </connector>
        <filterLogic>1 AND 10 AND 11 AND ((2 AND 3) OR (4 AND 5) OR (6 AND 7) OR (8 AND 9))</filterLogic>
        <filters>
            <field>Mobile_Number__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Mobile</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Preferred_Vehicle__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Preferred_Vehicle__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Test_Drive__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>U_Get_Dup_TD_by_SA</name>
        <label>[U] Get Dup TD by SA</label>
        <locationX>1304</locationX>
        <locationY>611</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>U_Owner_has_dup_TD</targetReference>
        </connector>
        <filterLogic>1 AND ((2 AND 3) OR (4 AND 5) OR (6 AND 7) OR (8 AND 9)) AND 10</filterLogic>
        <filters>
            <field>OwnerId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_Start__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_End__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>$Record.Confirmed_End__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Test_Drive__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Created_with_Status_Assigned_Set_Verified_By</name>
        <label>Created with Status = &apos;Assigned&apos; &gt; Set Verified By</label>
        <locationX>248</locationX>
        <locationY>1103</locationY>
        <connector>
            <targetReference>Get_Dup_TD_by_SA</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Assigned</stringValue>
            </value>
        </filters>
        <filters>
            <field>Verified_By__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Verified_By__c</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Set_BU_Owner_BU</name>
        <label>Set BU = Owner BU</label>
        <locationX>259</locationX>
        <locationY>719</locationY>
        <connector>
            <targetReference>Is_Manually_Created</targetReference>
        </connector>
        <inputAssignments>
            <field>Business_Unit__c</field>
            <value>
                <elementReference>$Record.Owner:User.AccountId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Set_Verified_By</name>
        <label>Set Verified By</label>
        <locationX>1425</locationX>
        <locationY>1511</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Verified_By__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Verified_By__c</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>1392</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_Global_Switch_Off</targetReference>
        </connector>
        <object>Test_Drive__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
