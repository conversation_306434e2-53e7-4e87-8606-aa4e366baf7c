<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <choices>
        <name>Amount_Choice</name>
        <choiceText>Amount</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Amount</stringValue>
        </value>
    </choices>
    <choices>
        <name>None_Choice</name>
        <choiceText>No discount</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>None</stringValue>
        </value>
    </choices>
    <choices>
        <name>Percentage_Choice</name>
        <choiceText>Percentage</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Percentage</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Already_added_before</name>
        <label>Already added before</label>
        <locationX>446</locationX>
        <locationY>974</locationY>
        <defaultConnector>
            <targetReference>Create_ROLI</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Added</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI_same_Work_Type</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Duplicate_Work_Type</targetReference>
            </connector>
            <label>Added</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_parent_status</name>
        <label>Check parent status?</label>
        <locationX>248</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>ROLI_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_valid</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_Concern.Work_Order__r.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Invoicing</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Concern.Work_Order__r.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Paid</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Concern.Work_Order__r.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Concern.Work_Order__r.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Canceled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Invalid_Screen</targetReference>
            </connector>
            <label>Not valid</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Actual_Duration_consider</name>
        <label>Is Actual Duration consider?</label>
        <locationX>446</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>Get_WOLI_same_Work_Type</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_actual</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Work_Type.Actual_Duration__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Actual_Duration_Screen</targetReference>
            </connector>
            <label>Yes actual</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>DurationFormula</name>
        <dataType>Number</dataType>
        <expression>IF ({!Get_Work_Type.Actual_Duration__c} = TRUE , {!Actual_Duration_is_needed_for_this_Work_Type} , {!Get_Work_Type.EstimatedDuration})</expression>
        <scale>2</scale>
    </formulas>
    <interviewLabel>WOLI - New {!$Flow.CurrentDateTime}</interviewLabel>
    <label>WOLI - New</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_ROLI</name>
        <label>Create ROLI</label>
        <locationX>578</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>ROLI_Created</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Exception_Screen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Classification__c</field>
            <value>
                <elementReference>Get_Concern.Bill_classification__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Concern__c</field>
            <value>
                <elementReference>Get_Concern.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Discount_Amount__c</field>
            <value>
                <elementReference>Discount_Amount</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Discount_Percentage__c</field>
            <value>
                <elementReference>Discount_Percentage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Duration</field>
            <value>
                <elementReference>DurationFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DurationType</field>
            <value>
                <elementReference>Get_Work_Type.DurationType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WorkOrderId</field>
            <value>
                <elementReference>Get_Concern.Work_Order__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WorkTypeId</field>
            <value>
                <elementReference>Work_Type.recordId</elementReference>
            </value>
        </inputAssignments>
        <object>WorkOrderLineItem</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Concern</name>
        <label>Get Concern</label>
        <locationX>248</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_parent_status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Concern__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_WOLI_same_Work_Type</name>
        <label>Get WOLI same Work Type</label>
        <locationX>446</locationX>
        <locationY>866</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Already_added_before</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Concern__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Concern.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>WorkTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Work_Type.recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>WorkOrderLineItem</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Work_Type</name>
        <label>Get Work Type</label>
        <locationX>446</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Actual_Duration_consider</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Work_Type.recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>WorkType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Actual_Duration_Screen</name>
        <label>Actual Duration Screen</label>
        <locationX>314</locationX>
        <locationY>674</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Get_WOLI_same_Work_Type</targetReference>
        </connector>
        <fields>
            <name>ActualDurationText</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Actual Duration is needed to consider for the selected Work type. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Actual_Duration_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Actual_Duration_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Selected_Work_Type</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Work_Type.recordName</elementReference>
                    </defaultValue>
                    <fieldText>Selected Work Type</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Actual_Duration_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Actual_Duration_is_needed_for_this_Work_Type</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Get_Work_Type.EstimatedDuration</elementReference>
                    </defaultValue>
                    <fieldText>Actual Duration</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <scale>2</scale>
                    <validationRule>
                        <errorMessage>&lt;p&gt;Actual Duration must be greater than 0. &lt;/p&gt;</errorMessage>
                        <formulaExpression>{!Actual_Duration_is_needed_for_this_Work_Type} &gt; 0</formulaExpression>
                    </validationRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Duplicate_Work_Type</name>
        <label>Duplicate Work Type</label>
        <locationX>314</locationX>
        <locationY>1082</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ROLI_Screen</targetReference>
        </connector>
        <fields>
            <name>DuplicateWorkTypeMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;This Work type has already been added to this Concern before.&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;(Work Order Line Item: {!Get_WOLI_same_Work_Type.LineItemNumber}) &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Back</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Exception_Screen</name>
        <label>Exception Screen</label>
        <locationX>842</locationX>
        <locationY>1190</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ROLI_Screen</targetReference>
        </connector>
        <fields>
            <name>ExceptionMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Failed to create Work Order Line Item. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Back</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Invalid_Screen</name>
        <label>Invalid Screen</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>InvalidMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 16px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;Work Order is no longer allowed to create a new work type.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ROLI_Created</name>
        <label>ROLI Created</label>
        <locationX>578</locationX>
        <locationY>1190</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>ROLI_Screen</targetReference>
        </connector>
        <fields>
            <name>SuccessMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;New Work Order Line Item has been created!&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Create Another</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ROLI_Screen</name>
        <label>ROLI Screen</label>
        <locationX>446</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Get_Work_Type</targetReference>
        </connector>
        <fields>
            <name>ROLI_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ROLI_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Work_Type</name>
                    <extensionName>flowruntime:lookup</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>objectApiName</name>
                        <value>
                            <stringValue>WorkOrderLineItem</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>required</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>fieldApiName</name>
                        <value>
                            <stringValue>WorkTypeId</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Work Type</stringValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ROLI_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Discount_Method</name>
                    <choiceReferences>Amount_Choice</choiceReferences>
                    <choiceReferences>Percentage_Choice</choiceReferences>
                    <choiceReferences>None_Choice</choiceReferences>
                    <dataType>String</dataType>
                    <defaultSelectedChoiceReference>None_Choice</defaultSelectedChoiceReference>
                    <fieldText>Discount Method</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Get_Concern.Bill_classification__c</leftValueReference>
                            <operator>NotEqualTo</operator>
                            <rightValue>
                                <stringValue>W</stringValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ROLI_Screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Discount_Amount</name>
                    <dataType>Number</dataType>
                    <fieldText>Discount Amount</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <scale>0</scale>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Discount_Method</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>Amount_Choice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Discount_Percentage</name>
                    <dataType>Number</dataType>
                    <fieldText>Discount Percentage</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <scale>0</scale>
                    <validationRule>
                        <errorMessage>&lt;p&gt;The input value must be between 1-100.&lt;/p&gt;</errorMessage>
                        <formulaExpression>AND (
{!Discount_Percentage} &gt; 0,
{!Discount_Percentage} &lt;= 100
)</formulaExpression>
                    </validationRule>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>Discount_Method</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <elementReference>Percentage_Choice</elementReference>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>122</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Concern</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
