<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Related_to</name>
        <label>Related to?</label>
        <locationX>446</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>No customer info</defaultConnectorLabel>
        <rules>
            <name>Lead</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_TD.Associated_Lead__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Info_Screen</targetReference>
            </connector>
            <label>Lead</label>
        </rules>
        <rules>
            <name>Person_Account</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_TD.Associated_Account__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_TD.Associated_Account__r.IsPersonAccount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Account_Info_Screen</targetReference>
            </connector>
            <label>Person Account</label>
        </rules>
        <rules>
            <name>Contact</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_TD.Associated_Contact__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Contact_Info_Screen</targetReference>
            </connector>
            <label>Contact</label>
        </rules>
    </decisions>
    <description>IDMS-18: Initial version. Show Lead/Contact/Account information.</description>
    <environments>Default</environments>
    <interviewLabel>Test Drive - Customer Information {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Test Drive - Customer Information</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_TD</name>
        <label>Get TD</label>
        <locationX>446</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Related_to</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Test_Drive__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Account_Info_Screen</name>
        <label>Account Info Screen</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_1_of_Full_name</name>
            <dataType>String</dataType>
            <defaultValue>
                <stringValue>{!Get_TD.Associated_Account__r.FirstName} {!Get_TD.Associated_Account__r.LastName} </stringValue>
            </defaultValue>
            <fieldText>Full name</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Copy_1_of_Mobile</name>
            <extensionName>flowruntime:phone</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Mobile Number</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonMobilePhone</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Copy_1_of_Email</name>
            <extensionName>flowruntime:email</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>readonly</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonEmail</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Copy_1_of_Address</name>
            <extensionName>flowruntime:address</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>addressLabel</name>
                <value>
                    <stringValue>Address</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>street</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonMailingStreet</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>city</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonMailingCity</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>province</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonMailingStateCode</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>provinceCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonMailingStateCode</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>postalCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonMailingPostalCode</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>country</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonMailingCountry</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>countryCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Account__r.PersonMailingCountryCode</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Hide</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Contact_Info_Screen</name>
        <label>Contact Info Screen</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_2_of_Full_name</name>
            <dataType>String</dataType>
            <defaultValue>
                <stringValue>{!Get_TD.Associated_Contact__r.FirstName} {!Get_TD.Associated_Contact__r.LastName} </stringValue>
            </defaultValue>
            <fieldText>Full name</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Copy_2_of_Mobile</name>
            <extensionName>flowruntime:phone</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Mobile Number</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.MobilePhone</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Copy_2_of_Email</name>
            <extensionName>flowruntime:email</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>readonly</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.Email</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Copy_2_of_Address</name>
            <extensionName>flowruntime:address</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>addressLabel</name>
                <value>
                    <stringValue>Address</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>street</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.MailingStreet</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>city</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.MailingCity</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>province</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.MailingState</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>provinceCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.MailingStateCode</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>country</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.MailingCountry</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>countryCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.MailingCountryCode</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>postalCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Contact__r.MailingPostalCode</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Hide</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Info_Screen</name>
        <label>Info Screen</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Full_name</name>
            <dataType>String</dataType>
            <defaultValue>
                <stringValue>{!Get_TD.Associated_Lead__r.FirstName} {!Get_TD.Associated_Lead__r.LastName} </stringValue>
            </defaultValue>
            <fieldText>Full name</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Mobile</name>
            <extensionName>flowruntime:phone</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Mobile Number</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.MobilePhone</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Email</name>
            <extensionName>flowruntime:email</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>readonly</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>value</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.Email</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Address</name>
            <extensionName>flowruntime:address</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>addressLabel</name>
                <value>
                    <stringValue>Address</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>street</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.Street</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>city</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.City</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>province</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.State</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>provinceCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.StateCode</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>postalCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.PostalCode</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>country</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.Country</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>countryCode</name>
                <value>
                    <elementReference>Get_TD.Associated_Lead__r.CountryCode</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Hide</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_TD</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
