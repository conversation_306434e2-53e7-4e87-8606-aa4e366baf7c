<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Noti_to_new_owner</name>
        <label>Send Noti to new owner</label>
        <locationX>182</locationX>
        <locationY>816</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <connector>
            <targetReference>Is_Completed</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>Get_Notification_Type.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>Recipient</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>Noti_Subject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>Noti_Body</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Set_recipient</name>
        <label>Set recipient</label>
        <locationX>182</locationX>
        <locationY>708</locationY>
        <assignmentItems>
            <assignToReference>Recipient</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$Record.OwnerId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Send_Noti_to_new_owner</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Is_change_owner</name>
        <label>Is change owner?</label>
        <locationX>314</locationX>
        <locationY>492</locationY>
        <defaultConnector>
            <targetReference>Is_Completed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_Change_owner</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.OwnerId</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>005</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Notification_Type</targetReference>
            </connector>
            <label>Yes, Change owner</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Completed</name>
        <label>Is Completed?</label>
        <locationX>314</locationX>
        <locationY>1008</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Status_Completed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record__Prior.Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Lead</targetReference>
            </connector>
            <label>Status &gt; Completed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Global_Switch_Off</name>
        <label>Is Global Switch Off?</label>
        <locationX>402</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>On</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.SYS_GlobalOnOffSwitch__c.SYS_IsFlowActive__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_New</targetReference>
            </connector>
            <label>On</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_New</name>
        <label>Is New?</label>
        <locationX>50</locationX>
        <locationY>384</locationY>
        <defaultConnector>
            <targetReference>Is_change_owner</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Update</defaultConnectorLabel>
        <rules>
            <name>Insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Insert</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_send</name>
        <label>Is send?</label>
        <locationX>1150</locationX>
        <locationY>492</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>TD_confirm_SMS_sent</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Send_TD_Confirm_SMS.return_code</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>200</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Send_TD_Confirm_SMS.return_description</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Request accepted</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Confirmation_Sent</targetReference>
            </connector>
            <label>TD confirm SMS sent</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Status_changed2</name>
        <label>Is Status changed?</label>
        <locationX>1348</locationX>
        <locationY>276</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Assigned_Confirmed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Confirmed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Confirmation_Sent__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_TD_Confirm_SMS</targetReference>
            </connector>
            <label>Assigned &gt; Confirmed</label>
        </rules>
    </decisions>
    <description>IDMS-708: Update [Lead] Test Drive Taken after completing a Test Drive
IDMS-18: Initial Version. Send SMS when confirming</description>
    <environments>Default</environments>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>IsNew()</expression>
    </formulas>
    <formulas>
        <name>SMS_recipient</name>
        <dataType>String</dataType>
        <expression>&apos;+91&apos; + {!$Record.Mobile_Number__c}</expression>
    </formulas>
    <interviewLabel>Test Drive - After Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Test Drive - After Save</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Notification_Type</name>
        <label>Get Notification Type</label>
        <locationX>182</locationX>
        <locationY>600</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Set_recipient</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Lead_Notification</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Set_Confirmation_Sent</name>
        <label>Set Confirmation Sent</label>
        <locationX>1018</locationX>
        <locationY>600</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Confirmation_Sent__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Confirmation_Sent__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Test Drive Taken = TRUE</description>
        <name>Update_Lead</name>
        <label>Update Lead</label>
        <locationX>50</locationX>
        <locationY>1116</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Associated_Lead__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Test_Driver_Taken__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Lead</object>
    </recordUpdates>
    <start>
        <locationX>749</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_Global_Switch_Off</targetReference>
        </connector>
        <object>Test_Drive__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <scheduledPaths>
            <name>Send_Confirm_SMS</name>
            <connector>
                <targetReference>Is_Status_changed2</targetReference>
            </connector>
            <label>Send Confirm SMS</label>
            <offsetNumber>1</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <recordField>LastModifiedDate</recordField>
            <timeSource>RecordField</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Send_TD_Confirm_SMS</name>
        <label>Send TD Confirm SMS</label>
        <locationX>1150</locationX>
        <locationY>384</locationY>
        <connector>
            <targetReference>Is_send</targetReference>
        </connector>
        <flowName>Send_SMS_API</flowName>
        <inputAssignments>
            <name>content</name>
            <value>
                <elementReference>SMS_Body</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>receiver</name>
            <value>
                <elementReference>SMS_recipient</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>templateID</name>
            <value>
                <stringValue>123123</stringValue>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <textTemplates>
        <name>Noti_Body</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>You have been assigned a Test drive. ({!$Record.Status__c})
- Model: {!$Record.Preferred_Vehicle__c}
- Confirmed Date: {!$Record.Confirmed_Start__c}</text>
    </textTemplates>
    <textTemplates>
        <name>Noti_Subject</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>You have been assigned a Test drive.</text>
    </textTemplates>
    <textTemplates>
        <name>SMS_Body</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Your Test Drive is confirmed.</text>
    </textTemplates>
    <variables>
        <name>Recipient</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
