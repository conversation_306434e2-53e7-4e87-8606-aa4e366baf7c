<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>assign_Order_vf_discount</name>
        <label>assign Order vf_discount</label>
        <locationX>50</locationX>
        <locationY>1055</locationY>
        <assignmentItems>
            <assignToReference>get_Order.VF_Discount_Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>vf_discount</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>update_Order_VF_Discount</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_Quote_discount</name>
        <label>assign Quote discount</label>
        <locationX>842</locationX>
        <locationY>1055</locationY>
        <assignmentItems>
            <assignToReference>get_quote.VF_Discount_Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>vf_discount</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>update_Quote_VF_Discount</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Quote_total_discount</name>
        <label>Quote total discount</label>
        <locationX>1194</locationX>
        <locationY>755</locationY>
        <assignmentItems>
            <assignToReference>vf_discount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loop_Quote_promotion.VF_Fixed_Amount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_Quote_promotion</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>total_discount</name>
        <label>total discount</label>
        <locationX>402</locationX>
        <locationY>755</locationY>
        <assignmentItems>
            <assignToReference>vf_discount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loop_order_VF_promotion.VF_Fixed_Amount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_order_VF_promotion</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_VF_promotion_for_Order</name>
        <label>Check VF promotion for Order</label>
        <locationX>710</locationX>
        <locationY>323</locationY>
        <defaultConnector>
            <targetReference>get_quote</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>hasQuote</defaultConnectorLabel>
        <rules>
            <name>hasOrder</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.VF_SO_No__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>get_Order</targetReference>
            </connector>
            <label>hasOrder</label>
        </rules>
    </decisions>
    <decisions>
        <description>Only update Order VF Discount</description>
        <name>Only_update_if_Order_status_is_New</name>
        <label>Only update if Order status is New</label>
        <locationX>314</locationX>
        <locationY>947</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_Order.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assign_Order_vf_discount</targetReference>
            </connector>
            <label>yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Only_update_if_Quote_status_is_New</name>
        <label>Only update if Quote status is New</label>
        <locationX>1106</locationX>
        <locationY>947</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_quote.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assign_Quote_discount</targetReference>
            </connector>
            <label>yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>VinFast Promotion After Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>VinFast Promotion After Save</label>
    <loops>
        <name>loop_order_VF_promotion</name>
        <label>loop order VF promotion</label>
        <locationX>314</locationX>
        <locationY>647</locationY>
        <collectionReference>get_all_promotion_for_order</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>total_discount</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Only_update_if_Order_status_is_New</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loop_Quote_promotion</name>
        <label>loop Quote promotion</label>
        <locationX>1106</locationX>
        <locationY>647</locationY>
        <collectionReference>get_all_promotion_for_Quote</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Quote_total_discount</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Only_update_if_Quote_status_is_New</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>save_exception</name>
        <label>save exception</label>
        <locationX>314</locationX>
        <locationY>1271</locationY>
        <inputAssignments>
            <field>Description__c</field>
            <value>
                <stringValue>Failed when updating VF_Discount on Quote, Order</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Exception_Message__c</field>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>SYS_StackTrace_c__c</field>
            <value>
                <elementReference>$Flow.InterviewGuid</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type__c</field>
            <value>
                <stringValue>Flow</stringValue>
            </value>
        </inputAssignments>
        <object>SYS_Exception__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>get_all_promotion_for_order</name>
        <label>get all promotion for order</label>
        <locationX>314</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>loop_order_VF_promotion</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>VF_SO_No__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_Order.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>VF_Promotion_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Vehicle Order Amount Deduction</stringValue>
            </value>
        </filters>
        <filters>
            <field>VF_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Potential</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>VinFast_Promotion__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_all_promotion_for_Quote</name>
        <label>get all promotion for Quote</label>
        <locationX>1106</locationX>
        <locationY>539</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>loop_Quote_promotion</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>VF_SQ_No__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_quote.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>VF_Promotion_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Vehicle Order Amount Deduction</stringValue>
            </value>
        </filters>
        <filters>
            <field>VF_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Potential</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>VinFast_Promotion__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_Order</name>
        <label>get Order</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_all_promotion_for_order</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.VF_SO_No__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Order</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_quote</name>
        <label>Get Quote</label>
        <locationX>1106</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_all_promotion_for_Quote</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.VF_SQ_No__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>update_Order_VF_Discount</name>
        <label>update Order VF_Discount</label>
        <locationX>50</locationX>
        <locationY>1163</locationY>
        <faultConnector>
            <targetReference>save_exception</targetReference>
        </faultConnector>
        <inputReference>get_Order</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>update_Quote_VF_Discount</name>
        <label>update Quote VF_Discount</label>
        <locationX>842</locationX>
        <locationY>1163</locationY>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>save_exception</targetReference>
        </faultConnector>
        <inputReference>get_quote</inputReference>
    </recordUpdates>
    <start>
        <locationX>584</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_VF_promotion_for_Order</targetReference>
        </connector>
        <object>VinFast_Promotion__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>vf_discount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
</Flow>
