<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_WC</name>
        <label>Assign WC</label>
        <locationX>138</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>WC.Concern__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Concerns.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>WC.Work_Order__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Concerns.Work_Order__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>WC.Asset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Concerns.Work_Order__r.AssetId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>WC.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Open</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>New_WCs</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>WC</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Concerns</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Check_RO_Status</name>
        <label>Check RO Status?</label>
        <locationX>380</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Invalid_Status_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RO_Valid</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Paid</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_valid_concerns</targetReference>
            </connector>
            <label>RO Valid</label>
        </rules>
    </decisions>
    <decisions>
        <name>Create_any_WCs</name>
        <label>Create any WCs?</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>No_Concern_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_create_WC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_valid_concerns</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Concerns</targetReference>
            </connector>
            <label>Yes create WC</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Work Order - Cr {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Work Order - Create Warranty Claim</label>
    <loops>
        <name>Loop_Concerns</name>
        <label>Loop Concerns</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <collectionReference>Get_valid_concerns</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_WC</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Create_WCs</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_WCs</name>
        <label>Create WCs</label>
        <locationX>50</locationX>
        <locationY>866</locationY>
        <connector>
            <targetReference>SuccessScreen</targetReference>
        </connector>
        <inputReference>New_WCs</inputReference>
    </recordCreates>
    <recordLookups>
        <name>Get_RO</name>
        <label>Get RO</label>
        <locationX>380</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_RO_Status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>WorkOrder</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get warranty concern has no warranty claim</description>
        <name>Get_valid_concerns</name>
        <label>Get Valid Concerns</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_any_WCs</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Work_Order__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Count_WC__c</field>
            <operator>EqualTo</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </filters>
        <filters>
            <field>Bill_classification__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>W</stringValue>
            </value>
        </filters>
        <filters>
            <field>Grand_Total__c</field>
            <operator>GreaterThan</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Concern__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Invalid_Status_Screen</name>
        <label>Invalid Status Screen</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>InvalidStatusMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Current status is not valid to create a Warranty Claim. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Concern_Screen</name>
        <label>No Concern Screen</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NoConcernMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;There is no Warranty Claim can be created for this Work Order. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>SuccessScreen</name>
        <label>Success Screen</label>
        <locationX>50</locationX>
        <locationY>974</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Warranty Claim(s) created successfully. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_RO</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>New_WCs</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Warranty_Claim__c</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>WC</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Warranty_Claim__c</objectType>
    </variables>
</Flow>
