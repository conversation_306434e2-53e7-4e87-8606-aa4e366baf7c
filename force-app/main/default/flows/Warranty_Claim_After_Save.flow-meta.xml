<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Notify_HO</name>
        <label>Notify HO</label>
        <locationX>50</locationX>
        <locationY>1379</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <connector>
            <targetReference>MD_Approval</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>Get_Notification_Type.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>lst_HO</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>Noti_Subject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>Noti_Body</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Notify_MD</name>
        <label>Notify MD</label>
        <locationX>50</locationX>
        <locationY>2627</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>Get_Notification_Type2.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>lst_MD</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>Noti_Subject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>Noti_Body</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_HO_members</name>
        <label>Assign HO members</label>
        <locationX>138</locationX>
        <locationY>1079</locationY>
        <assignmentItems>
            <assignToReference>lst_HO</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_HO_members.UserOrGroupId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_HO_members</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_MD_members</name>
        <label>Assign MD members</label>
        <locationX>138</locationX>
        <locationY>2327</locationY>
        <assignmentItems>
            <assignToReference>lst_MD</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_MD_members.UserOrGroupId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_MD_members</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>Noti_Body</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Warranty Claim is waiting for your approval.</stringValue>
        </value>
    </constants>
    <constants>
        <name>Noti_Subject</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Warranty Claim is waiting for your approval.</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Flow_Type</name>
        <label>Flow Type?</label>
        <locationX>116</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>HO_Approval</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Update</defaultConnectorLabel>
        <rules>
            <name>Insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Insert</label>
        </rules>
    </decisions>
    <decisions>
        <name>Global_Switch_On</name>
        <label>Global Switch On?</label>
        <locationX>523</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>On</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.SYS_GlobalOnOffSwitch__c.SYS_IsFlowActive__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Flow_Type</targetReference>
            </connector>
            <label>On</label>
        </rules>
    </decisions>
    <decisions>
        <name>HO_Approval</name>
        <label>HO Approval</label>
        <locationX>534</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>MD_Approval</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_HO</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>HO Approval</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_HO_Queue</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Yes HO</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_HO_Queue_has_Members</name>
        <label>Is HO Queue has Members?</label>
        <locationX>314</locationX>
        <locationY>863</locationY>
        <defaultConnector>
            <targetReference>MD_Approval</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_HO_members</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_HO_queue_members</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_HO_members</targetReference>
            </connector>
            <label>Yes HO members</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_MD_Queue_has_Members</name>
        <label>Is MD Queue has Members?</label>
        <locationX>314</locationX>
        <locationY>2111</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_MD_members</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_MD_queue_members</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_MD_members</targetReference>
            </connector>
            <label>Yes MD members</label>
        </rules>
    </decisions>
    <decisions>
        <name>MD_Approval</name>
        <label>MD Approval</label>
        <locationX>534</locationX>
        <locationY>1787</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_MD</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>MD Approval</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_MD_Queue</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Yes MD</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>IsNew()</expression>
    </formulas>
    <interviewLabel>Warranty Claim - After Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Warranty Claim - After Save</label>
    <loops>
        <name>Loop_HO_members</name>
        <label>Loop HO members</label>
        <locationX>50</locationX>
        <locationY>971</locationY>
        <collectionReference>Get_HO_queue_members</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_HO_members</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Notification_Type</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_MD_members</name>
        <label>Loop MD members</label>
        <locationX>50</locationX>
        <locationY>2219</locationY>
        <collectionReference>Get_MD_queue_members</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_MD_members</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Notification_Type2</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_HO_Queue</name>
        <label>Get HO Queue</label>
        <locationX>314</locationX>
        <locationY>647</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_HO_queue_members</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>AFS_HO_Approver</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_HO_queue_members</name>
        <label>Get HO queue members</label>
        <locationX>314</locationX>
        <locationY>755</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_HO_Queue_has_Members</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>GroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_HO_Queue.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_MD_Queue</name>
        <label>Get MD Queue</label>
        <locationX>314</locationX>
        <locationY>1895</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_MD_queue_members</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>AFS_MD_Approver</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_MD_queue_members</name>
        <label>Get MD queue members</label>
        <locationX>314</locationX>
        <locationY>2003</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_MD_Queue_has_Members</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>GroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_MD_Queue.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Notification_Type</name>
        <label>Get Notification Type</label>
        <locationX>50</locationX>
        <locationY>1271</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Notify_HO</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>WarrantyClaim_Notification</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Notification_Type2</name>
        <label>Get Notification Type</label>
        <locationX>50</locationX>
        <locationY>2519</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Notify_MD</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>WarrantyClaim_Notification</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>397</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Global_Switch_On</targetReference>
        </connector>
        <object>Warranty_Claim__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>lst_HO</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>lst_MD</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
