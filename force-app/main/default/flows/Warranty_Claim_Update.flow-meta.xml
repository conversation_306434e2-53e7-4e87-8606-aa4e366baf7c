<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_WC_values</name>
        <label>Assign WC values</label>
        <locationX>182</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>Get_WC.Category__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Category.topValue</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WC.Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Category.middleValue</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WC.Issue_Description__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Issue_Description</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WC.Main_Work_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Main_Work_Type</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WC.Main_Part__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Main_Part</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WC.Claim_to__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Claim_To</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_WC</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Flag_False</name>
        <label>Set Flag = False</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>ShowPart</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Flag_True</name>
        <label>Set Flag = True</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>ShowPart</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Screen</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Check_Status</name>
        <label>Check Status</label>
        <locationX>512</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Invalid_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Open</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WC.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Open</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_related_PCs</targetReference>
            </connector>
            <label>Open</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_PCs</name>
        <label>Has PCs?</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Set_Flag_False</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_PCs</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_related_PCs</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Flag_True</targetReference>
            </connector>
            <label>Yes PCs</label>
        </rules>
    </decisions>
    <dynamicChoiceSets>
        <name>Category_ChoiceSet</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Category__c</picklistField>
        <picklistObject>Warranty_Claim__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>ClaimTo_ChoiceSet</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Claim_to__c</picklistField>
        <picklistObject>Warranty_Claim__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>MainPart_ChoiceSet</name>
        <dataType>String</dataType>
        <displayField>ProductName</displayField>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Concern__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_WC.Concern__c</elementReference>
            </value>
        </filters>
        <object>ProductConsumed</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>MainWorkType_ChoiceSet</name>
        <dataType>String</dataType>
        <displayField>Work_Type_Name__c</displayField>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Concern__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_WC.Concern__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Canceled</stringValue>
            </value>
        </filters>
        <object>WorkOrderLineItem</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>Warranty Claim - {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Warranty Claim - Update</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_related_PCs</name>
        <label>Get related PCs</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Has_PCs</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Concern__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_WC.Concern__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ProductConsumed</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_WC</name>
        <label>Get WC</label>
        <locationX>512</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Warranty_Claim__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_WC</name>
        <label>Update WC</label>
        <locationX>182</locationX>
        <locationY>974</locationY>
        <connector>
            <targetReference>SuccessScreen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Exception_Screen</targetReference>
        </faultConnector>
        <inputReference>Get_WC</inputReference>
    </recordUpdates>
    <screens>
        <name>Exception_Screen</name>
        <label>Exception Screen</label>
        <locationX>578</locationX>
        <locationY>1082</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ExceptionMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Something went wrong. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Invalid_Screen</name>
        <label>Invalid Screen</label>
        <locationX>842</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>InvalidStatusMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;You can only update a Warranty Claim with status Open.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>SuccessScreen</name>
        <label>Success Screen</label>
        <locationX>182</locationX>
        <locationY>1082</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Warranty Claim has been updated successfully!&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Update_Screen</name>
        <label>Update Screen</label>
        <locationX>182</locationX>
        <locationY>758</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_WC_values</targetReference>
        </connector>
        <fields>
            <name>Update_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Update_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Category</name>
                    <extensionName>flowruntime:dependentPicklists</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>dependencyWrapperApiName</name>
                        <value>
                            <stringValue>Warranty_Claim__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>topPicklistApiName</name>
                        <value>
                            <stringValue>Category__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>middlePicklistApiName</name>
                        <value>
                            <stringValue>Type__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>topLabel</name>
                        <value>
                            <stringValue>Category</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>topRequired</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>middleLabel</name>
                        <value>
                            <stringValue>Type</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>middleRequired</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>topValue</name>
                        <value>
                            <elementReference>Get_WC.Category__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>middleValue</name>
                        <value>
                            <elementReference>Get_WC.Type__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Update_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Claim_To</name>
                    <choiceReferences>ClaimTo_ChoiceSet</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Claim_to__c</elementReference>
                    </defaultValue>
                    <fieldText>Claim To</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>Main_Work_Type</name>
                    <choiceReferences>MainWorkType_ChoiceSet</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Main_Work_Type__c</elementReference>
                    </defaultValue>
                    <fieldText>Main Work Type</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>Main_Part</name>
                    <choiceReferences>MainPart_ChoiceSet</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Main_Part__c</elementReference>
                    </defaultValue>
                    <fieldText>Main Part</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>ShowPart</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <name>Causal_Part_Code</name>
                    <extensionName>flowruntime:lookup</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>fieldApiName</name>
                        <value>
                            <stringValue>Causal_Part_Code__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Causual Part Code</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>objectApiName</name>
                        <value>
                            <stringValue>Warranty_Claim__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>recordId</name>
                        <value>
                            <elementReference>Get_WC.Causal_Part_Code__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>required</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>ShowPart</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Update_Screen_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Issue_Description</name>
                    <defaultValue>
                        <stringValue>{!Get_WC.Issue_Description__c}</stringValue>
                    </defaultValue>
                    <fieldText>Issue Description</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Update_Screen_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Update_Screen_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>Get_WC.Causal_Part_Serial_Number__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>Get_WC.Replacement_Part_Serial_Number__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Update_Screen_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>Get_WC.Part_replacement_ODO__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>Get_WC.Part_replacement_date__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Category.topValue</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Spare part warranty</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>386</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_WC</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ShowPart</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
