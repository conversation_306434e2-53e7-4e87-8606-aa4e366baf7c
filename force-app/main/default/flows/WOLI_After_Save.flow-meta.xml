<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Set_Status_PSCI</name>
        <label>Set Status PSCI</label>
        <locationX>50</locationX>
        <locationY>1355</locationY>
        <assignmentItems>
            <assignToReference>ToBePSCI.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.ProductServiceCampaignItemId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ToBePSCI.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_PSCI_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Status_related_PSCI</name>
        <label>Set Status related PSCI</label>
        <locationX>314</locationX>
        <locationY>1571</locationY>
        <assignmentItems>
            <assignToReference>ToBePSCI.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Related_PSC_Junction.Product_Service_Campaign_Item__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ToBePSCI.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_PSCI_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Status_VMP</name>
        <label>Set Status VMP</label>
        <locationX>116</locationX>
        <locationY>2063</locationY>
        <assignmentItems>
            <assignToReference>ToBeMP.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Concern__r.Vehicle_Maintenance_Plan__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ToBeMP.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_VMP</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Calculate_Discount_Amount</name>
        <label>Calculate Discount Amount</label>
        <locationX>545</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Need_calculate_concern</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>New</name>
            <conditionLogic>1 AND (2 OR 3)</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Discount_Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Discount_Percentage__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Discount</targetReference>
            </connector>
            <label>New</label>
        </rules>
        <rules>
            <name>Amount_changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Discount_Amount__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Discount_Amount__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Percentage</targetReference>
            </connector>
            <label>Amount changed</label>
        </rules>
        <rules>
            <name>Percentage_Changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Discount_Percentage__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Discount_Percentage__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Amount</targetReference>
            </connector>
            <label>Percentage Changed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Global_Switch</name>
        <label>Global Switch</label>
        <locationX>831</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Off</defaultConnectorLabel>
        <rules>
            <name>On</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.SYS_GlobalOnOffSwitch__c.SYS_IsFlowActive__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Calculate_Discount_Amount</targetReference>
            </connector>
            <label>On</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Closed</name>
        <label>Is Closed?</label>
        <locationX>545</locationX>
        <locationY>1139</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_related_PSCI</targetReference>
            </connector>
            <label>Yes closed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_PSC_Junction</name>
        <label>Is PSC Junction?</label>
        <locationX>446</locationX>
        <locationY>1463</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Is_related_VMP</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_Junction</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Related_PSC_Junction</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Status_related_PSCI</targetReference>
            </connector>
            <label>Yes Junction</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_related_PSCI</name>
        <label>Is related PSCI?</label>
        <locationX>248</locationX>
        <locationY>1247</locationY>
        <defaultConnector>
            <targetReference>Get_Related_PSC_Junction</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_PSCI</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.ProductServiceCampaignItemId</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Status_PSCI</targetReference>
            </connector>
            <label>Yes PSCI</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_related_VMP</name>
        <label>Is related VMP?</label>
        <locationX>248</locationX>
        <locationY>1955</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_VMP</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Concern__r.Vehicle_Maintenance_Plan__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Status_VMP</targetReference>
            </connector>
            <label>Yes VMP</label>
        </rules>
    </decisions>
    <decisions>
        <name>Need_calculate_concern</name>
        <label>Need calculate concern?</label>
        <locationX>545</locationX>
        <locationY>731</locationY>
        <defaultConnector>
            <targetReference>Set_Total_Input</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_need</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WorkTypeId</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Duration</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Tax_1_Amount__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Tax_2_Amount__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Recalculate_Concern</targetReference>
            </connector>
            <label>Yes need</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>AmountToPercent</name>
        <dataType>Number</dataType>
        <expression>{!$Record.Discount_Amount__c} / {!$Record.Total_Base_Amount__c} * 100</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>FromNew_Amount</name>
        <dataType>Number</dataType>
        <expression>IF ({!$Record.Discount_Amount__c} &gt; 0, 
{!$Record.Discount_Amount__c},
{!$Record.Total_Base_Amount__c} / 100 * {!$Record.Discount_Percentage__c}
)</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>FromNew_Percent</name>
        <dataType>Number</dataType>
        <expression>IF ({!$Record.Discount_Percentage__c} &gt; 0, 
{!$Record.Discount_Percentage__c},
{!$Record.Discount_Amount__c} / {!$Record.Total_Base_Amount__c} * 100
)</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>FromPercentToAmount</name>
        <dataType>Number</dataType>
        <expression>{!$Record.Total_Base_Amount__c} / 100 * {!$Record.Discount_Percentage__c}</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>IsNew()</expression>
    </formulas>
    <interviewLabel>WOLI - After Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>WOLI - After Save</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Related_PSC_Junction</name>
        <label>Get Related PSC Junction</label>
        <locationX>446</locationX>
        <locationY>1355</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_PSC_Junction</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>VIN__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Asset.Name</elementReference>
            </value>
        </filters>
        <filters>
            <field>Work_Type_ID__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WorkTypeId</elementReference>
            </value>
        </filters>
        <filters>
            <field>PSCI_status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>PSC_Juntion__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Set_Amount</name>
        <label>Set Amount</label>
        <locationX>677</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Need_calculate_concern</targetReference>
        </connector>
        <inputAssignments>
            <field>Discount_Amount__c</field>
            <value>
                <elementReference>FromPercentToAmount</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Set_Discount</name>
        <label>Set Discount</label>
        <locationX>149</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Need_calculate_concern</targetReference>
        </connector>
        <inputAssignments>
            <field>Discount_Amount__c</field>
            <value>
                <elementReference>FromNew_Amount</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Discount_Percentage__c</field>
            <value>
                <elementReference>FromNew_Percent</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Set_Percentage</name>
        <label>Set Percentage</label>
        <locationX>413</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Need_calculate_concern</targetReference>
        </connector>
        <inputAssignments>
            <field>Discount_Percentage__c</field>
            <value>
                <elementReference>AmountToPercent</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Set Taxable_Amount_Input__c, Total_Labor_Amount_Input__c</description>
        <name>Set_Total_Input</name>
        <label>Set Total Input</label>
        <locationX>545</locationX>
        <locationY>1031</locationY>
        <connector>
            <targetReference>Is_Closed</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Taxable_Amount__c</field>
            <operator>GreaterThan</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </filters>
        <filters>
            <field>Total_Labor_Amount__c</field>
            <operator>GreaterThan</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Taxable_Amount_Input__c</field>
            <value>
                <elementReference>$Record.Taxable_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Total_Labor_Amount_Input__c</field>
            <value>
                <elementReference>$Record.Total_Labor_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_PSCI_status</name>
        <label>Update PSCI status</label>
        <locationX>248</locationX>
        <locationY>1847</locationY>
        <connector>
            <targetReference>Is_related_VMP</targetReference>
        </connector>
        <inputReference>ToBePSCI</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_VMP</name>
        <label>Update VMP</label>
        <locationX>116</locationX>
        <locationY>2171</locationY>
        <inputReference>ToBeMP</inputReference>
    </recordUpdates>
    <start>
        <locationX>705</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Global_Switch</targetReference>
        </connector>
        <object>WorkOrderLineItem</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Recalculate_Concern</name>
        <label>Recalculate Concern</label>
        <locationX>413</locationX>
        <locationY>839</locationY>
        <connector>
            <targetReference>Set_Total_Input</targetReference>
        </connector>
        <flowName>Concern_Full_Calculate</flowName>
        <inputAssignments>
            <name>ConcernID</name>
            <value>
                <elementReference>$Record.Concern__c</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>ToBeMP</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Vehicle_Maintenance_Plan__c</objectType>
    </variables>
    <variables>
        <name>ToBePSCI</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ProductServiceCampaignItem</objectType>
    </variables>
</Flow>
