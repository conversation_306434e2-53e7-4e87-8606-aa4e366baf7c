<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Flow_Type</name>
        <label>Flow Type?</label>
        <locationX>50</locationX>
        <locationY>611</locationY>
        <defaultConnector>
            <targetReference>Set_Completed_Datetime</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Update</defaultConnectorLabel>
        <rules>
            <name>Insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>Insert</label>
        </rules>
    </decisions>
    <decisions>
        <name>Global_Switch_On</name>
        <label>Global Switch On?</label>
        <locationX>248</locationX>
        <locationY>287</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>On</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.SYS_GlobalOnOffSwitch__c.SYS_IsFlowActive__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_VIN</targetReference>
            </connector>
            <label>On</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>IsNew()</expression>
    </formulas>
    <interviewLabel>Warranty Claim - Before Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Warranty Claim - Before Save</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Set_Completed_Datetime</name>
        <label>Set Completed Datetime</label>
        <locationX>182</locationX>
        <locationY>719</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Completion_Date_time__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Completion_Date_time__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Status_Rejected</name>
        <label>Update Status = Rejected</label>
        <locationX>50</locationX>
        <locationY>503</locationY>
        <connector>
            <targetReference>Flow_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Rejected</stringValue>
            </value>
        </filters>
        <filters>
            <field>Number_of_rejection__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <numberValue>3.0</numberValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Rejected</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_VIN</name>
        <label>Update VIN</label>
        <locationX>50</locationX>
        <locationY>395</locationY>
        <connector>
            <targetReference>Update_Status_Rejected</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Asset__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Asset__c</field>
            <value>
                <elementReference>$Record.Work_Order__r.AssetId</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>122</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Global_Switch_On</targetReference>
        </connector>
        <object>Warranty_Claim__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
