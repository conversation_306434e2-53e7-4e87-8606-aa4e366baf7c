<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Submit_for_approval</name>
        <label>Submit for approval</label>
        <locationX>1106</locationX>
        <locationY>566</locationY>
        <actionName>submit</actionName>
        <actionType>submit</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Exception_Screen</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>objectId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>comment</name>
            <value>
                <elementReference>SubmitComment</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>submitterId</name>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>nextApproverIds</name>
            <value>
                <elementReference>AppoverIDs</elementReference>
            </value>
        </inputParameters>
        <nameSegment>submit</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_approver</name>
        <label>Assign approver</label>
        <locationX>1106</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>AppoverIDs</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_WC.Work_Order__r.Account.VF_ServiceManager__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Submit_for_approval</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Check_WC</name>
        <label>Check WC</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Confirmation_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Good</defaultConnectorLabel>
        <rules>
            <name>WO_is_cancelled</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WC.Work_Order__r.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Canceled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RO_canceled_Screen</targetReference>
            </connector>
            <label>WO is cancelled?</label>
        </rules>
        <rules>
            <name>Too_many_rejection</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WC.Number_of_rejection__c</leftValueReference>
                <operator>GreaterThanOrEqualTo</operator>
                <rightValue>
                    <numberValue>3.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Rejected_Screen</targetReference>
            </connector>
            <label>Too many rejection</label>
        </rules>
        <rules>
            <name>Not_Open</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WC.Status__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Open</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Invalid_Status_Screen</targetReference>
            </connector>
            <label>Not Open</label>
        </rules>
        <rules>
            <name>Missing_information</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_WC.Category__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_WC.Claim_to__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Missing_Info_Screen</targetReference>
            </connector>
            <label>Missing information</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Warranty Claim - Submit for Approval {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Warranty Claim - Submit for Approval</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_WC</name>
        <label>Get WC</label>
        <locationX>578</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_WC</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Warranty_Claim__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Confirmation_Screen</name>
        <label>Confirmation Screen</label>
        <locationX>1106</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_approver</targetReference>
        </connector>
        <fields>
            <name>ConfirmationMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Once you submit the warranty claim, it will be locked and you won&apos;t be able to make any further updates. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Are you certain you want to proceed? &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Exception_Screen</name>
        <label>Exception Screen</label>
        <locationX>1370</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ExceptionMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Something went wrong. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;{!$Flow.FaultMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Invalid_Status_Screen</name>
        <label>Invalid Status Screen</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>InvalidStatusMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;You can only submit for approval for a Warranty Claim with Status &lt;/span&gt;&lt;span style=&quot;font-size: 16px; background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Open.&lt;/span&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt; &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Missing_Info_Screen</name>
        <label>Missing Info Screen</label>
        <locationX>842</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>MissingInfoMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Cannot submit due to missing information in the warranty claim. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Please update it before submitting again.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Rejected_Screen</name>
        <label>Rejected Screen</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>RejectedMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Warranty claim has passed the maximum rejection. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>RO_canceled_Screen</name>
        <label>RO canceled Screen</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ROCanceledMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;The Repair Order has been canceled. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;This Warranty claim is no longer valid. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Success_Screen</name>
        <label>Success Screen</label>
        <locationX>1106</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Warranty Claim has been submitted for approval. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>452</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_WC</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>SubmitComment</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Submit warranty claim for approval.</text>
    </textTemplates>
    <variables>
        <name>AppoverIDs</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
