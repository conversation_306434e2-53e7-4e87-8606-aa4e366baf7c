<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Convert_Money_to_Words_Action_1</name>
        <label>Convert Money to Words Action 1</label>
        <locationX>314</locationX>
        <locationY>674</locationY>
        <actionName>VF_CurrencyToWordsConverter</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Set_Total_in_words</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>amount</name>
            <value>
                <elementReference>Get_Updated_Work_Order.Grand_Total__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>currencyCode</name>
            <value>
                <stringValue>ENG</stringValue>
            </value>
        </inputParameters>
        <nameSegment>VF_CurrencyToWordsConverter</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <actionCalls>
        <name>Get_RO_Taxes_Action</name>
        <label>Get RO Taxes Action</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <actionName>RepairOrderTaxesController</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Check_return</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Exception_Screen</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>recordIds</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>RepairOrderTaxesController</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Set_Total_in_words</name>
        <label>Set Total in words</label>
        <locationX>314</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>Get_Updated_Work_Order.Total_in_words__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Convert_Money_to_Words_Action_1.amountInWords</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Work_Order</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Check_return</name>
        <label>Check return?</label>
        <locationX>578</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Not_Success</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Success</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO_Taxes_Action</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>S</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Updated_Work_Order</targetReference>
            </connector>
            <label>Success</label>
        </rules>
    </decisions>
    <decisions>
        <name>No_ROLI</name>
        <label>No ROLI</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Get_RO_Taxes_Action</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_No_ROLI</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ROLIs</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>No_ROLI_screen</targetReference>
            </connector>
            <label>Yes, No ROLI</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Work {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Work Order - Get Taxes</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_ROLIs</name>
        <label>Get ROLIs</label>
        <locationX>314</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>No_ROLI</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WorkOrderId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>WorkOrderLineItem</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Updated_Work_Order</name>
        <label>Get Updated Work Order</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Convert_Money_to_Words_Action_1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>WorkOrder</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Work_Order</name>
        <label>Update Work Order</label>
        <locationX>314</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Exception_Screen</targetReference>
        </faultConnector>
        <inputReference>Get_Updated_Work_Order</inputReference>
    </recordUpdates>
    <screens>
        <name>Exception_Screen</name>
        <label>Exception Screen</label>
        <locationX>1106</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ExceptionMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Something went wrong. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_ROLI_screen</name>
        <label>No ROLI screen</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NoROLIMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;The Work Order has no Work Type and Part. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Not_Success</name>
        <label>Not Success</label>
        <locationX>842</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NotSuccessMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Something went wrong. &lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;{!Get_RO_Taxes_Action}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Success_Screen</name>
        <label>Success Screen</label>
        <locationX>314</locationX>
        <locationY>998</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Taxes have been updated successfully. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_ROLIs</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
