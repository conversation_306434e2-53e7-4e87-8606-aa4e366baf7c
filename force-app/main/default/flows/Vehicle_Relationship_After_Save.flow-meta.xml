<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Is_changed_relationship</name>
        <label>Is changed relationship?</label>
        <locationX>446</locationX>
        <locationY>731</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_changed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Relationship__c</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Active__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_PE_for_Modification</targetReference>
            </connector>
            <label>Yes changed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Deactivate</name>
        <label>Is Deactivate?</label>
        <locationX>446</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Is_changed_relationship</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_deactivated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Active__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_PE_for_deactiation</targetReference>
            </connector>
            <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
            <label>Yes deactivated</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_New</name>
        <label>Is New?</label>
        <locationX>248</locationX>
        <locationY>323</locationY>
        <defaultConnector>
            <targetReference>Is_Deactivate</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Update</defaultConnectorLabel>
        <rules>
            <name>Insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_PE_for_creation</targetReference>
            </connector>
            <label>Insert</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>IsNew()</expression>
    </formulas>
    <interviewLabel>Vehicle Relationship - After Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Vehicle Relationship - After Save</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_PE_for_creation</name>
        <label>Create PE for creation</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <inputAssignments>
            <field>Action__c</field>
            <value>
                <stringValue>Insert</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer__c</field>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Relationship__c</field>
            <value>
                <elementReference>$Record.Relationship__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>VIN__c</field>
            <value>
                <elementReference>$Record.Vehicle__r.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>recordId__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Vehicle_Relationship_Event__e</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_PE_for_deactiation</name>
        <label>Create PE for deactiation</label>
        <locationX>314</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Is_changed_relationship</targetReference>
        </connector>
        <inputAssignments>
            <field>Action__c</field>
            <value>
                <stringValue>Delete</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer__c</field>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Relationship__c</field>
            <value>
                <elementReference>$Record.Relationship__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>VIN__c</field>
            <value>
                <elementReference>$Record.Vehicle__r.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>recordId__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Vehicle_Relationship_Event__e</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_PE_for_Modification</name>
        <label>Create PE for Modification</label>
        <locationX>314</locationX>
        <locationY>839</locationY>
        <inputAssignments>
            <field>Action__c</field>
            <value>
                <stringValue>Update</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Customer__c</field>
            <value>
                <elementReference>$Record.Account__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Relationship__c</field>
            <value>
                <elementReference>$Record.Relationship__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>VIN__c</field>
            <value>
                <elementReference>$Record.Vehicle__r.Name</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>recordId__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Vehicle_Relationship_Event__e</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <start>
        <locationX>122</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_New</targetReference>
        </connector>
        <object>Vehicle_Relationship__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
