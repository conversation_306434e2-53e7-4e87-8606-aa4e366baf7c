<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <choices>
        <name>India_choice</name>
        <choiceText>India</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>India</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <environments>Default</environments>
    <interviewLabel>Warranty Claim - Info Screen {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Warranty Claim - Info Screen</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_PC</name>
        <label>Get PC</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Info_Screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Concern__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_WC.Concern__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ProductConsumed</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_WC</name>
        <label>Get WC</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_WOLI</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Warranty_Claim__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_WOLI</name>
        <label>Get WOLI</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_PC</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Concern__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_WC.Concern__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Canceled</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>WorkOrderLineItem</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Info_Screen</name>
        <label>Info Screen</label>
        <locationX>176</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>Info_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Info_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Concern</name>
                    <extensionName>flowruntime:lookup</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>fieldApiName</name>
                        <value>
                            <stringValue>Concern__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Concern</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>objectApiName</name>
                        <value>
                            <stringValue>Warranty_Claim__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>disabled</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>recordId</name>
                        <value>
                            <elementReference>Get_WC.Concern__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>Vehicle</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Asset__r.Name</elementReference>
                    </defaultValue>
                    <fieldText>Vehicle</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Recorded_ODO</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Work_Order__r.ODO_In__c</elementReference>
                    </defaultValue>
                    <fieldText>Recorded ODO</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                    <scale>2</scale>
                </fields>
                <fields>
                    <name>WO_Transaction_Date</name>
                    <dataType>Date</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Work_Order__r.CreatedDate</elementReference>
                    </defaultValue>
                    <fieldText>WO Transaction Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Repair_Country</name>
                    <choiceReferences>India_choice</choiceReferences>
                    <dataType>String</dataType>
                    <defaultSelectedChoiceReference>India_choice</defaultSelectedChoiceReference>
                    <fieldText>Repair Country</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Category</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Category__c</elementReference>
                    </defaultValue>
                    <fieldText>Category</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Type</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Type__c</elementReference>
                    </defaultValue>
                    <fieldText>Type</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Main_Work_Type</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Main_Work_Type__r.Work_Type_Name__c</elementReference>
                    </defaultValue>
                    <fieldText>Main Work Type</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Work_Type_Description</name>
                    <defaultValue>
                        <stringValue>{!Get_WC.Main_Work_Type__r.WorkType.Description}</stringValue>
                    </defaultValue>
                    <fieldText>Work Type Description</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Diagnostic_Trouble_code_1</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Diagnostic_Trouble_Code_1__c</elementReference>
                    </defaultValue>
                    <fieldText>Diagnostic Trouble Code 1</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Diagnostic_Trouble_Code_3</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Diagnostic_Trouble_Code_3__c</elementReference>
                    </defaultValue>
                    <fieldText>Diagnostic Trouble Code 3</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Causal_Part_Code</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Causal_Part_Code__r.Name</elementReference>
                    </defaultValue>
                    <fieldText>Causal Part Code</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Info_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>RO</name>
                    <extensionName>flowruntime:lookup</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>fieldApiName</name>
                        <value>
                            <stringValue>Work_Order__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Work Order</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>objectApiName</name>
                        <value>
                            <stringValue>Warranty_Claim__c</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>recordId</name>
                        <value>
                            <elementReference>Get_WC.Work_Order__c</elementReference>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>maxValues</name>
                        <value>
                            <numberValue>1.0</numberValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>disabled</name>
                        <value>
                            <booleanValue>true</booleanValue>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>Model</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Asset__r.Model__c</elementReference>
                    </defaultValue>
                    <fieldText>Model</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Warranty_Start</name>
                    <dataType>Date</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Asset__r.Warranty_Start_Date__c</elementReference>
                    </defaultValue>
                    <fieldText>Warranty Start</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Warranty_End</name>
                    <dataType>Date</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Asset__r.Warranty_End_Date__c</elementReference>
                    </defaultValue>
                    <fieldText>Warranty End</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Warranty_Mileage</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Asset__r.Warranty_Mileage__c</elementReference>
                    </defaultValue>
                    <fieldText>Warranty Mileage</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Claim_To</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Claim_to__c</elementReference>
                    </defaultValue>
                    <fieldText>Claim To</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Product_Service_Campaign</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Product_Service_Campaign_Item__r.ProductServiceCampaign.ProductServiceCampaignName</elementReference>
                    </defaultValue>
                    <fieldText>Product Service Campaign</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Main_Part</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Main_Part__r.ProductName</elementReference>
                    </defaultValue>
                    <fieldText>Main Part</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Part_Description</name>
                    <defaultValue>
                        <stringValue>{!Get_WC.Main_Part__r.Part_Description__c}</stringValue>
                    </defaultValue>
                    <fieldText>Part Description</fieldText>
                    <fieldType>LargeTextArea</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Diagnostic_Trouble_Code_2</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Diagnostic_Trouble_Code_2__c</elementReference>
                    </defaultValue>
                    <fieldText>Diagnostic Trouble Code 2</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Diagnostic_Trouble_Code_4</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Diagnostic_Trouble_Code_4__c</elementReference>
                    </defaultValue>
                    <fieldText>Diagnostic Trouble Code 4</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Info_Screen_Section2</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Info_Screen_Section2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Causal_Part_Serial_Number</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Causal_Part_Serial_Number__c</elementReference>
                    </defaultValue>
                    <fieldText>Causal Part Serial Number</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Part_Replacement_Date</name>
                    <dataType>Date</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Part_replacement_date__c</elementReference>
                    </defaultValue>
                    <fieldText>Part Replacement Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Info_Screen_Section2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Part_Replacement_ODO</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Part_replacement_ODO__c</elementReference>
                    </defaultValue>
                    <fieldText>Part Replacement ODO</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Replacement_Part_Serial_Number</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Replacement_Part_Serial_Number</elementReference>
                    </defaultValue>
                    <fieldText>Replacement Part Serial Number</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Get_WC.Category__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Spare part warranty</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Issue_Description</name>
            <defaultValue>
                <stringValue>{!Get_WC.Issue_Description__c}</stringValue>
            </defaultValue>
            <fieldText>Issue Description</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Cancel_reason</name>
            <defaultValue>
                <stringValue>{!Get_WC.Cancel_reason__c}</stringValue>
            </defaultValue>
            <fieldText>Cancel Reason</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Get_WC.Status__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Canceled</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>WOLI</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>WorkOrderLineItem</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Work type(s)</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_WOLI</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Work_Type_Name__c&quot;,&quot;guid&quot;:&quot;column-5c58&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Work Type Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Duration&quot;,&quot;guid&quot;:&quot;column-f680&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Duration&quot;,&quot;type&quot;:&quot;number&quot;},{&quot;apiName&quot;:&quot;Labor_Rate__c&quot;,&quot;guid&quot;:&quot;column-d173&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Labor Rate&quot;,&quot;type&quot;:&quot;number&quot;},{&quot;apiName&quot;:&quot;Total_Tax_Percent__c&quot;,&quot;guid&quot;:&quot;column-3cc1&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;% Tax&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Total Tax (%)&quot;,&quot;type&quot;:&quot;customPercent&quot;},{&quot;apiName&quot;:&quot;Total_Tax_Amount__c&quot;,&quot;guid&quot;:&quot;column-22f0&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Tax Amount&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Total Tax Amount&quot;,&quot;type&quot;:&quot;currency&quot;},{&quot;apiName&quot;:&quot;Total_Labor_Amount__c&quot;,&quot;guid&quot;:&quot;column-484d&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Total Amount&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;Total Labor Amount&quot;,&quot;type&quot;:&quot;currency&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>PC</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>ProductConsumed</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Consumed Part(s)</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_PC</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;ProductName&quot;,&quot;guid&quot;:&quot;column-a40c&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Part&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Product Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Part_Description__c&quot;,&quot;guid&quot;:&quot;column-407f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Description&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Part Description&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;QuantityConsumed&quot;,&quot;guid&quot;:&quot;column-457d&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Quantity&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Quantity Consumed&quot;,&quot;type&quot;:&quot;number&quot;},{&quot;apiName&quot;:&quot;UnitPrice&quot;,&quot;guid&quot;:&quot;column-fe58&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Unit Price&quot;,&quot;type&quot;:&quot;currency&quot;},{&quot;apiName&quot;:&quot;Total_Tax_Percent__c&quot;,&quot;guid&quot;:&quot;column-53c0&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;% Tax&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Total Tax (%)&quot;,&quot;type&quot;:&quot;customPercent&quot;},{&quot;apiName&quot;:&quot;Total_Tax__c&quot;,&quot;guid&quot;:&quot;column-38fe&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Tax Amount&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;Total Tax&quot;,&quot;type&quot;:&quot;currency&quot;},{&quot;apiName&quot;:&quot;Total_Part_Amount__c&quot;,&quot;guid&quot;:&quot;column-1e41&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Total Amount&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:6,&quot;label&quot;:&quot;Total Part Amount&quot;,&quot;type&quot;:&quot;currency&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Summary</name>
            <fieldText>Summary</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Summary_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Total_service</name>
                    <dataType>Currency</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Total_Labor_Base_Amount__c</elementReference>
                    </defaultValue>
                    <fieldText>Total Services</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                    <scale>2</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Summary_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Total_Part</name>
                    <dataType>Currency</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Total_Part_Base_Amount__c</elementReference>
                    </defaultValue>
                    <fieldText>Total Parts</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                    <scale>2</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Summary_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Total_Taxes</name>
                    <dataType>Currency</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Total_Tax_Amount__c</elementReference>
                    </defaultValue>
                    <fieldText>Total Taxes</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                    <scale>2</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Summary_Column4</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Grand_Total</name>
                    <dataType>Currency</dataType>
                    <defaultValue>
                        <elementReference>Get_WC.Concern__r.Grand_Total__c</elementReference>
                    </defaultValue>
                    <fieldText>Grand Total</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                    <scale>2</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_WC</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
