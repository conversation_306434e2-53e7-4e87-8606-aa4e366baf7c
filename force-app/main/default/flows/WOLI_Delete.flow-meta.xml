<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <name>RO_Status_not_valid</name>
        <label>RO Status not valid</label>
        <locationX>50</locationX>
        <locationY>395</locationY>
        <connector>
            <targetReference>Is_WOLI_cancelled</targetReference>
        </connector>
        <customErrorMessages>
            <errorMessage>You cannot delete a Work Order Line Item due to Work Order status ({!$Record.WorkOrder.Status})</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>WOLI_status_not_valid</name>
        <label>WOLI status not valid</label>
        <locationX>50</locationX>
        <locationY>695</locationY>
        <customErrorMessages>
            <errorMessage>You need to cancel the Work Order Line Item in order to delete it.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Is_RO_status_valid</name>
        <label>Is RO status valid?</label>
        <locationX>182</locationX>
        <locationY>287</locationY>
        <defaultConnector>
            <targetReference>Is_WOLI_cancelled</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RO_not_valid</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.WorkOrder.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Invoicing</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WorkOrder.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Paid</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WorkOrder.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Invoicing</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.WorkOrder.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Canceled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RO_Status_not_valid</targetReference>
            </connector>
            <label>RO not valid</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_WOLI_cancelled</name>
        <label>Is WOLI cancelled?</label>
        <locationX>182</locationX>
        <locationY>587</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_yet_cancelled</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Canceled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>WOLI_status_not_valid</targetReference>
            </connector>
            <label>Not yet cancelled</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>WOLI - Delete {!$Flow.CurrentDateTime}</interviewLabel>
    <label>WOLI - Delete</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_RO_status_valid</targetReference>
        </connector>
        <object>WorkOrderLineItem</object>
        <recordTriggerType>Delete</recordTriggerType>
        <triggerType>RecordBeforeDelete</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>currentItem_Received_PCs</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>ProductConsumed</objectType>
    </variables>
</Flow>
