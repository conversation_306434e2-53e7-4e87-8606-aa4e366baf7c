<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_value</name>
        <label>Assign value</label>
        <locationX>264</locationX>
        <locationY>468</locationY>
        <assignmentItems>
            <assignToReference>Update_Status_toCancel.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Update_Status_toCancel.Test_Drive_Cancellation_Reason__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Customer No-Show</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ToBeUpdated</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Update_Status_toCancel</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Status_toCancel</targetReference>
        </connector>
    </assignments>
    <description>IDMS-18: Process No show Test drive</description>
    <environments>Default</environments>
    <interviewLabel>Test Drive - No show {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Test Drive - No show</label>
    <loops>
        <name>Update_Status_toCancel</name>
        <label>Update Status to Cancel</label>
        <locationX>176</locationX>
        <locationY>360</locationY>
        <collectionReference>Get_Out_Date_Test_Drives</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_value</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Test_drives</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Out_Date_Test_Drives</name>
        <label>Get Out Date Test Drives</label>
        <locationX>176</locationX>
        <locationY>252</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Status_toCancel</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Confirmed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThan</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Test_Drive__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Status__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Test_drives</name>
        <label>Update Test drives</label>
        <locationX>176</locationX>
        <locationY>660</locationY>
        <inputReference>ToBeUpdated</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Out_Date_Test_Drives</targetReference>
        </connector>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2025-03-03</startDate>
            <startTime>00:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>ToBeUpdated</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Test_Drive__c</objectType>
    </variables>
</Flow>
