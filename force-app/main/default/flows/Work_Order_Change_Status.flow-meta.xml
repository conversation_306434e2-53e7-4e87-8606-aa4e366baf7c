<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_1</name>
        <label>Assign 1</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>Get_RO.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status1</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Additional_Screen_For</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_2</name>
        <label>Assign 2</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>Get_RO.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status2</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Additional_Screen_For</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_3</name>
        <label>Assign 3</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>Get_RO.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status3</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Additional_Screen_For</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_4</name>
        <label>Assign 4</label>
        <locationX>842</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>Get_RO.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status4</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Additional_Screen_For</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_5</name>
        <label>Assign 5</label>
        <locationX>1106</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>Get_RO.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status5</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Additional_Screen_For</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Cancel_Reason</name>
        <label>Assign Cancel Reason</label>
        <locationX>94</locationX>
        <locationY>974</locationY>
        <assignmentItems>
            <assignToReference>Get_RO.Cancel_Reason__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Reason_of_cancelation</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_WO</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Canceled_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;Canceled&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Canceled</stringValue>
        </value>
    </choices>
    <choices>
        <name>Closed_Choice</name>
        <choiceText>Closed</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Closed</stringValue>
        </value>
    </choices>
    <choices>
        <name>In_Progress_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;In Progress&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>In Progress</stringValue>
        </value>
    </choices>
    <choices>
        <name>Invoicing_Choice</name>
        <choiceText>Invoicing</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Invoicing</stringValue>
        </value>
    </choices>
    <choices>
        <name>Paid_Choice</name>
        <choiceText>Paid</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Paid</stringValue>
        </value>
    </choices>
    <choices>
        <name>Quotation_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;Quotation&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Quotation</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Additional_Screen_For</name>
        <label>Additional Screen For?</label>
        <locationX>710</locationX>
        <locationY>758</locationY>
        <defaultConnector>
            <targetReference>Update_WO</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Canceled</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Canceled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Canceled_Screen</targetReference>
            </connector>
            <label>Canceled</label>
        </rules>
        <rules>
            <name>Invoicing2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Invoicing</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_ROLIs</targetReference>
            </connector>
            <label>Invoicing</label>
        </rules>
        <rules>
            <name>Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Close_Initial_SA</targetReference>
            </connector>
            <label>Closed</label>
        </rules>
        <rules>
            <name>In_Progress2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In Progress</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Start_Initial_SA</targetReference>
            </connector>
            <label>In Progress</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_permission</name>
        <label>Check permission?</label>
        <locationX>1304</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Not_Allowed_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Service_Advisor</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Permission.Service_Advisor</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_RO</targetReference>
            </connector>
            <label>Service Advisor</label>
        </rules>
    </decisions>
    <decisions>
        <name>Current_RO_status</name>
        <label>Current RO status</label>
        <locationX>710</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>End_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Closed, Canceled, other...</defaultConnectorLabel>
        <rules>
            <name>Open</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Open</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Status_1</targetReference>
            </connector>
            <label>Open</label>
        </rules>
        <rules>
            <name>Quotation</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Quotation</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Status_2</targetReference>
            </connector>
            <label>Quotation</label>
        </rules>
        <rules>
            <name>In_Progress</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In Progress</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Status_3</targetReference>
            </connector>
            <label>In Progress</label>
        </rules>
        <rules>
            <name>Invoicing</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Invoicing</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Status_4</targetReference>
            </connector>
            <label>Invoicing</label>
        </rules>
        <rules>
            <name>Paid</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_RO.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Paid</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Status_5</targetReference>
            </connector>
            <label>Paid</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_All_ROLIs_Closed</name>
        <label>Is All ROLIs Closed?</label>
        <locationX>490</locationX>
        <locationY>974</locationY>
        <defaultConnector>
            <targetReference>Update_WO</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Nope</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ROLIs</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Still_In_progress_Screen</targetReference>
            </connector>
            <label>Nope</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>WO - {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Work Order - Change Status</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_RO</name>
        <label>Get RO</label>
        <locationX>710</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Current_RO_status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>WorkOrder</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ROLIs</name>
        <label>Get ROLIs</label>
        <locationX>490</locationX>
        <locationY>866</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_All_ROLIs_Closed</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WorkOrderId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Canceled</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>WorkOrderLineItem</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Close_Initial_SA</name>
        <label>Close Initial SA</label>
        <locationX>798</locationX>
        <locationY>866</locationY>
        <connector>
            <targetReference>Update_WO</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Work_Order__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>ActualEndTime</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <object>ServiceAppointment</object>
    </recordUpdates>
    <recordUpdates>
        <name>Start_Initial_SA</name>
        <label>Start Initial SA</label>
        <locationX>1062</locationX>
        <locationY>866</locationY>
        <connector>
            <targetReference>Update_WO</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Work_Order__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>In Progress</stringValue>
            </value>
        </inputAssignments>
        <object>ServiceAppointment</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_WO</name>
        <label>Update WO</label>
        <locationX>710</locationX>
        <locationY>1466</locationY>
        <faultConnector>
            <targetReference>Exception_Screen</targetReference>
        </faultConnector>
        <inputReference>Get_RO</inputReference>
    </recordUpdates>
    <screens>
        <name>Canceled_Screen</name>
        <label>Canceled Screen</label>
        <locationX>94</locationX>
        <locationY>866</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_Cancel_Reason</targetReference>
        </connector>
        <fields>
            <name>Reason_of_cancelation</name>
            <dataType>String</dataType>
            <fieldText>Reason of Cancelation</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>End_Screen</name>
        <label>End Screen</label>
        <locationX>1370</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>EndMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;No further action. &lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Exception_Screen</name>
        <label>Exception Screen</label>
        <locationX>1634</locationX>
        <locationY>1574</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ExceptionMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;Something went wrong. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Not_Allowed_Screen</name>
        <label>Not Allowed Screen</label>
        <locationX>1898</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NotAllowedMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;You are not allowed to change status. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Status_1</name>
        <label>Status 1</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_1</targetReference>
        </connector>
        <fields>
            <name>Status1</name>
            <choiceReferences>Quotation_Choice</choiceReferences>
            <choiceReferences>Canceled_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Quotation_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Status_2</name>
        <label>Status 2</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_2</targetReference>
        </connector>
        <fields>
            <name>Status2</name>
            <choiceReferences>In_Progress_Choice</choiceReferences>
            <choiceReferences>Canceled_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>In_Progress_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Status_3</name>
        <label>Status 3</label>
        <locationX>578</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_3</targetReference>
        </connector>
        <fields>
            <name>Status3</name>
            <choiceReferences>Invoicing_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Invoicing_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Status_4</name>
        <label>Status 4</label>
        <locationX>842</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_4</targetReference>
        </connector>
        <fields>
            <name>Status4</name>
            <choiceReferences>Paid_Choice</choiceReferences>
            <choiceReferences>In_Progress_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Paid_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Status_5</name>
        <label>Status 5</label>
        <locationX>1106</locationX>
        <locationY>458</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_5</targetReference>
        </connector>
        <fields>
            <name>Status5</name>
            <choiceReferences>Closed_Choice</choiceReferences>
            <choiceReferences>Invoicing_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Closed_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Still_In_progress_Screen</name>
        <label>Still In-progress Screen</label>
        <locationX>358</locationX>
        <locationY>1082</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>InProgressMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Please close all Work Order Line Items to move to &apos;Invoicing&apos;. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>1178</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_permission</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>NewStatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
