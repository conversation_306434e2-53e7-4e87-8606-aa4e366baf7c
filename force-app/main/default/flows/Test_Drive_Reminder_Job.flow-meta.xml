<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Set_SMS_info</name>
        <label>Set SMS info</label>
        <locationX>396</locationX>
        <locationY>468</locationY>
        <assignmentItems>
            <assignToReference>SMS_receiver</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Cur_receiver</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SMS_content</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Cur_content</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Send_SMS_API_Flow</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_TobeUpdate</name>
        <label>Set TobeUpdate</label>
        <locationX>264</locationX>
        <locationY>792</locationY>
        <assignmentItems>
            <assignToReference>Record.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_TD.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Record.Reminder_Sent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>TobeUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Record</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_TD</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Sent</name>
        <label>Sent</label>
        <locationX>396</locationX>
        <locationY>684</locationY>
        <defaultConnector>
            <targetReference>Loop_TD</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Success</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Send_SMS_API_Flow.return_code</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>200</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Send_SMS_API_Flow.return_description</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Request accepted</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_TobeUpdate</targetReference>
            </connector>
            <label>Success</label>
        </rules>
    </decisions>
    <description>IDMS-18: Initial Version. Send Reminder SMS</description>
    <environments>Default</environments>
    <formulas>
        <name>Cur_content</name>
        <dataType>String</dataType>
        <expression>&apos;TBD&apos;</expression>
    </formulas>
    <formulas>
        <name>Cur_receiver</name>
        <dataType>String</dataType>
        <expression>&apos;+91&apos; + {!Loop_TD.Mobile_Number__c}</expression>
    </formulas>
    <formulas>
        <name>Tomorrow_End</name>
        <dataType>DateTime</dataType>
        <expression>{!Tomorrow_Start} + (24/24)</expression>
    </formulas>
    <formulas>
        <name>Tomorrow_Start</name>
        <dataType>DateTime</dataType>
        <expression>DATETIMEVALUE(TODAY()+1)</expression>
    </formulas>
    <interviewLabel>Test Drive Reminder Job {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Test Drive Reminder Job</label>
    <loops>
        <name>Loop_TD</name>
        <label>Loop TD</label>
        <locationX>176</locationX>
        <locationY>360</locationY>
        <collectionReference>Get_Tomorrow_TD</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Set_SMS_info</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_TobeUpdate</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Tomorrow_TD</name>
        <label>Get Tomorrow TD</label>
        <locationX>176</locationX>
        <locationY>252</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_TD</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>GreaterThanOrEqualTo</operator>
            <value>
                <elementReference>Tomorrow_Start</elementReference>
            </value>
        </filters>
        <filters>
            <field>Confirmed_Start__c</field>
            <operator>LessThan</operator>
            <value>
                <elementReference>Tomorrow_End</elementReference>
            </value>
        </filters>
        <filters>
            <field>Reminder_Sent__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Confirmed</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Test_Drive__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_TobeUpdate</name>
        <label>Update TobeUpdate</label>
        <locationX>176</locationX>
        <locationY>1068</locationY>
        <inputReference>TobeUpdate</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Tomorrow_TD</targetReference>
        </connector>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2025-02-27</startDate>
            <startTime>07:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Send_SMS_API_Flow</name>
        <label>Send SMS API Flow</label>
        <locationX>396</locationX>
        <locationY>576</locationY>
        <connector>
            <targetReference>Sent</targetReference>
        </connector>
        <flowName>Send_SMS_API</flowName>
        <inputAssignments>
            <name>content</name>
            <value>
                <elementReference>SMS_content</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>receiver</name>
            <value>
                <elementReference>SMS_receiver</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>templateID</name>
            <value>
                <stringValue>123123</stringValue>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>Record</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Test_Drive__c</objectType>
    </variables>
    <variables>
        <name>SMS_content</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>SMS_receiver</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>TobeUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Test_Drive__c</objectType>
    </variables>
</Flow>
