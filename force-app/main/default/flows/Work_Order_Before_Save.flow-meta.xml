<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <name>NoLaborRate</name>
        <label>NoLaborRate</label>
        <locationX>50</locationX>
        <locationY>827</locationY>
        <customErrorMessages>
            <errorMessage>The service center has no labor rate. Please contact the administrator to complete the dealer setup.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Action</name>
        <label>Action?</label>
        <locationX>611</locationX>
        <locationY>395</locationY>
        <defaultConnectorLabel>Update</defaultConnectorLabel>
        <rules>
            <name>Insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsNew</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Is_Labor_Rate</targetReference>
            </connector>
            <label>Insert</label>
        </rules>
    </decisions>
    <decisions>
        <name>Global_Switch</name>
        <label>Global Switch</label>
        <locationX>858</locationX>
        <locationY>287</locationY>
        <defaultConnectorLabel>Off</defaultConnectorLabel>
        <rules>
            <name>On</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.SYS_GlobalOnOffSwitch__c.SYS_IsFlowActive__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Action</targetReference>
            </connector>
            <label>On</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Labor_Rate</name>
        <label>Is Labor Rate?</label>
        <locationX>380</locationX>
        <locationY>503</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_Labor_Rate</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Warranty_Flat_Rate__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Customer_Pay_Flat_Rate__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Labor_Rate</targetReference>
            </connector>
            <label>No Labor Rate</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Labor_Rate_available</name>
        <label>Is Labor Rate available?</label>
        <locationX>182</locationX>
        <locationY>719</locationY>
        <defaultConnector>
            <targetReference>Update_Labor_Rate</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_yet</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Labor_Rate</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>NoLaborRate</targetReference>
            </connector>
            <label>Not yet</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>IsNew</name>
        <dataType>Boolean</dataType>
        <expression>IsNew()</expression>
    </formulas>
    <formulas>
        <name>NOW</name>
        <dataType>DateTime</dataType>
        <expression>NOW()</expression>
    </formulas>
    <interviewLabel>Work Order - Before Save {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Work Order - Before Save</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Labor_Rate</name>
        <label>Get Labor Rate</label>
        <locationX>182</locationX>
        <locationY>611</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Labor_Rate_available</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Asset.RecordType.Name</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Labor_Rate__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Labor_Rate</name>
        <label>Update Labor Rate</label>
        <locationX>314</locationX>
        <locationY>827</locationY>
        <inputAssignments>
            <field>Customer_Pay_Flat_Rate__c</field>
            <value>
                <elementReference>Get_Labor_Rate.Customer_Paid_Flat_Rate__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Warranty_Flat_Rate__c</field>
            <value>
                <elementReference>Get_Labor_Rate.Warranty_Flat_Rate__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>732</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Global_Switch</targetReference>
        </connector>
        <object>WorkOrder</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
