<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <name>Cannot_delete</name>
        <label>Cannot delete</label>
        <locationX>50</locationX>
        <locationY>503</locationY>
        <customErrorMessages>
            <errorMessage>This Warranty Plan cannot be deleted because it is linked to one or more sold Vehicle Assets.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Linked</name>
        <label>Linked</label>
        <locationX>182</locationX>
        <locationY>395</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_related_VINs</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Cannot_delete</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Warranty Plan - Delete {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Warranty Plan - Delete</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_related_VINs</name>
        <label>Get related VINs</label>
        <locationX>182</locationX>
        <locationY>287</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Linked</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Warranty_Plan__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Asset</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_related_VINs</targetReference>
        </connector>
        <object>Warranty_Plan__c</object>
        <recordTriggerType>Delete</recordTriggerType>
        <triggerType>RecordBeforeDelete</triggerType>
    </start>
    <status>Active</status>
</Flow>
