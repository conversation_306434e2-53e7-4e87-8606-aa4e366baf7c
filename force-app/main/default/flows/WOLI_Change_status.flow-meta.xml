<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Call_SAP</name>
        <label>Call SAP</label>
        <locationX>1370</locationX>
        <locationY>1274</locationY>
        <actionName>PartReserveCallout.Part Reserve</actionName>
        <actionType>externalService</actionType>
        <connector>
            <targetReference>Check_response</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>payload</elementReference>
            </value>
        </inputParameters>
        <nameSegment>PartReserveCallout.Part Reserve</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_In_Progress_Screen</name>
        <label>Assign In Progress Screen</label>
        <locationX>490</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status2</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_new_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_New_Screen</name>
        <label>Assign New Screen</label>
        <locationX>226</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_new_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_On_Hold</name>
        <label>Assign On Hold</label>
        <locationX>754</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status3</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_new_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_payload</name>
        <label>Assign payload</label>
        <locationX>1458</locationX>
        <locationY>1082</locationY>
        <assignmentItems>
            <assignToReference>i.requestQuantity</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.Request_Quantity__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.z0type</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>2.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.dealer</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.WorkOrder.Account.VF_DealerCode__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.isDeleted</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>X</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.itemID</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.SAP_Item_ID__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.orderUnit</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.QuantityUnitOfMeasure</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.partCode</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.PricebookEntry.Product2.ProductCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.repairOrderNumber</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.WorkOrder.WorkOrderNumber</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.repairOrderNumberItem</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.WorkOrderLineItem.LineItemNumber</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.requestID</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>i.reservationID</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_PCs.SAP_Reservation_ID__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>payload</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>i</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_PCs</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assigned_Completed</name>
        <label>Assigned Completed</label>
        <locationX>1018</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status4</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_new_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assigned_QC_Passed</name>
        <label>Assigned QC Passed</label>
        <locationX>1546</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status6</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_new_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assigned_Quality</name>
        <label>Assigned Quality</label>
        <locationX>1282</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Status5</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>BackFromQC</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_new_status</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Description</name>
        <label>Set Description</label>
        <locationX>1722</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Description</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Description</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_WOLI</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_End</name>
        <label>Set End</label>
        <locationX>842</locationX>
        <locationY>974</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.EndDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_related_SA</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_QC</name>
        <label>Set QC</label>
        <locationX>1986</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Quality_Control__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Quality_Control2</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_WOLI</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_QC_failure</name>
        <label>Set QC failure</label>
        <locationX>314</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.Reason_of_QC_failures__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Reason_of_Quality_Checking_failures</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_WOLI</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Start</name>
        <label>Set Start</label>
        <locationX>50</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>Get_WOLI.StartDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WOLI.Technician_1__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Technician_1</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WOLI.Technician_2__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Technician_2</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WOLI.Technician_3__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Technician_3</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_WOLI.Quality_Control__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Quality_Control</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_WOLI</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Cancel_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;Canceled&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Canceled</stringValue>
        </value>
    </choices>
    <choices>
        <name>Closed_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;Closed&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Closed</stringValue>
        </value>
    </choices>
    <choices>
        <name>Completed_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(227, 243, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;Completed&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Completed</stringValue>
        </value>
    </choices>
    <choices>
        <name>InProgress_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;In Progress&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>In Progress</stringValue>
        </value>
    </choices>
    <choices>
        <name>OnHold_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;On Hold&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>On Hold</stringValue>
        </value>
    </choices>
    <choices>
        <name>QC_Passed_Choice</name>
        <choiceText>QC Passed</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>QC Passed</stringValue>
        </value>
    </choices>
    <choices>
        <name>QualityChecking_Choice</name>
        <choiceText>&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 11.36px; font-family: Arial, Helvetica, sans-serif; color: rgb(0, 0, 0);&quot;&gt;Quality Checking&lt;/span&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Quality Checking</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Check_new_status</name>
        <label>Check new status</label>
        <locationX>1150</locationX>
        <locationY>650</locationY>
        <defaultConnector>
            <targetReference>Update_WOLI</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>In_progress</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In Progress</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_WOLI.StartDate</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>BackFromQC</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_Technicians_Screen</targetReference>
            </connector>
            <label>In-progress</label>
        </rules>
        <rules>
            <name>In_Progress_From_QC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In Progress</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>BackFromQC</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>QC_Failed_Reason</targetReference>
            </connector>
            <label>In-Progress (From QC)</label>
        </rules>
        <rules>
            <name>Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.EndDate</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Incomplete_PCs</targetReference>
            </connector>
            <label>Closed</label>
        </rules>
        <rules>
            <name>Cancelled</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Canceled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_reserved_PCs</targetReference>
            </connector>
            <label>Cancelled</label>
        </rules>
        <rules>
            <name>On_hold</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>On Hold</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>OnHoldScreen</targetReference>
            </connector>
            <label>On-hold</label>
        </rules>
        <rules>
            <name>QC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Quality Checking</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>QC_Screen</targetReference>
            </connector>
            <label>QC</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_response</name>
        <label>Check response?</label>
        <locationX>1370</locationX>
        <locationY>1382</locationY>
        <defaultConnector>
            <targetReference>Failed_SAP_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Success</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Call_SAP.2XX.result.status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>S</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Canceled_related_SA</targetReference>
            </connector>
            <label>Success</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Status</name>
        <label>Check Status</label>
        <locationX>1150</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Check_new_status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>From_New</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>New_Screen</targetReference>
            </connector>
            <label>From New</label>
        </rules>
        <rules>
            <name>From_In_Progress</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In Progress</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>In_Progress_Screen</targetReference>
            </connector>
            <label>From In-Progress</label>
        </rules>
        <rules>
            <name>From_On_hold</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>On Hold</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>On_Hold_Screen</targetReference>
            </connector>
            <label>From On-hold</label>
        </rules>
        <rules>
            <name>Completed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Completed_Screen</targetReference>
            </connector>
            <label>Completed</label>
        </rules>
        <rules>
            <name>Quality_Checking</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Quality Checking</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Quality_Screen</targetReference>
            </connector>
            <label>Quality Checking</label>
        </rules>
        <rules>
            <name>QC_Passed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>QC Passed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>QC_Passed_Screen</targetReference>
            </connector>
            <label>QC Passed</label>
        </rules>
        <rules>
            <name>Closed_Cancel</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_WOLI.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Canceled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ClosedScreen</targetReference>
            </connector>
            <label>Closed/ Cancel</label>
        </rules>
    </decisions>
    <decisions>
        <name>Has_PCs</name>
        <label>Has PCs?</label>
        <locationX>1238</locationX>
        <locationY>866</locationY>
        <defaultConnector>
            <targetReference>Loop_PCs</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_PCs</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_reserved_PCs</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Canceled_related_SA</targetReference>
            </connector>
            <label>No PCs</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Part_remaining</name>
        <label>Is Part remaining?</label>
        <locationX>710</locationX>
        <locationY>866</locationY>
        <defaultConnector>
            <targetReference>Set_End</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_remaining</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Incomplete_PCs</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Remaining_Parts_Screen</targetReference>
            </connector>
            <label>Yes remaining</label>
        </rules>
    </decisions>
    <dynamicChoiceSets>
        <name>DealerUserList</name>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_WOLI.WorkOrder.AccountId</elementReference>
            </value>
        </filters>
        <object>User</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>WOLI - Change status {!$Flow.CurrentDateTime}</interviewLabel>
    <label>WOLI - Change status</label>
    <loops>
        <name>Loop_PCs</name>
        <label>Loop PCs</label>
        <locationX>1370</locationX>
        <locationY>974</locationY>
        <collectionReference>Get_reserved_PCs</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_payload</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Call_SAP</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Incomplete_PCs</name>
        <label>Get Incomplete PCs</label>
        <locationX>710</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Part_remaining</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>To_be_returned__c</field>
            <operator>GreaterThan</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </filters>
        <filters>
            <field>WorkOrderLineItemId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ProductConsumed</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_reserved_PCs</name>
        <label>Get reserved PCs</label>
        <locationX>1238</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Has_PCs</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>WorkOrderLineItemId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Reserved_Quantity__c</field>
            <operator>GreaterThan</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ProductConsumed</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_WOLI</name>
        <label>Get WOLI</label>
        <locationX>1150</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Status</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>WorkOrderLineItem</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Canceled_related_SA</name>
        <label>Canceled related SA</label>
        <locationX>1238</locationX>
        <locationY>1874</locationY>
        <connector>
            <targetReference>Update_WOLI</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ParentRecordId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Canceled</stringValue>
            </value>
        </inputAssignments>
        <object>ServiceAppointment</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_related_SA</name>
        <label>Update related SA</label>
        <locationX>842</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>Update_WOLI</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ParentRecordId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>ActualEndTime</field>
            <value>
                <elementReference>Get_WOLI.EndDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ActualStartTime</field>
            <value>
                <elementReference>Get_WOLI.StartDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Completed</stringValue>
            </value>
        </inputAssignments>
        <object>ServiceAppointment</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_WOLI</name>
        <label>Update WOLI</label>
        <locationX>1150</locationX>
        <locationY>2066</locationY>
        <faultConnector>
            <targetReference>Exception_Screen</targetReference>
        </faultConnector>
        <inputReference>Get_WOLI</inputReference>
    </recordUpdates>
    <screens>
        <name>Add_Technicians_Screen</name>
        <label>Add Technicians Screen</label>
        <locationX>50</locationX>
        <locationY>758</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Set_Start</targetReference>
        </connector>
        <fields>
            <name>Add_Technicians_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Add_Technicians_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Technician_1</name>
                    <choiceReferences>DealerUserList</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WOLI.Technician_1__c</elementReference>
                    </defaultValue>
                    <fieldText>Technician 1</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <fields>
                    <name>Technician_3</name>
                    <choiceReferences>DealerUserList</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WOLI.Technician_3__c</elementReference>
                    </defaultValue>
                    <fieldText>Technician 3</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Add_Technicians_Screen_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Technician_2</name>
                    <choiceReferences>DealerUserList</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WOLI.Technician_2__c</elementReference>
                    </defaultValue>
                    <fieldText>Technician 2</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Quality_Control</name>
                    <choiceReferences>DealerUserList</choiceReferences>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_WOLI.Quality_Control__c</elementReference>
                    </defaultValue>
                    <fieldText>Quality Control</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ClosedScreen</name>
        <label>Closed Screen</label>
        <locationX>1810</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Check_new_status</targetReference>
        </connector>
        <fields>
            <name>NoStatusMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;No further steps for this Work Order Line Item. &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Completed_Screen</name>
        <label>Completed Screen</label>
        <locationX>1018</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assigned_Completed</targetReference>
        </connector>
        <fields>
            <name>Status4</name>
            <choiceReferences>QualityChecking_Choice</choiceReferences>
            <choiceReferences>Cancel_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>QualityChecking_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Exception_Screen</name>
        <label>Exception Screen</label>
        <locationX>2426</locationX>
        <locationY>2174</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ExceptionMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Cannot update the Work Order Line Item. &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;{!$Flow.FaultMessage}&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Failed_SAP_Screen</name>
        <label>Failed SAP Screen</label>
        <locationX>1458</locationX>
        <locationY>1490</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FailedSAPMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;{!Call_SAP.2XX.result.message}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>In_Progress_Screen</name>
        <label>In-Progress Screen</label>
        <locationX>490</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_In_Progress_Screen</targetReference>
        </connector>
        <fields>
            <name>Status2</name>
            <choiceReferences>OnHold_Choice</choiceReferences>
            <choiceReferences>Cancel_Choice</choiceReferences>
            <choiceReferences>Completed_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Completed_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>New_Screen</name>
        <label>New Screen</label>
        <locationX>226</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_New_Screen</targetReference>
        </connector>
        <fields>
            <name>Status</name>
            <choiceReferences>InProgress_Choice</choiceReferences>
            <choiceReferences>Cancel_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>InProgress_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>false</booleanValue>
            </isDisabled>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>On_Hold_Screen</name>
        <label>On Hold Screen</label>
        <locationX>754</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_On_Hold</targetReference>
        </connector>
        <fields>
            <name>Status3</name>
            <choiceReferences>InProgress_Choice</choiceReferences>
            <choiceReferences>Cancel_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>InProgress_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>OnHoldScreen</name>
        <label>On-Hold Screen</label>
        <locationX>1722</locationX>
        <locationY>758</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Set_Description</targetReference>
        </connector>
        <fields>
            <name>OnHoldStatus</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>OnHold_Choice</elementReference>
            </defaultValue>
            <fieldText>Status</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Description</name>
            <defaultValue>
                <stringValue>{!Get_WOLI.Description}</stringValue>
            </defaultValue>
            <fieldText>Description</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>QC_Failed_Reason</name>
        <label>QC Failed Reason</label>
        <locationX>314</locationX>
        <locationY>758</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Set_QC_failure</targetReference>
        </connector>
        <fields>
            <name>Reason_of_Quality_Checking_failures</name>
            <defaultValue>
                <stringValue>{!Get_WOLI.Reason_of_QC_failures__c}</stringValue>
            </defaultValue>
            <fieldText>Reason of Quality Checking failures</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>QC_Passed_Screen</name>
        <label>QC Passed Screen</label>
        <locationX>1546</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assigned_QC_Passed</targetReference>
        </connector>
        <fields>
            <name>Status6</name>
            <choiceReferences>Closed_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>Closed_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>QC_Screen</name>
        <label>QC Screen</label>
        <locationX>1986</locationX>
        <locationY>758</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Set_QC</targetReference>
        </connector>
        <fields>
            <name>StatusQC</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>QualityChecking_Choice</elementReference>
            </defaultValue>
            <fieldText>Status</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Quality_Control2</name>
            <choiceReferences>DealerUserList</choiceReferences>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>Get_WOLI.Quality_Control__c</elementReference>
            </defaultValue>
            <fieldText>Quality Control</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Quality_Screen</name>
        <label>Quality Screen</label>
        <locationX>1282</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assigned_Quality</targetReference>
        </connector>
        <fields>
            <name>Status5</name>
            <choiceReferences>InProgress_Choice</choiceReferences>
            <choiceReferences>QC_Passed_Choice</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>QC_Passed_Choice</defaultSelectedChoiceReference>
            <fieldText>Status</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Remaining_Parts_Screen</name>
        <label>Remaining Parts Screen</label>
        <locationX>578</locationX>
        <locationY>974</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>RemainingPartDataTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>ProductConsumed</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Remaining Part(s)</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Incomplete_PCs</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;ProductName&quot;,&quot;guid&quot;:&quot;column-7c97&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Part&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Product Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Part_Description__c&quot;,&quot;guid&quot;:&quot;column-115c&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Part Description&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Received_Quantity__c&quot;,&quot;guid&quot;:&quot;column-c369&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Received Quantity&quot;,&quot;type&quot;:&quot;number&quot;},{&quot;apiName&quot;:&quot;QuantityConsumed&quot;,&quot;guid&quot;:&quot;column-79b1&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Quantity Consumed&quot;,&quot;type&quot;:&quot;number&quot;},{&quot;apiName&quot;:&quot;To_be_returned__c&quot;,&quot;guid&quot;:&quot;column-f12d&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;To be Returned&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;To be returned&quot;,&quot;type&quot;:&quot;number&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>1024</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_WOLI</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>BackFromQC</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>i</name>
        <apexClass>ExternalService__PartReserveCallout_Partx20Reserve_IN_body</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>NewStatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>payload</name>
        <apexClass>ExternalService__PartReserveCallout_Partx20Reserve_IN_body</apexClass>
        <dataType>Apex</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
