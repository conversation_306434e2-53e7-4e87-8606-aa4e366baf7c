<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <environments>Default</environments>
    <interviewLabel>Work Order - Info Screen {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Work Order - Info Screen</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_RO</name>
        <label>Get RO</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_SA</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>WorkOrder</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_SA</name>
        <label>Get SA</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Repair_Order</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Work_Order__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ServiceAppointment</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>Repair_Order</name>
        <label>Repair Order</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Repair_Order_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Repair_Order_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Contact</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <stringValue>{!Get_RO.Contact.FirstName} {!Get_RO.Contact.LastName}</stringValue>
                    </defaultValue>
                    <fieldText>Contact</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Vehicle</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Name</elementReference>
                    </defaultValue>
                    <fieldText>Vehicle</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Repair_Order_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Phone</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Contact.MobilePhone</elementReference>
                    </defaultValue>
                    <fieldText>Phone</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Plate</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Plate_No__c</elementReference>
                    </defaultValue>
                    <fieldText>Plate</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Repair_Order_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Email</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Contact.Email</elementReference>
                    </defaultValue>
                    <fieldText>Email</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Warranty_Start_Date</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Warranty_Start_Date__c</elementReference>
                    </defaultValue>
                    <fieldText>Warranty Start Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Repair_Order_Section1_Column4</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Owner</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <stringValue>{!Get_RO.Owner:User.FirstName} {!Get_RO.Owner:User.LastName}</stringValue>
                    </defaultValue>
                    <fieldText>Owner</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Warranty_End_Date</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Warranty_End_Date__c</elementReference>
                    </defaultValue>
                    <fieldText>Warranty End Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>3</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Customer_Section</name>
            <fieldText>Customer</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Customer_Section_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Contact2</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <stringValue>{!Get_RO.Contact.FirstName} {!Get_RO.Contact.LastName}</stringValue>
                    </defaultValue>
                    <fieldText>Contact</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Driver_s_Name</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_SA.Driver_s_Name__c</elementReference>
                    </defaultValue>
                    <fieldText>Driver&apos;s Name</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Customer_Section_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Phone2</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Contact.MobilePhone</elementReference>
                    </defaultValue>
                    <fieldText>Phone</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Driver_s_Phone</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_SA.Driver_s_Phone__c</elementReference>
                    </defaultValue>
                    <fieldText>Driver&apos;s Phone</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Customer_Section_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Email2</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Contact.Email</elementReference>
                    </defaultValue>
                    <fieldText>Email</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Driver_s_Email</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_SA.Driver_s_Email__c</elementReference>
                    </defaultValue>
                    <fieldText>Driver&apos;s Email</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Vehicle_Section</name>
            <fieldText>Vehicle</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Vehicle_Section_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Vehicle_VIN</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Name</elementReference>
                    </defaultValue>
                    <fieldText>Vehicle (VIN)</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>ODO_In</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.ODO_In__c</elementReference>
                    </defaultValue>
                    <fieldText>ODO In</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Warranty_Start_Date_2</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Warranty_Start_Date__c</elementReference>
                    </defaultValue>
                    <fieldText>Warranty Start Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Battery_Level_In</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Battery_Level_In__c</elementReference>
                    </defaultValue>
                    <fieldText>Battery Level In</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                    <scale>2</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Vehicle_Section_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Plate_Number</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Plate_No__c</elementReference>
                    </defaultValue>
                    <fieldText>Plate Number</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>ODO_Out</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.ODO_Out__c</elementReference>
                    </defaultValue>
                    <fieldText>ODO Out</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Warranty_End_Date_2</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Warranty_End_Date__c</elementReference>
                    </defaultValue>
                    <fieldText>Warranty End Date</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Battery_Level_Out</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Battery_Level_Out__c</elementReference>
                    </defaultValue>
                    <fieldText>Battery Level Out</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                    <scale>2</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Vehicle_Section_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Model</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Asset.Model__c</elementReference>
                    </defaultValue>
                    <fieldText>Model</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>ODO_Unit</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.ODO_Unit__c</elementReference>
                    </defaultValue>
                    <fieldText>ODO Unit</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Repair_Order_Section</name>
            <fieldText>Repair Order</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Repair_Order_Section_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Work_Order_Number</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.WorkOrderNumber</elementReference>
                    </defaultValue>
                    <fieldText>Work Order Number</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Account</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <stringValue>{!Get_RO.Account.FirstName} {!Get_RO.Account.LastName}</stringValue>
                    </defaultValue>
                    <fieldText>Account</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Schedule_Start</name>
                    <dataType>DateTime</dataType>
                    <defaultValue>
                        <elementReference>Get_SA.SchedStartTime</elementReference>
                    </defaultValue>
                    <fieldText>Schedule Start</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Actual_Start</name>
                    <dataType>DateTime</dataType>
                    <defaultValue>
                        <elementReference>Get_SA.ActualStartTime</elementReference>
                    </defaultValue>
                    <fieldText>Actual Start</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Repair_Order_Section_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Advisor_In_Charge</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <stringValue>{!Get_RO.Owner:User.FirstName} {!Get_RO.Owner:User.LastName}</stringValue>
                    </defaultValue>
                    <fieldText>Advisor In Charge</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Service_Territory</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.ServiceTerritory.Name</elementReference>
                    </defaultValue>
                    <fieldText>Service Territory</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Schedule_End</name>
                    <dataType>DateTime</dataType>
                    <defaultValue>
                        <elementReference>Get_SA.SchedEndTime</elementReference>
                    </defaultValue>
                    <fieldText>Schedule End</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Actual_End</name>
                    <dataType>DateTime</dataType>
                    <defaultValue>
                        <elementReference>Get_SA.ActualEndTime</elementReference>
                    </defaultValue>
                    <fieldText>Actual End</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Repair_Order_Section_Column3</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Wash_preference</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Wash_preference__c</elementReference>
                    </defaultValue>
                    <fieldText>Wash preference</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Work_type</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>Get_RO.Concern_Type_s__c</elementReference>
                    </defaultValue>
                    <fieldText>Work type</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>Actual_Duration</name>
                    <dataType>Number</dataType>
                    <defaultValue>
                        <elementReference>Get_SA.ActualDuration</elementReference>
                    </defaultValue>
                    <fieldText>Actual Duration (Minutes)</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isDisabled>
                        <booleanValue>true</booleanValue>
                    </isDisabled>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                    <scale>0</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>High_voltage_battery_note</name>
            <defaultValue>
                <stringValue>{!Get_RO.High_voltage_battery_note__c}</stringValue>
            </defaultValue>
            <fieldText>High Voltage Battery Note</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>Service_Note</name>
            <defaultValue>
                <stringValue>{!Get_RO.Description}</stringValue>
            </defaultValue>
            <fieldText>Service Note</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>false</isRequired>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_RO</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
