/*****Class Description*******************
* Name           : DealerVehiclePurchaseOrderSubmitSap
* Test Class     : DealerVehiclePurchaseOrderSubmitSapTest
* Purpose        : Synchronize Vechile purchase to SAP
* Version  Author      DateModified     Description
* 1.0      thangnv12    09/04/2025       Init
* *******************************************/
public with sharing class DealerVehiclePurchaseOrderSubmitSap {
    
    @InvocableMethod(label='Dealer Purchase Order - Synchronize Vehicle Purchase to SAP' description='Callout SAP API to Synchronize Vehicle Purchase Order' category='Purchase Order')
    public static List<Output> execute(List<Input> requests) {
        if (requests == null || requests.isEmpty()) return null;
        Set<Id> orderIds = new Set<Id>();
        List<Output> outputList = new List<Output>();
        for (Input ord : requests) {
            Output result = new Output(true, 'Created');
            outputList.add(result);
            orderIds.add(ord.orderId);
        }
        System.debug('orderIds: ' + orderIds);
        DealerVehiclePurchaseOrderSubmitSapBatch batch = new DealerVehiclePurchaseOrderSubmitSapBatch(orderIds);
        Id jobId = Database.executeBatch(batch, 50);
        System.debug(jobId);
        return outputList;
    }
        
    public class Input {
        @InvocableVariable(
            label='Order Id' 
            required = true
            description='Id of the Order'
        )
        public Id orderId;
    }

    public class Output {
        @InvocableVariable(label='Is Success')
        public Boolean success;
        @InvocableVariable(label='Result Message')
        public String message;
        public Output(Boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }
}