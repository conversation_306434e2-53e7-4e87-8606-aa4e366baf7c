@isTest
private class DealerPurchaseOrderSapHelperTest {

    @testSetup
    static void setupTestData() {
        DealerPurchaseOrderTestDataFactory.createTestData();
    }

    @isTest
    static void testBuildPartPayload() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<OrderItem> orderItems = [SELECT Id, Product2Id, VF_TransID__c, PricebookEntryId, Quantity FROM OrderItem LIMIT 1];
            Test.startTest();
            String payload = DealerPurchaseOrderSapHelper.buildPartPayloadGetPrice(orderItems);
            System.assertNotEquals(null, payload);
            System.debug(payload);
            Test.stopTest();
        }
    }

    @isTest
    static void testBuildVehiclePayload() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Dealership_Purchase_Order_Item__c> items = [SELECT Id, Model__c, VF_TransID__c, Product_Code__c, Sunroof__c, Exterior_Color__c, Interior_Color__c, Interior_Equipment__c FROM Dealership_Purchase_Order_Item__c LIMIT 1];
            Test.startTest();
            String payload = DealerPurchaseOrderSapHelper.buildVehiclePayloadGetPrice(items);
            System.assertNotEquals(null, payload);
            System.debug(payload);
            Test.stopTest();
        }
    }

    @isTest
    static void testMakeCallout() {
        Test.startTest();
        // Set mock response
        Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderPriceSapMock());

        String requestBody = '{"items":[]}';
        DealerPurchaseOrderSapHelper.GetPriceResponseWrapper res = DealerPurchaseOrderSapHelper.makeCalloutGetPrice(requestBody);
        System.assertNotEquals(null, res);
        Test.stopTest();
    }

    @isTest
    static void testGetPartOrderPriceFromSAP() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<OrderItem> orderItems = [SELECT Id, Product2Id,VF_TransID__c, PricebookEntryId, Quantity FROM OrderItem LIMIT 1];
            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderPriceSapMock());
            Test.startTest();
            DealerPurchaseOrderSapHelper.GetPriceResponseWrapper res = DealerPurchaseOrderSapHelper.getPartOrderPriceFromSAP(orderItems);
            System.assertNotEquals(null, res);
            Test.stopTest();
        }
    }

    @isTest
    static void testGetVehicleOrderPriceFromSAP() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Dealership_Purchase_Order_Item__c> items = [SELECT Id, Model__c, VF_TransID__c, Product_Code__c, Sunroof__c, Exterior_Color__c, Interior_Color__c, Interior_Equipment__c FROM Dealership_Purchase_Order_Item__c LIMIT 1];
            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderPriceSapMock());
            Test.startTest();
            DealerPurchaseOrderSapHelper.GetPriceResponseWrapper res = DealerPurchaseOrderSapHelper.getVehicleOrderPriceFromSAP(items);
            System.assertNotEquals(null, res);
            Test.stopTest();
        }
    }

    @isTest
    static void testMakeCalloutSyncOrderVehicle() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Order> orders = [SELECT BillingCountryCode, Customer_Order_Number__c, Purchase_Order_Category__c, RecordType.Name, Account.VF_Level__c, Account.Parent.VF_DealerCode__c, Account.VF_DealerCode__c, OrderNumber, CreatedDate, Expected_Delivery_Date__c, Account.Parent.Plant_Part__c, Account.Parent.Plant_Vehicle__c, (SELECT OrderItemNumber, Product2.VF_ProductId__c, Quantity, PricebookEntry.VF_UOM__c, Product2.VF_Model__c, Product2.VF_Trim__c, Product2.Family, Product2.VF_FamilyValue__c FROM OrderItems) FROM Order WHERE RecordType.Name = 'Dealer Vehicle Purchase' LIMIT 1];

            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderSapMock(200, '{"result":{"code":"200","message":"Success","status":"S"},"data":{}}'));
            Test.startTest();
            DealerPurchaseOrderSapHelper.SyncOrderResult res = DealerPurchaseOrderSapHelper.makeCalloutSyncOrder(orders[0]);
            System.assertEquals(true, res.order.Sync2SAP__c, 'Order should be sync to SAP successfully');
            Test.stopTest();
        }
    }

    @isTest
    static void testMakeCalloutSyncOrderPart() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Order> orders = [SELECT BillingCountryCode, Customer_Order_Number__c, Purchase_Order_Category__c, RecordType.Name, Account.VF_Level__c, Account.Parent.VF_DealerCode__c, Account.VF_DealerCode__c, OrderNumber, CreatedDate, Expected_Delivery_Date__c, Account.Parent.Plant_Part__c, Account.Parent.Plant_Vehicle__c, (SELECT OrderItemNumber, Product2.VF_ProductId__c, Quantity, PricebookEntry.VF_UOM__c, Product2.VF_Model__c, Product2.VF_Trim__c, Product2.Family, Product2.VF_FamilyValue__c FROM OrderItems) FROM Order WHERE RecordType.Name = 'Dealer Part Purchase' LIMIT 1];

            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderSapMock(200, '{"result":{"code":"200","message":"Success","status":"S"},"data":{}}'));
            Test.startTest();
            DealerPurchaseOrderSapHelper.SyncOrderResult res = DealerPurchaseOrderSapHelper.makeCalloutSyncOrder(orders[0]);
            System.assertEquals(true, res.order.Sync2SAP__c, 'Order should be sync to SAP successfully');
            Test.stopTest();
        }
    }
    @isTest
    static void testMakeCalloutSyncOrderError() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Order> orders = [SELECT BillingCountryCode, Customer_Order_Number__c, Purchase_Order_Category__c, RecordType.Name, Account.VF_Level__c, Account.Parent.VF_DealerCode__c, Account.VF_DealerCode__c, OrderNumber, CreatedDate, Expected_Delivery_Date__c, Account.Parent.Plant_Part__c, Account.Parent.Plant_Vehicle__c, (SELECT OrderItemNumber, Product2.VF_ProductId__c, Quantity, PricebookEntry.VF_UOM__c, Product2.VF_Model__c, Product2.VF_Trim__c, Product2.Family, Product2.VF_FamilyValue__c FROM OrderItems) FROM Order WHERE RecordType.Name = 'Dealer Part Purchase' LIMIT 1];

            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderSapMock(200, '{"result":{"code":"400","message":"Failed","status":"F"},"data":{}}'));
            Test.startTest();
            DealerPurchaseOrderSapHelper.SyncOrderResult res = DealerPurchaseOrderSapHelper.makeCalloutSyncOrder(orders[0]);
            System.assertEquals(false, res.order.Sync2SAP__c, 'Order should not be sync to SAP');
            Test.stopTest();
        }
    }

    @isTest
    static void testGetSapPriority() {
        Test.startTest();
        // Test for valid categories
        System.assertEquals('03', DealerPurchaseOrderSapHelper.getSapPriority('Stock'), 'Expected priority for Stock is 03');
        System.assertEquals('06', DealerPurchaseOrderSapHelper.getSapPriority('Customer'), 'Expected priority for Customer is 06');
        System.assertEquals('01', DealerPurchaseOrderSapHelper.getSapPriority('VOR'), 'Expected priority for VOR is 01');
        System.assertEquals('02', DealerPurchaseOrderSapHelper.getSapPriority('Urgent'), 'Expected priority for Urgent is 02');

        // Test for an invalid category
        try {
            DealerPurchaseOrderSapHelper.getSapPriority('InvalidCategory');
            System.assert(false, 'Expected exception not thrown for invalid category');
        } catch (DealerPurchaseOrderSapHelper.DealerPurchaseOrderSapException e) {
            System.assertEquals('Invalid Purchase Order Category', e.getMessage(), 'Expected exception message for invalid category');
        }
        Test.stopTest();
    }

}