@isTest
private class SalesOrderInvoicePdfExtensionTest {
    @testSetup
    static void setupData() {
        // Create Account
        Account acc = new Account(
            Name = 'Test Account',
            BillingStreet = '123 Test St',
            BillingCity = 'Mumbai',
            BillingState = 'Assam',
            BillingCountry = 'India',
            BillingCountryCode = 'IN',
            BillingPostalCode = '400002',
            VF_IdentificationNo__c = 'GST12345678',
            Phone = '*********',
            VF_Hotline__c = '*********',
            AccountNumber = 'A123456',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Dealer').getRecordTypeId()
        );
        insert acc;

        // Create a Contact
        Contact con = new Contact(
            LastName = 'Test Contact',
            MobilePhone = '**********',
            AccountId = acc.Id
        );
        insert con;

        // Create Relationship Account (optional)
        Account relAcc = new Account(Name = 'Rel Acc', AccountNumber = 'RA123');
        insert relAcc;

        // Create Order
        Order ord = new Order(
            AccountId = acc.Id,
            Business_Unit__c = acc.Id,
            Name = 'Test Order',
            BillingStreet = '123 Test St',
            BillingCity = 'Mumbai',
            BillingState = 'Assam',
            BillingCountry = 'India',
            BillingCountryCode = 'IN',
            BillingPostalCode = '400002',
            ShippingStreet = '123 Test St',
            ShippingCity = 'Mumbai',
            ShippingState = 'Assam',
            ShippingCountry = 'India',
            ShippingCountryCode = 'IN',
            ShippingPostalCode = '400002',
            Description = 'Test',
            EffectiveDate = System.today(),
            Status = 'New',
            RecordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByName().get('Booking').getRecordTypeId(),
            Pricebook2Id = Test.getStandardPricebookId(),
            Vehicle_Information__c = 'Car Model X',
            Relationship_Account__c = relAcc.Id,
            Relationship_Contact__c = con.Id,
            Dealer_Discount_Amount__c = 1000,
            VF_Discount_Amount__c = 500,
            CGST_Amount__c = 100,
            CGST_Rate__c = 9,
            SGST_Amount__c = 100,
            SGST_Rate__c = 9,
            IGST_Amount__c = 0,
            IGST_Rate__c = 0,
            UGST_Amount__c = 0,
            UGST_Rate__c = 0,
            Vehicle_Ex_Showroom_Price__c = 60000,
            TCS_Rate__c = 1,
            TCS_Amount__c = 100,
            Grand_Total_Without_Additional_Fees__c = 59000,
            PAN__c = '**********',
            VIN_Number__c = null,
            Invoiced_Date__c = System.today()
        );
        insert ord;
    }

    @isTest
    static void testExtensionMethods() {
        Order testOrder = [SELECT Id FROM Order LIMIT 1];

        Test.startTest();
        ApexPages.StandardController stdCtrl = new ApexPages.StandardController(testOrder);
        SalesOrderInvoicePdfExtension ext = new SalesOrderInvoicePdfExtension(stdCtrl);

        // Test all public methods
        System.assertNotEquals(null, ext.getAccountBillingAddress());
        System.assertNotEquals(null, ext.getOrderBillingAddress());
        System.assertNotEquals(null, ext.getOrderShippingAddress());
        System.assertNotEquals(null, ext.getProductPrice());
        System.assertNotEquals(null, ext.getDiscount());
        System.assertNotEquals(null, ext.getNetSellingPrice());
        System.assertEquals(1, ext.getCount());

        // Set page parameters
        ApexPages.currentPage().getParameters().put('invoiceNumber', 'INV1234');
        ApexPages.currentPage().getParameters().put('invoiceName', 'Invoice Test');

        System.assertEquals('INV1234', ext.getInvoiceNumber());
        System.assertEquals('Invoice Test', ext.getInvoiceName());

        List<SalesOrderInvoicePdfExtension.GSTPrice> gstPrices = ext.getGSTPriceList();
        System.assert(gstPrices.size() > 0);

        System.assertNotEquals(null, ext.getInvoiceDate());
        System.assertNotEquals(null, ext.getGrandTotal());
        System.assertNotEquals(null, ext.getVehicleExShowroomPrice());
        System.assertNotEquals(null, ext.getTCSAmount());
        System.assertNotEquals(null, ext.getTCSRate());
        Test.stopTest();
    }
}