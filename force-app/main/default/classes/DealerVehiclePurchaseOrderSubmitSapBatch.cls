/**
 * Batch class to submit Dealer Vehicle Purchase Order to SAP 
 * 
 * This class is called by the scheduled job and it will callout the SAP API
 * to submit the Dealer Vehicle Purchase Order.
 * 
 * 
 */
public class DealerVehiclePurchaseOrderSubmitSapBatch implements
    Database.Batchable<sObject>, Database.Stateful, Database.AllowsCallouts {
    // The set of Order Ids to be processed
    public Set<Id> orderIds = new Set<Id>();
    // The list of logs to be inserted
    List<Outbound_Log__c> logs = new List<Outbound_Log__c>();
    // The list of orders to be updated
    List<Order> orderToUpdates = new List<Order>();
    
    /**
     * Constructor
     * 
     * @param orderIds The set of Order Ids to be processed
     */
    public DealerVehiclePurchaseOrderSubmitSapBatch(Set<Id> orderIds) {
        this.orderIds = orderIds;
    }
    
    /**
     * Start of the batch job
     * 
     * @param bc The batch context
     * @return The query locator
     */
    public Database.QueryLocator start(Database.BatchableContext bc) {
        String query = 'SELECT BillingCountryCode, Customer_Order_Number__c, Purchase_Order_Category__c, RecordType.Name, Account.VF_Level__c, Account.Parent.VF_DealerCode__c, Account.VF_DealerCode__c, OrderNumber, CreatedDate, Expected_Delivery_Date__c, Account.Parent.Plant_Part__c, Account.Parent.Plant_Vehicle__c, (SELECT OrderItemNumber, Product2.VF_ProductId__c, Quantity, PricebookEntry.VF_UOM__c, Product2.VF_Model__c, Product2.VF_Trim__c, Product2.Family, Product2.VF_FamilyValue__c FROM OrderItems) FROM Order WHERE Id IN :orderIds';
        return Database.getQueryLocator(query);
    }

    /**
     * Execute the batch job
     * 
     * @param bc The batch context
     * @param orders The list of orders to be processed
     */
    public void execute(Database.BatchableContext bc, List<Order> orders) {
        if (orders.isEmpty()) return;
        for (Order ord : orders) {
            DealerPurchaseOrderSapHelper.SyncOrderResult rs = DealerPurchaseOrderSapHelper.makeCalloutSyncOrder(ord);
            if (rs.log != null) {
                logs.add(rs.log);
            }
            if (rs.order != null) {
                orderToUpdates.add(rs.order);
            } 
        }
    }
    
    /**
     * Finish of the batch job
     * 
     * @param bc The batch context
     */
    public void finish(Database.BatchableContext bc) {
        insertLog(logs);
        updateOrder(orderToUpdates);
    }

    /**
     * Update the orders
     * 
     * @param orderToUpdates The list of orders to be updated
     */
    public void updateOrder (List<Order> orderToUpdates){
        if (!orderToUpdates.isEmpty()) {
            Database.SaveResult[] results = Database.update(orderToUpdates, false);

            List<String> errorMessages = new List<String>();
    
            for (Integer i = 0; i < results.size(); i++) {
                if (results[i].isSuccess()) continue;
                for (Database.Error err : results[i].getErrors()) errorMessages.add('Record Id: ' + orderToUpdates[i].Id + ' - Error: ' + err.getMessage());
            }
    
            if (!errorMessages.isEmpty()) throw new DealerPurchaseOrderException('Batch failed on finish(): ' + String.join(errorMessages, ' | '));
        }
    }

    /**
     * Insert the logs
     * 
     * @param logs The list of logs to be inserted
     */
    public void insertLog(List<Outbound_Log__c> logs){
        if (!logs.isEmpty()) {
            try {
                Database.insert(logs, false);
            } catch (Exception e) {
                System.debug(e);
            }
        }
    }

    /**
     * Exception class
     */
    public class DealerPurchaseOrderException extends Exception {}
}