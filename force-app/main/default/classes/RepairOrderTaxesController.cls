/*****Class Description*******************
* Name           : RepairOrderTaxesController
* Test Class     : RepairOrderTaxesControllerTest
* Purpose        : Get taxes, prices for an Work order from SAP
* Modification History  :
* Version  Author      DateModified     Description
* 1.0      NinhNN      11/04/2025       Init
* *******************************************/
public with sharing class RepairOrderTaxesController {
    @InvocableMethod(label='Get RO Taxes' description='Get SAP Taxes' )
    public static List<String> getROTaxesFromSAP(List<String> recordIds) {
        List<String> results = new List<String>();
        String recordId = recordIds[0];
        WorkOrder objRO = [SELECT Id, CreatedDate, Contact.Account.BillingStateCode, Account.VF_DealerCode__c, Account.BillingStateCode, 
                           Account.BillingCountryCode, Total_Discount_Amount__c, Status
                           FROM WorkOrder
                           WHERE Id = :recordId 
                           LIMIT 1];
        List<WorkOrderLineItem> objWOLI = [SELECT Id, LineItemNumber, WorkType.Name, WorkType.External_Id__c, WorkType.EstimatedDuration, WorkType.DurationType, Taxable_Amount__c,
                                           Discount_Amount__c, Classification__c, SAP_Labor_Rate__c, SAP_Total_Labor_Amount__c, Tax_1__c, Tax_1_Amount__c, Tax_2__c, Tax_2_Amount__c,
                                           Labor_Rate__c
                                           FROM WorkOrderLineItem 
                                           WHERE WorkOrderId = :recordId];
        Map<String, WorkOrderLineItem> map_woli = new Map<String, WorkOrderLineItem>();
        For (WorkOrderLineItem woli : objWOLI){
            String lineItemNumber = woli.LineItemNumber;
            String last6Digits = lineItemNumber.substring(lineItemNumber.length() - 6);
            map_woli.put(last6Digits, woli);
        }
        List<ProductConsumed> objPC = [SELECT Id, ProductConsumedNumber, ProductName, Product2.ProductCode, Request_Quantity__c, QuantityUnitOfMeasure, Taxable_Amount__c, 
                                       Discount_Amount__c, Classification__c, SAP_Total_Part_Amount__c, Tax_1__c, Tax_1_Amount__c, Tax_2__c, Tax_2_Amount__c, QuantityConsumed
                                       FROM ProductConsumed
                                       WHERE WorkOrderId = :recordId];
        Map<String, ProductConsumed> map_pc = new Map<String, ProductConsumed>();
        For (ProductConsumed pc : objPC){
            String ProductConsumedNumber = pc.ProductConsumedNumber;
            String last6Digits = ProductConsumedNumber.length() > 6 ? ProductConsumedNumber.substring(ProductConsumedNumber.length() - 6) : pc.ProductConsumedNumber;
            map_pc.put(last6Digits, pc);
        }        
        String requestBody = buildPayload(objRO, objWOLI, objPC); 
        System.debug(requestBody);
        // Define the endpoint and headers using NameCredential
        String url = 'callout:MuleSoft_Credential/dms/api/v1/taxes-pricing';
        String method = 'POST';
        String zbody;
        Map<String, String> headers = new Map<String, String>{
            'Content-Type' => 'application/json'
                };
                    try {
                        HttpResponse response = VF_MakeCalloutGeneric.makeCallout(url, method, headers, requestBody);
                        System.debug(response.getBody());
                        SAPResponseWrapper responseMS = (SAPResponseWrapper)JSON.deserialize(response.getbody(), SAPResponseWrapper.class);
                        System.debug(JSON.serialize(responseMS));
                        VF_Utility.createOutboundLog(url, method, requestBody, response, null);
                        zbody = response.getBody();
                        if (response.getStatusCode() == VF_Constants.SUCCESS_CODE) {
                            List<WorkOrderLineItem> tobeWOLI = new List<WorkOrderLineItem>();
                            List<ProductConsumed> tobePC = new List<ProductConsumed>();
                            for(cls_items item : responseMS.data.items) {
                                system.debug(item.transactionItemId + ' / ' + item.productCode);
                                WorkOrderLineItem t_woli = map_woli.get(item.transactionItemId);                               
                                If (t_woli != null){                                    
                                    for(cls_priceObject price : item.priceObject) {
                                        system.debug(price);
                                        /*if (price.priceType == 'ZSP2' && t_woli.Classification__c == 'W') { 
                                            t_woli.SAP_Total_Labor_Amount__c = price.priceValue;
                                        }
                                        if (price.priceType == 'JTCB' && t_woli.Classification__c == 'C') { 
                                            t_woli.SAP_Total_Labor_Amount__c = price.priceValue;
                                        }*/
                                        if (price.priceType == 'JTCB') { 
                                            t_woli.SAP_Total_Labor_Amount__c = price.priceValue;
                                        }
                                        if (price.priceType == 'JOSG') { 
                                            t_woli.Tax_1__c = price.priceAmount;
                                            t_woli.Tax_1_Amount__c = price.priceValue;
                                        }
                                        if (price.priceType == 'JOCG') { 
                                            t_woli.Tax_2__c = price.priceAmount;
                                            t_woli.Tax_2_Amount__c = price.priceValue;
                                        }                                        
                                    }
                                    tobeWOLI.add(t_woli);
                                }
                                else{
                                    ProductConsumed t_pc = map_pc.get(item.transactionItemId);
                                    If (t_pc != null){
                                        for(cls_priceObject price : item.priceObject) {
                                            system.debug(price);
                                            /*if (price.priceType == 'ZSP2' && t_pc.Classification__c == 'W') { 
                                                t_pc.SAP_Total_Part_Amount__c = price.priceValue;
                                            }
                                            if (price.priceType == 'JTCB' && t_pc.Classification__c == 'C') { 
                                                t_pc.SAP_Total_Part_Amount__c = price.priceValue;
                                            }*/
                                            if (price.priceType == 'JTCB') { 
                                                t_pc.SAP_Total_Part_Amount__c = price.priceValue;
                                            }
                                            if (price.priceType == 'JOSG') { 
                                                t_pc.Tax_1__c = price.priceAmount;
                                                t_pc.Tax_1_Amount__c = price.priceValue;
                                            }
                                            if (price.priceType == 'JOCG') { 
                                                t_pc.Tax_2__c = price.priceAmount;
                                                t_pc.Tax_2_Amount__c = price.priceValue;
                                            }                                        
                                        }
                                        tobePC.add(t_pc);
                                    } 
                                }                                                                
                            }
                            system.debug(tobeWOLI);
                            Update tobeWOLI;
                            system.debug(tobePC);
                            Update tobePC;                            
                            results.add('S');
                            return results;
                        } else {
                            If (zbody.contains('productCode expected type')){
                                results.add('Product/Work Type Code is missing. Please check the Master Data.');
                            }
                            else{
                                results.add(zbody);
                            }
                            system.debug(results);
                            return results;
                            //throw new CalloutException('Failed to fetch taxes. Status Code: ' + response.getStatusCode());                  
                        }
                    } catch (Exception ex) {
                        results.add(ex.getMessage());
                        results.add(zbody);
                        system.debug(results);
                        return results;
                        //throw new AuraHandledException('Error fetching taxes: ' + ex.getMessage());
                    }
    }
    
    private static String buildPayload(WorkOrder rec, List<WorkOrderLineItem> wolis, List<ProductConsumed> pcs) {
        String payload = '';        
        //Get custom metadata with getAll
        Map<String, String> stateSAPCode = new Map<String, String>();
        Map<String, State_Masterdata__mdt> mapStateMetadata = State_Masterdata__mdt.getAll();
        for(String mapKey : mapStateMetadata.keySet()) {
            stateSAPCode.put(mapStateMetadata.get(mapKey).State_Code__c, mapStateMetadata.get(mapKey).State_Code_SAP__c);
        }        
        Map<String, Object> params = new Map<String,Object>();
        DateTime currentDateTime = DateTime.now();
        // build header
        params.put('requestID',String.format('{0}_{1}', new List<String>{rec.Id, currentDateTime.format('yyyyMMdd_HHmmss')}));
        params.put('transactionID', rec.Id);
        params.put('transactionType', '2'); // WorkOrder
        // params.put('deliveryPriority', '00'); // ???   
        params.put('transactionDate', rec.CreatedDate.format('dd.MM.yyyy'));
        params.put('buyerLocation', stateSAPCode.get(rec.Account.BillingStateCode));       
        params.put('sellerLocation', rec.Account.VF_DealerCode__c);
        params.put('currency', 'INR');
        params.put('country', rec.Account.BillingCountryCode);
        // build items
        List<Map<String, Object>> items = new List<Map<String, Object>>();
        // build itm for items                 
        for (WorkOrderLineItem woli : wolis) {
            Map<String, Object> itm = new Map<String, Object>(); 
            String lineItemNumber = woli.LineItemNumber;
            String last6Digits = lineItemNumber.substring(lineItemNumber.length() - 6);
            itm.put('transactionItem', last6Digits);
            itm.put('productCode', woli.WorkType.External_Id__c);
            itm.put('productType', '60');
            itm.put('quantity', woli.WorkType.EstimatedDuration);
            itm.put('UOM', 'H'); 
            itm.put('basedPrice', woli.Labor_Rate__c);
            itm.put('discountAmount', woli.Discount_Amount__c);
            itm.put('billClassification', woli.Classification__c);
            items.add(itm);
        }
        for (ProductConsumed pc : pcs) {
            Map<String, Object> itm = new Map<String, Object>(); 
            String ProductConsumedNumber = pc.ProductConsumedNumber;
            String last6Digits = ProductConsumedNumber.length() > 6 ? ProductConsumedNumber.substring(ProductConsumedNumber.length() - 6) : pc.ProductConsumedNumber;
            itm.put('transactionItem', last6Digits);
            itm.put('productCode', pc.Product2.ProductCode);
            itm.put('productType', '50');            
            if (rec.Status == 'Open' || rec.Status == 'Quotation' || rec.Status == 'In Progress'){
                itm.put('quantity', pc.Request_Quantity__c); 
            }else{
                itm.put('quantity', pc.QuantityConsumed);
            }  
            itm.put('UOM', pc.QuantityUnitOfMeasure); 
            itm.put('basedPrice', null);
            itm.put('discountAmount', pc.Discount_Amount__c);
            itm.put('billClassification', pc.Classification__c);
            items.add(itm);
        }
        params.put('items', items);
        payload = JSON.serialize(params);    
        return payload;
    }
    
    public class SAPResponseWrapper {
        @AuraEnabled public cls_result result {get; set;}
        @AuraEnabled public cls_data data {get; set;}
    }
    public class cls_result {
        @AuraEnabled public String code;
        @AuraEnabled public String message;
        @AuraEnabled public String status;
    }
    public class cls_data {
        @AuraEnabled public String transactionID;
        @AuraEnabled public Integer transactionType;
        @AuraEnabled public cls_items[] items;
    }
    class cls_items {
        @AuraEnabled public String transactionItemId;
        @AuraEnabled public String productCode;
        @AuraEnabled public Decimal quantity;
        @AuraEnabled public cls_priceObject[] priceObject;
    }
    class cls_priceObject {
        @AuraEnabled public String priceType;
        @AuraEnabled public Decimal priceAmount;
        @AuraEnabled public Decimal priceValue;
        @AuraEnabled public Decimal unitPerPrice;
    }
}