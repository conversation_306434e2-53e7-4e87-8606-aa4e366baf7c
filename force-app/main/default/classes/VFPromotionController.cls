/*****Class Description*******************
* Name           : VFPromotionController
* Test Class     : 
* Purpose        : Get VinFast promotions from Loyalty
* Modification History  : NA
* Version  Author      DateModified     Description
* 1.0      thanhnm     18/04/2025       Init
* *******************************************/

public with sharing class VFPromotionController {

    @AuraEnabled(cacheable=true)
    public static List<PromotionWrapper> getVinFastPromotions(String recordId, String objectName) {
        List<PromotionWrapper> result =  new List<PromotionWrapper>();
        MS_Response msResponse = new MS_Response();

        try {
            if (objectName == 'Quote') {
                msResponse = getVinFastPromotionsByQuote(recordId);
            } else if (objectName == 'Order') {
                Order objOrder = [SELECT Id,Status FROM Order WHERE Id = :recordId LIMIT 1];

                // check Order status
                if (objOrder.Status != 'New') {
                    throw new CustomException('Your sale order is on the process to handle for vehicle matching, invoicing, delivery. If you want to edit more, please turn back to New status.');
                }
                msResponse = getVinFastPromotionsByOrder(recordId);
            }
            if(!msResponse.success) {
                throw new CalloutException('Loyalty message: ' + msResponse.message);
            }
            result = msResponse.data.promotions;
        } catch (Exception ex) {
            throw new AuraHandledException(ex.getMessage());
        }

        return result;
    }

    @AuraEnabled(cacheable=true)
    public static String redeemVinFastPromotions(String orderId) {
        String result =  null;
        MS_Response msResponse = new MS_Response();

        try {
            msResponse = redeemPromotionsByOrder(orderId);
            
            if(!msResponse.success) {
                throw new CalloutException('Loyalty Redeem: ' + msResponse.message);
            }
            result = msResponse.data.sessionId;
            return result;
        } catch (Exception ex) {
            throw new AuraHandledException(ex.getMessage());
        }        
    }

    public static MS_Response getVinFastPromotionsByQuote(String recordId) {
        Quote objQuote = [SELECT Id,QuoteNumber,QuoteAccountId,Status,TotalPrice,LineItemCount,ExpirationDate,Accessory_Kit__c,Accessory_Kit__r.Name,Accessory_Kit__r.Kit_Family_Code__c,Accessory_Kit__r.Kit_Pricing__c,(SELECT Id,UnitPrice,Product_Family_Code__c,Product2.ProductCode,Product2.VF_FamilyValue__c FROM QuoteLineItems)  FROM Quote WHERE Id = :recordId LIMIT 1];

        if (objQuote.Status != 'New') {
            throw new CustomException('Your sale quote is on the process to handle. If you want to edit more, please turn back to New status.');
        }

        String payload = buildQuotePayload(objQuote);
        MS_Response msResult = getPromotions(payload);

        return msResult;
    }

    public static MS_Response getVinFastPromotionsByOrder(String recordId) {
        Order objOrder = [SELECT Id,OrderNumber,AccountId,Status,EffectiveDate,TotalAmount,Accessory_Kit__c,Accessory_Kit__r.Name,Accessory_Kit__r.Kit_Pricing__c,Accessory_Kit__r.Kit_Family_Code__c,Count_Order_Item__c,(SELECT Id,UnitPrice,Order_Product_Family_Code__c,Product2.ProductCode,Product2.VF_FamilyValue__c FROM OrderItems) FROM Order WHERE Id = :recordId LIMIT 1];

        String payload = buildOrderPayload(objOrder);
        MS_Response msResult = getPromotions(payload);

        return msResult;
    }

    public static MS_Response redeemPromotionsByOrder(String recordId) {
        Order objOrder = [SELECT Id,OrderNumber,AccountId,Status,EffectiveDate,TotalAmount,Accessory_Kit__c,Accessory_Kit__r.Name,Accessory_Kit__r.Kit_Pricing__c,Accessory_Kit__r.Kit_Family_Code__c,Count_Order_Item__c,Invoiced_Date__c,(SELECT Id,UnitPrice,Order_Product_Family_Code__c,Product2.ProductCode,Product2.VF_FamilyValue__c FROM OrderItems),(SELECT id,VF_Promotion_Code__c FROM VinFast_Promotions__r) FROM Order WHERE Id = :recordId LIMIT 1];

        String payload = buildRedeemPayload(objOrder);
        MS_Response msResult = redeemPromotions(payload);

        return msResult;
    }

    public static MS_Response getPromotions(String requestBody) {
        String url = 'callout:MuleSoft_Credential/dms/api/v1/loyalty/promotions/get-matched-v2';
        String httpMethod = 'POST';
        Map<String, String> headers = new Map<String, String>{
            'Content-Type' => 'application/json'
        };

        return calloutLoyalty(url, httpMethod, headers, requestBody);

    }

    public static MS_Response redeemPromotions(String requestBody) {
        String url = 'callout:MuleSoft_Credential/dms/api/v1/loyalty/promotions/redemptions-v2';
        String httpMethod = 'POST';
        Map<String, String> headers = new Map<String, String>{
            'Content-Type' => 'application/json'
        };

        return calloutLoyalty(url, httpMethod, headers, requestBody);

    }

    public static MS_Response calloutLoyalty(String url, String method, Map<String, String> headers, String requestBody) {
        MS_Response responseMS = new MS_Response ();
        
        System.debug(requestBody);
        try {
            HttpResponse response = VF_MakeCalloutGeneric.makeCallout(url, method, headers, requestBody);
            System.debug(response.getBody());
            responseMS = (MS_Response)JSON.deserialize(response.getbody(), MS_Response.class);
            System.debug(JSON.serialize(responseMS));

            // VF_Utility.createOutboundLog(url, method, requestBody, response, null);
            if (response.getStatusCode() == VF_Constants.SUCCESS_CODE) {
                responseMS.success = true;
            } else { 
                responseMS.success = false;
                responseMS.message = String.format('{0} - {1}', new List<String>{ responseMS.result.message, responseMS.result.description });
            }
        } catch (Exception ex) {
            responseMS.success = false;
            responseMS.message = 'Please check with Mulesoft: ' + ex.getMessage();
        }

        return responseMS;
    }

    private static String buildQuotePayload(Quote rec) {
        String payload = '';

        DateTime bookingDate = DateTime.newInstanceGmt(rec.ExpirationDate, Time.newInstance(0, 0, 0, 0));
        Decimal quoteTotalAmount = rec.Accessory_Kit__c != null ? (rec.TotalPrice + rec.Accessory_Kit__r.Kit_Pricing__c) : rec.TotalPrice ;

        Map<String, Object> params = new Map<String,Object>();
        params.put('customerId', rec.QuoteAccountId);
        params.put('orderId', rec.Id);
        params.put('orderAmount', quoteTotalAmount);
        params.put('orderQuantity', rec.LineItemCount);
        params.put('bookingDate', bookingDate.getTime());
        params.put('invoiceDate', null);
        params.put('deliveryDate', null);

        // build product items
        List<Map<String, Object>> productItems = new List<Map<String, Object>>();
        // get Accessory Kit
        if (rec.Accessory_Kit__c != null) {
            Map<String, Object> accessoryKit = new Map<String, Object>();
            accessoryKit.put('code', rec.Accessory_Kit__r?.Name);
            accessoryKit.put('family', 'AK1'); // set default
            accessoryKit.put('quantity', 1); // set default
            accessoryKit.put('price', rec.Accessory_Kit__r?.Kit_Pricing__c);

            productItems.add(accessoryKit);
        }
        

        for (QuoteLineItem qli : rec.QuoteLineItems) {
            // Vehcile + Options
            Map<String, Object> item = new Map<String, Object>();
            if(qli.Product_Family_Code__c == 'MOD') {
                item.put('code', qli.Product2.ProductCode);
            } else {
                item.put('code', qli.Product2.VF_FamilyValue__c);
            }
            
            item.put('family', qli.Product_Family_Code__c);
            item.put('quantity', 1); // set default
            item.put('price', qli.UnitPrice);
            productItems.add(item);
        }
        params.put('products', productItems);

        payload = JSON.serialize(params);

        return payload;
    }

    private static String buildOrderPayload(Order rec) {
        String payload = '';

        DateTime bookingDate = DateTime.newInstanceGmt(rec.EffectiveDate, Time.newInstance(0, 0, 0, 0));
        Decimal totalAmount = rec.Accessory_Kit__c != null ? (rec.TotalAmount + rec.Accessory_Kit__r.Kit_Pricing__c) : rec.TotalAmount ;

        Map<String, Object> params = new Map<String,Object>();
        params.put('customerId', rec.AccountId);
        params.put('orderId', rec.Id);
        params.put('orderAmount', totalAmount);
        params.put('orderQuantity', rec.Count_Order_Item__c);
        params.put('bookingDate', bookingDate.getTime());
        params.put('invoiceDate', null);
        params.put('deliveryDate', null);

        // build product items
        List<Map<String, Object>> productItems = new List<Map<String, Object>>();
        // get Accessory Kit
        if (rec.Accessory_Kit__c != null) {
            Map<String, Object> accessoryKit = new Map<String, Object>();
            accessoryKit.put('code', rec.Accessory_Kit__r?.Name);
            accessoryKit.put('family', rec.Accessory_Kit__r?.Kit_Family_Code__c); // set default AK1
            accessoryKit.put('quantity', 1); // set default
            accessoryKit.put('price', rec.Accessory_Kit__r?.Kit_Pricing__c);

            productItems.add(accessoryKit);
        }
        

        for (OrderItem oi : rec.OrderItems) {
            // Vehcile + Options
            Map<String, Object> item = new Map<String, Object>();
            if(oi.Order_Product_Family_Code__c == 'MOD') {
                item.put('code', oi.Product2.ProductCode);
            } else {
                item.put('code', oi.Product2.VF_FamilyValue__c);
            }
            
            item.put('family', oi.Order_Product_Family_Code__c);
            item.put('quantity', 1); // set default
            item.put('price', oi.UnitPrice);
            productItems.add(item);
        }
        params.put('products', productItems);

        payload = JSON.serialize(params);
        
        return payload;
    }

    private static String buildRedeemPayload(Order rec) {
        String payload = '';

        DateTime bookingDate = DateTime.newInstanceGmt(rec.EffectiveDate, Time.newInstance(0, 0, 0, 0));
        Decimal totalAmount = rec.Accessory_Kit__c != null ? (rec.TotalAmount + rec.Accessory_Kit__r.Kit_Pricing__c) : rec.TotalAmount ;

        DateTime invoiceDate = rec.Invoiced_Date__c != null ? rec.Invoiced_Date__c : DateTime.now() ;

        Map<String, Object> params = new Map<String,Object>();
        params.put('customerId', rec.AccountId);
        params.put('orderId', rec.Id);
        params.put('orderAmount', totalAmount);
        params.put('orderQuantity', rec.Count_Order_Item__c);
        params.put('bookingDate', bookingDate.getTime());
        params.put('invoiceDate', invoiceDate.getTime());
        params.put('deliveryDate', null);

        List<String> campaigncodes = new List<String>();
        // get VF promotion campaigncodes
        for(VinFast_Promotion__c vp : rec.VinFast_Promotions__r) {
            campaigncodes.add(vp.VF_Promotion_Code__c);
        }
        params.put('campaignCodes', campaigncodes);

        // build product items
        List<Map<String, Object>> productItems = new List<Map<String, Object>>();
        // get Accessory Kit
        if (rec.Accessory_Kit__c != null) {
            Map<String, Object> accessoryKit = new Map<String, Object>();
            accessoryKit.put('code', rec.Accessory_Kit__r?.Name);
            accessoryKit.put('family', rec.Accessory_Kit__r?.Kit_Family_Code__c); // set default AK1
            accessoryKit.put('quantity', 1); // set default
            accessoryKit.put('price', rec.Accessory_Kit__r?.Kit_Pricing__c);

            productItems.add(accessoryKit);
        }
        

        for (OrderItem oi : rec.OrderItems) {
            // Vehcile + Options
            Map<String, Object> item = new Map<String, Object>();
            if(oi.Order_Product_Family_Code__c == 'MOD') {
                item.put('code', oi.Product2.ProductCode);
            } else {
                item.put('code', oi.Product2.VF_FamilyValue__c);
            }
            
            item.put('family', oi.Order_Product_Family_Code__c);
            item.put('quantity', 1); // set default
            item.put('price', oi.UnitPrice);
            productItems.add(item);
        }
        params.put('products', productItems);

        payload = JSON.serialize(params);
        
        return payload;
    }

    @AuraEnabled
    public static void upsertData(String objectName, String recordId, List<PromotionWrapper> selectedPromotion) {
        System.debug('Received Data: ' + JSON.serialize(selectedPromotion));

        if (selectedPromotion == null || selectedPromotion.isEmpty()) {
            throw new AuraHandledException('No promotion data received!');
        }
        List<VinFast_Promotion__c> recordsToInsert = new List<VinFast_Promotion__c>();
    
        for (PromotionWrapper item : selectedPromotion) {
            VinFast_Promotion__c obj = new VinFast_Promotion__c();
            obj.VF_Promotion_Name__c = item.name;
            obj.VF_Promotion_Code__c = item.code;
            obj.VF_Applied_Term__c = item.termsApply;
            obj.VF_Fixed_Amount__c = item.discountChanged;
            obj.VF_Promotion_ID__c = item.promotionId;
            DateTime startDate = DateTime.newInstance(item.promotionStartDate);
            obj.VF_Start_Date__c = startDate.date();

            DateTime endDate = DateTime.newInstance(item.promotionExpiryDate);
            obj.VF_End_Date__c = endDate.date();
            obj.VF_Promotion_Type__c = item.metadata.eligibleForVehicle.equalsIgnoreCase('YES') ? 'Vehicle Order Amount Deduction' : 'No Vehicle Order Amount Deduction';

            if (objectName == 'Quote') {
                obj.VF_SQ_No__c = recordId;
            } else if (objectName == 'Order') {
                obj.VF_SO_No__c = recordId;
            }
            obj.External_Key__c = String.format('{0}_{1}_{2}', new List<String>{ recordId, item.code, item.promotionId });

            recordsToInsert.add(obj);
        }
        System.debug('transactions: ' + JSON.serialize(recordsToInsert));
        upsert recordsToInsert External_Key__c;
    }

    public class CustomException extends Exception {}

    public class MS_Response {
        @AuraEnabled public cls_result result {get; set;}
        @AuraEnabled public Boolean success {get; set;}
        @AuraEnabled public string message {get; set;}
        @AuraEnabled public cls_data data {get; set;}
    }

    public class cls_result {
		@AuraEnabled public String message;
		@AuraEnabled public String status;
        @AuraEnabled public String description;
    }

    public class cls_data {
		@AuraEnabled public String sessionId;
		@AuraEnabled public List<PromotionWrapper> promotions;
    }

    public class PromotionWrapper {
        @AuraEnabled public String name {get; set;}
        @AuraEnabled public String code {get; set;}
        @AuraEnabled public String termsApply {get; set;}
        @AuraEnabled public String promotionId {get; set;}
        @AuraEnabled public Decimal discountChanged {get; set;}
        @AuraEnabled public Long promotionStartDate {get; set;}
        @AuraEnabled public Long promotionExpiryDate {get; set;}
        @AuraEnabled public Decimal budgetApplied {get; set;}
        @AuraEnabled public cls_metadata metadata {get; set;}
    }

    public class cls_metadata {
        @AuraEnabled public String eligibleForVehicle {get; set;}
        @AuraEnabled public String accessoryKitInfo {get; set;}
    }
   
}