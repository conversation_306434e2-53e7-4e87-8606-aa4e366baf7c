/*****Class Description*******************
* Name           : SalesOrderInvoicePdfController
* Test Class     : SalesOrderInvoicePdfControllerTest
* Purpose        : Controller for Sales Order Invoice PDF
* Version  Author      DateModified     Description
* 1.0      thangnv12    11/04/2025       Init
* *******************************************/
public with sharing class SalesOrderInvoicePdfController {
    /**
     * Generate the next available file title based on invoiceType and existing ContentDocuments.
     * @param recordId Id of the Sales Order record
     * @param invoiceType String invoiceType of the file corresponding to the Sales Order Invoice Type ('PIV-' for Proforma Invoice, 'TIV-' for Tax Invoice)
     * @return String suffix of the file to be generated
     */
    @AuraEnabled
    public static String generateInvoiceNumber(Id recordId, String invoiceType) {
        return SalesOrderInvoicePdfService.generateInvoiceNumber(recordId, invoiceType);
    }

    /**
     * Get the Order Invoice History
     * @param recordId Id of the Sales Order record
     * @param invoiceNumber String of the invoice number
     * @param invoiceType String invoiceType of the file corresponding to the Sales Order Invoice Type ('PIV' for Proforma Invoice, 'TIV' for Tax Invoice)
     * @param contentDocumentId Id of the ContentDocument of the generated file
     * @return String Id of the Order Invoice History record
     */
    @AuraEnabled
    public static String createOrderInvoiceHistory(Id recordId, String invoiceNumber, String invoiceType, String contentDocumentId) {
        return SalesOrderInvoicePdfService.createOrderInvoiceHistory(recordId, invoiceNumber, invoiceType, contentDocumentId);
    }
}