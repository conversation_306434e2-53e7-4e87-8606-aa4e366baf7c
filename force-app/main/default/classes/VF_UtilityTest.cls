@isTest
public with sharing class VF_UtilityTest {

    @testSetup
    static void setupTestData() {
        // Create test data for Outbound_Log__c
        Outbound_Log__c log = new Outbound_Log__c(
            Endpoint__c = 'https://example.com/api',
            HTTP_Method__c = 'POST',
            Request_Body__c = '{"key":"value"}'
        );
        insert log;

        // Create test data for SYS_Exception__c
        SYS_Exception__c err = new SYS_Exception__c(
            Developer_Message__c = 'Test Developer Message',
            Exception_Message__c = 'Test Exception Message',
            SYS_StackTrace_c__c = 'Test Stack Trace',
            Type__c = 'Test Type'
        );
        insert err;
    }

    @isTest
    static void testCreateOutboundLog() {
        // Call the method
        HttpResponse response = new HttpResponse();
        response.setStatusCode(200);
        response.setBody('{"success":true}');
        Outbound_Log__c log = VF_Utility.createOutboundLog(
            'https://example.com/api',
            'POST',
            '{"key":"value"}',
            response,
            'No error'
        );

        // Assert the log was created
        System.assertNotEquals(null, log.Id, 'Log should be created');
        System.assertEquals('https://example.com/api', log.Endpoint__c, 'Endpoint should match');
        System.assertEquals('POST', log.HTTP_Method__c, 'HTTP Method should match');
        System.assertEquals('{"key":"value"}', log.Request_Body__c, 'Request Body should match');
        System.assertEquals(200, log.Response_Status_Code__c, 'Response Status Code should match');
        System.assertEquals('{"success":true}', log.Response_Body__c, 'Response Body should match');
        System.assertEquals('No error', log.Error_Message__c, 'Error Message should match');
    }

    @isTest
    static void testUpdateOutboundLog() {
        // Fetch an existing log
        Outbound_Log__c log = [SELECT Id FROM Outbound_Log__c LIMIT 1];

        // Call the method
        HttpResponse response = new HttpResponse();
        response.setStatusCode(400);
        response.setBody('{"success":false}');
        VF_Utility.updateOutboundLog(log.Id, response, 'Error occurred');

        // Assert the log was updated
        Outbound_Log__c updatedLog = [SELECT Response_Status_Code__c, Response_Body__c, Error_Message__c FROM Outbound_Log__c WHERE Id = :log.Id];
        System.assertEquals(400, updatedLog.Response_Status_Code__c, 'Response Status Code should match');
        System.assertEquals('{"success":false}', updatedLog.Response_Body__c, 'Response Body should match');
        System.assertEquals('Error occurred', updatedLog.Error_Message__c, 'Error Message should match');
    }

    @isTest
    static void testCreateCustomException() {
        // Call the method
        SYS_Exception__c err = VF_Utility.createCustomException(
            'Developer Message',
            'Exception Message',
            'Stack Trace',
            'Custom Type'
        );

        // Assert the exception was created
        System.assertNotEquals(null, err.Id, 'Exception should be created');
        System.assertEquals('Developer Message', err.Developer_Message__c, 'Developer Message should match');
        System.assertEquals('Exception Message', err.Exception_Message__c, 'Exception Message should match');
        System.assertEquals('Stack Trace', err.SYS_StackTrace_c__c, 'Stack Trace should match');
        System.assertEquals('Custom Type', err.Type__c, 'Type should match');
    }

    @isTest
    static void testSaveException() {
        delete [SELECT Id FROM SYS_Exception__c];
        // Create a test exception
        try {
            Integer result = 1 / 0; // This will throw a Divide by Zero exception
        } catch (Exception ex) {
            // Call the method
            VF_Utility.saveException(ex);
        }

        // Assert the exception was saved
        SYS_Exception__c savedException = [SELECT Exception_Message__c, SYS_StackTrace_c__c, Type__c FROM SYS_Exception__c LIMIT 1];
        System.assertEquals('Divide by 0', savedException.Exception_Message__c, 'Exception Message should match');
        System.assert(savedException.SYS_StackTrace_c__c.contains('VF_UtilityTest'), 'Stack Trace should contain the test class name');
        System.assertEquals('System.MathException', savedException.Type__c, 'Type should match');
    }

    @isTest
    static void testConvertDate() {
        Date d = Date.newInstance(2025, 01, 01);
        String result = VF_Utility.formatDateTimeToDDMMYYYY(d);
        System.assertEquals('01.01.2025', result);
    }

    @isTest
    static void testConvertDatetime() {
        Datetime d = Datetime.newInstance(2025, 01, 01, 01, 01, 01);
        String result = VF_Utility.formatDateTimeToDDMMYYYY(d);
        System.assertEquals('01.01.2025', result);
    }

    @isTest
    static void testConvertDatetimeWrongData() {
        String result = VF_Utility.formatDateTimeToDDMMYYYY('d');
        System.assertEquals(null, result);
    }
}
