@IsTest
public with sharing class VF_PDFPreviewControllerTest {
    @isTest static void testConvertWithValidInput() {
        // Create a dummy Account record to use as recordId
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        Id id = VF_PDFPreviewController.generateAndSavePDF(acc.Id, 'test.pdf', '/apex/vfDummyPdfPage', null, null);
        System.assertNotEquals(null, Id, 'ContentDocumentId must be created');
    }
}