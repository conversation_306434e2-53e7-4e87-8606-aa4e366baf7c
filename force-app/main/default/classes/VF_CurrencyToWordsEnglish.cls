public with sharing class VF_CurrencyToWordsEnglish {
    private static List<Long> scales = new List<Long>{
        1000000000000000000L, // quintillion
        1000000000000000L,    // quadrillion
        1000000000000L,       // trillion
        1000000000L,          // billion
        1000000L,             // million
        1000L,                // thousand
        100L                 // hundred
    };

    private static List<String> scaleNames = new List<String>{
        ' quintillion',
        ' quadrillion',
        ' trillion',
        ' billion',
        ' million',
        ' thousand',
        ' hundred'
    };

    private static String[] units = new String[]{'','one','two','three','four','five','six','seven','eight','nine','ten',
    'eleven','twelve','thirteen','fourteen','fifteen','sixteen','seventeen','eighteen','nineteen'};

    private static String[] tens = new String[]{'','', 'twenty','thirty','forty','fifty','sixty','seventy','eighty','ninety'};

    public static String convert(Decimal amount) {
        if (amount == 0) return 'zero';
        String result = '';
        // Handle negative
        if (amount < 0) {
            result += 'minus ';
            amount = amount.abs();
        }

        Long integerPart = amount.longValue();
        Decimal decimalPart = amount - integerPart;

        result += convertInteger(integerPart);

        if (decimalPart > 0) {
            String decimalStr = String.valueOf(decimalPart).substringAfter('.');
            result += ' point';
            for (Integer i = 0; i < decimalStr.length(); i++) {
                String digitChar = decimalStr.substring(i, i+1);
                Integer digit = Integer.valueOf(digitChar);
                result += ' ' + units[digit];
            }
        }

        return result.trim();
    }

    private static String convertInteger(Long input) {
        if (input == 0) return '';
        String words = '';

        for (Integer i = 0; i < scales.size(); i++) {
            Long scaleValue = scales[i];
            if (input >= scaleValue) {
                words += convertInteger(input / scaleValue) + scaleNames[i] + ' ';
                input = Math.mod(input, scaleValue);
            }
        }

        if (input > 0) {
            if (input < 20) {
                words += units[(Integer)input];
            } else {
                words += tens[(Integer)(input / 10)];
                Integer tmp = (Integer)Math.mod(input, 10);
                if (tmp > 0) {
                    words += '-' + units[tmp];
                }
            }
        }

        return words.trim();
    }
}