@isTest
public class BlockFileUploadTest {
    @isTest
    static void testValidFileUpload() {
        Test.startTest();
        ContentVersion cv = new ContentVersion(
            Title = 'TestImage',
            PathOnClient = 'TestImage.jpeg',
            VersionData = Blob.valueOf('Test content')
        );
        insert cv; // Should pass
        Test.stopTest();
    }

    @isTest
    static void testBlockedFileType() {
        Test.startTest();
        ContentVersion cv = new ContentVersion(
            Title = 'BlockedFile',
            PathOnClient = 'script.exe',
            VersionData = Blob.valueOf('Sample content')
        );
        try {
            insert cv;
            System.assert(false, 'Expected file type validation error.');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('file type is not allowed'));
        }
        Test.stopTest();
    }
}