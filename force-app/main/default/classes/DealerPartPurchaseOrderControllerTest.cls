@isTest
private class DealerPartPurchaseOrderControllerTest {
    
    @testSetup
    static void setupTestData() {
        DealerPurchaseOrderTestDataFactory.createTestData();
    }

    @isTest
    static void testGetSapOrderPrice() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<OrderItem> orderItems = [SELECT Id, Product2Id, VF_TransID__c, PricebookEntryId, Quantity FROM OrderItem LIMIT 1];
            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderPriceSapMock());
            Test.startTest();
            DealerPurchaseOrderSapHelper.GetPriceResponseWrapper result = DealerPartPurchaseOrderController.getSapOrderPrice(orderItems);
            Test.stopTest();
            System.assertNotEquals(null, result, 'Expected get results');
        }
    }

    @isTest
    static void testGetProductUnitPrice() {
        Product2 p = [SELECT Id FROM Product2 WHERE RecordType.Name = 'Part' LIMIT 1];
        Test.startTest();
        PricebookEntry result = DealerPartPurchaseOrderController.getProductUnitPrice(p.Id);
        Test.stopTest();
        System.assertEquals(p.Id, result.Product2Id);
        System.assertEquals(1000, result.UnitPrice);
    }

    @isTest
    static void testSearch() {
        List<String> selectedIds = new List<String>();
        Test.startTest();
        Id [] fixedSearchResults= new Id[1];
        fixedSearchResults[0] = [SELECT id, Name FROM Product2 WHERE RecordType.Name = 'Part' LIMIT 1]?.Id;
        Test.setFixedSearchResults(fixedSearchResults);

        List<VfCustomLookupSearchResult> results = DealerPartPurchaseOrderController.search('Test', selectedIds);
        Test.stopTest();
        System.assertEquals(false, results.isEmpty(), 'Expected search results');
    }

    @isTest
    static void testCreateAndGetDealerPartPurchaseOrder() {

        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            Product2 p = [SELECT Id FROM Product2 LIMIT 1];
            PricebookEntry pbe = [SELECT Id, UnitPrice FROM PricebookEntry WHERE Product2Id = :p.Id LIMIT 1];

            Order o = new Order(
                Name = 'Test Order',
                Reference_WO_Number__c = 'Test',
                Expected_Delivery_Date__c = System.today(),
                Description = 'Test',
                Lock_Edit__c = false
            );

            List<OrderItem> items = new List<OrderItem>{
                new OrderItem(
                    PricebookEntryId = pbe.Id,
                    Quantity = 1,
                    Product2Id = p.Id,
                    UnitPrice = pbe.UnitPrice
                )
            };

            Id orderId;
            Test.startTest();
                // Test Create
                orderId = DealerPartPurchaseOrderController.createDealerPartPurchaseOrder(o, items);

                // Test Get
                DealerPartPurchaseOrderController.DealerPurhaseOrderData data =
                DealerPartPurchaseOrderController.getDealerPartPurchaseOrder(orderId);

                
                // Test Update
                o.Id = orderId;
                List<OrderItem> itemNews = new List<OrderItem>{
                    new OrderItem(
                        PricebookEntryId = pbe.Id,
                        Quantity = 2,
                        UnitPrice = pbe.UnitPrice
                    )
                };
                orderId = DealerPartPurchaseOrderController.createDealerPartPurchaseOrder(o, itemNews);

            Test.stopTest();
            System.assertNotEquals(null, orderId, 'Order should have been created');
            System.assertEquals(orderId, data.header.Id);
            System.assertEquals(1, data.items.size());
        }
    }

    @isTest
    static void testGetProductUnitPrice_Exception() {
        Test.startTest();
        try {
            DealerPartPurchaseOrderController.getProductUnitPrice('fakeId');
            System.assert(false, 'Exception should have been thrown');
        } catch (DealerPartPurchaseOrderController.DealerPurchaseOrderException e) {
            System.assert(e.getMessage().contains('UNIT_PRICE_NOT_FOUND'));
        }
        Test.stopTest();
    }
}