@isTest
private class DealerVehiclePurchaseOrderControllerTest {
    
    @testSetup
    static void setupTestData() {
        DealerPurchaseOrderTestDataFactory.createTestData();
    }

    @isTest
    static void testGetMasterData() {

        Test.startTest();
        List<DealerVehiclePurchaseOrderController.MasterData> data = DealerVehiclePurchaseOrderController.getMasterData('EC15_2023_GC12N', new List<String>{'CE1'});
        Test.stopTest();

        System.assert(!data.isEmpty(), 'Master data should not be empty');
        System.assertNotEquals(null, data[0].familyValue);
    }

    @isTest
    static void testGetTotalProductPrice() {
        List<String> codes = new List<String>{'EC15_2023_GC12N'};

        Test.startTest();
        Decimal total = DealerVehiclePurchaseOrderController.getTotalProductPrice(codes);
        Test.stopTest();

        System.assertEquals(1000, total);
    }

    @isTest
    static void testGetTotalProductPrice_Exception() {
        List<String> fakeCodes = new List<String>{'XYZ123'};

        Test.startTest();
        try {
            DealerVehiclePurchaseOrderController.getTotalProductPrice(fakeCodes);
            System.assert(false, 'Expected exception not thrown');
        } catch (AuraHandledException e) {
            System.debug(e.getMessage());
        }
        Test.stopTest();
    }

    @isTest
    static void testCreateDealerPurchaseOrder() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            Id accountId = [SELECT Id FROM Account LIMIT 1]?.Id;
            Dealership_Purchase_Order__c order = new Dealership_Purchase_Order__c(Customer_Order_Number__c = 'Test', Purchase_Order_Category__c = 'Stock', Estimated_Wholesales_Amount__c = 0, Expected_Delivery_Date__c = System.Now().addDays(1), Lock_Edit__c = false);
            Dealership_Purchase_Order_Item__c item = new Dealership_Purchase_Order_Item__c(
                Exterior_Color__c = 'CE17', 
                Exterior_Color_Name__c = 'CE17', 
                Interior_Color__c = 'CI19', 
                Interior_Color_Name__c = 'CI19', 
                Model_Year__c = 'Y023', 
                Model__c = 'EC15_2023',
                Product_Code__c = 'CODE0', 
                Quantity__c = 1, 
                Unit_Price__c = 1000
            );
            List<Dealership_Purchase_Order_Item__c> items = new List<Dealership_Purchase_Order_Item__c>{item};

            Test.startTest();
            Id orderId = DealerVehiclePurchaseOrderController.createDealerPurchaseOrder(order, items);
            // Test update
            order.Id = orderId;

            Dealership_Purchase_Order_Item__c itemNew = new Dealership_Purchase_Order_Item__c(
                Exterior_Color__c = 'CE17', 
                Exterior_Color_Name__c = 'CE17', 
                Interior_Color__c = 'CI19', 
                Interior_Color_Name__c = 'CI19', 
                Model_Year__c = 'Y023', 
                Model__c = 'EC15_2023',
                Product_Code__c = 'CODE0', 
                Quantity__c = 2, 
                Unit_Price__c = 2000
            );

            DealerVehiclePurchaseOrderController.createDealerPurchaseOrder(order, new List<Dealership_Purchase_Order_Item__c>{itemNew});
            Test.stopTest();

            System.assertNotEquals(null, orderId);
        }
    }

    @isTest
    static void testGetDealerPurchaseOrder() {
        // Create order and item
        Dealership_Purchase_Order__c order = new Dealership_Purchase_Order__c(Purchase_Order_Category__c = 'Stock', Account__c = [SELECT Id FROM Account LIMIT 1]?.Id, Customer_Order_Number__c = 'Test', Status__c = 'Draft', Estimated_Wholesales_Amount__c = 0, Expected_Delivery_Date__c = System.Now().addDays(1), Lock_Edit__c = false);
        insert order;
        Dealership_Purchase_Order_Item__c item = new Dealership_Purchase_Order_Item__c(
            Dealership_Purchase_Order__c = order.Id,
            Exterior_Color__c = 'CE17', 
            Exterior_Color_Name__c = 'CE17', 
            Interior_Color__c = 'CI19', 
            Interior_Color_Name__c = 'CI19', 
            Model_Year__c = 'Y023', 
            Model__c = 'EC15_2023',
            Product_Code__c = 'CODE0', 
            Quantity__c = 1, 
            Unit_Price__c = 1000
        );
        insert item;

        Test.startTest();
        DealerVehiclePurchaseOrderController.DealerPurhaseOrderData result = DealerVehiclePurchaseOrderController.getDealerPurchaseOrder(order.Id);
        Test.stopTest();

        System.assertEquals(order.Id, result.header.Id);
        System.assertEquals(1, result.items.size());
    }

    @isTest
    static void testGetDealerPurchaseOrder_NoRecord() {
        try {
            DealerVehiclePurchaseOrderController.getDealerPurchaseOrder('001000000000000AAA');
            System.assert(false, 'Expected exception not thrown');
        } catch (DealerVehiclePurchaseOrderController.DealerPurchaseOrderException e) {
            System.assert(e.getMessage().contains('No purchase order found'));
        }
    }

    @isTest
    static void testGetSapOrderPrice() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Dealership_Purchase_Order_Item__c> orderItems = [SELECT Id, Model__c, VF_TransID__c, Product_Code__c, Exterior_Color__c, Interior_Color__c, Sunroof__c, Interior_Equipment__c FROM Dealership_Purchase_Order_Item__c LIMIT 1];
            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderPriceSapMock());
            Test.startTest();
            DealerPurchaseOrderSapHelper.GetPriceResponseWrapper result = DealerVehiclePurchaseOrderController.getSapOrderPrice(orderItems);
            Test.stopTest();
            System.assertNotEquals(null, result, 'Expected get results');
        }
    }
}