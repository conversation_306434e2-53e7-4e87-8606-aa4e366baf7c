/*****Class Description*******************
* Name           : VF_PDFPreviewController
* Test Class     : VF_PDFPreviewControllerTest
* Purpose        : Controller of Preview PDF lwc component
* Version  Author      DateModified     Description
* 1.0      thangnv12    15/04/2025       Init
* *******************************************/
public class VF_PDFPreviewController {
    /**
     * Generate PDF from Visualforce page
     * @param recordId Record Id
     * @param pdfFileName Name of the PDF file
     * @param pageUrl url to the Visualforce page (e.g. /apex/mypage?id=xxx&otherParam=yyyy)
     * @param shareType ('V' = Viewer, 'C' = Collaborator, 'I' = Inferred) 
     * @param visibility ('AllUsers', 'InternalUsers', 'SharedUsers')
     * @return ContentDocumentId
     */
    @AuraEnabled
    public static Id generateAndSavePDF(Id recordId, String pdfFileName, String pageUrl, String shareType, String visibility) {
        return VF_PDFGeneratorService.generateAndSavePDF(recordId, pdfFileName, pageUrl, shareType, visibility);
    }
}