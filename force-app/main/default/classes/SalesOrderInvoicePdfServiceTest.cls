@isTest
private class SalesOrderInvoicePdfServiceTest {
    
    @testSetup
    static void setupData() {
        // Create a sample Account
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        // Create a sample Order
        Order o = new Order(
            Name = 'Test Order',
            Description = 'Test',
            AccountId = acc.Id,
            EffectiveDate = System.today(),
            Status = 'New',
            RecordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByName().get('Booking').getRecordTypeId(),
            Pricebook2Id = Test.getStandardPricebookId()
        );
        insert o;
    }

    @isTest
    static void testGenerateProformaInvoice() {
        Order testOrder = [SELECT Id FROM Order LIMIT 1];

        Test.startTest();
        SalesOrderInvoicePdfService.Input input = new SalesOrderInvoicePdfService.Input();
        input.recordId = testOrder.Id;
        input.invoiceType = 'PIV'; 
        input.shareType = 'V';
        input.visibility = 'AllUsers';
        List<SalesOrderInvoicePdfService.Input> inputs = new List<SalesOrderInvoicePdfService.Input>{ input };

        List<SalesOrderInvoicePdfService.Output> results = SalesOrderInvoicePdfService.generate(inputs);
        Test.stopTest();
        System.assertEquals(1, results.size());
        System.assertNotEquals(null, results[0].contentDocumentId);
        System.assert(results[0].contentDocumentId.startsWith('0') || results[0].contentDocumentId.startsWith('1'));
    }

    @isTest
    static void testGenerateTaxInvoice() {
        Order testOrder = [SELECT Id FROM Order LIMIT 1];

        SalesOrderInvoicePdfService.Input input = new SalesOrderInvoicePdfService.Input();
        input.recordId = testOrder.Id;
        input.invoiceType = 'TIV';
        input.shareType = 'V';
        input.visibility = 'AllUsers';

        List<SalesOrderInvoicePdfService.Output> results = SalesOrderInvoicePdfService.generate(new List<SalesOrderInvoicePdfService.Input>{ input });

        System.assertEquals(1, results.size());
        System.assertNotEquals(null, results[0].contentDocumentId);
    }

    @isTest
    static void testGenerateInvoiceNumber() {
        Order testOrder = [SELECT Id FROM Order LIMIT 1];
        SalesOrderInvoicePdfService.Input input = new SalesOrderInvoicePdfService.Input();
        input.recordId = testOrder.Id;
        input.invoiceType = 'TIV';
        input.shareType = 'V';
        input.visibility = 'AllUsers';

        List<SalesOrderInvoicePdfService.Output> results = SalesOrderInvoicePdfService.generate(new List<SalesOrderInvoicePdfService.Input>{ input });
        String invoiceNumber = SalesOrderInvoicePdfService.generateInvoiceNumber(testOrder.Id, 'TIV');
        System.assertEquals(true, invoiceNumber.endsWith('-002'));
    }

    @isTest
    static void testCreateInvoiceHistory() {
        Order testOrder = [SELECT Id FROM Order LIMIT 1];

        SalesOrderInvoicePdfService.Input input = new SalesOrderInvoicePdfService.Input();
        input.recordId = testOrder.Id;
        input.invoiceType = 'TIV';
        input.shareType = 'V';
        input.visibility = 'AllUsers';

        List<SalesOrderInvoicePdfService.Output> outputs = SalesOrderInvoicePdfService.generate(new List<SalesOrderInvoicePdfService.Input>{ input });

        String hisId = SalesOrderInvoicePdfService.createOrderInvoiceHistory(testOrder.Id, 'Test', 'TIV', outputs[0].contentDocumentId);
        System.assertNotEquals(null, hisId);
    }
}