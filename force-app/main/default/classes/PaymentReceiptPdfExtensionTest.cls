@isTest
public class PaymentReceiptPdfExtensionTest {

    @isTest
    static void testExtensionMethods() {
        // Create Account
        Account acc = new Account(
            Name = 'Test Account',
            BillingStreet = '123 Test St',
            BillingCity = 'Mumbai',
            BillingState = 'Assam',
            BillingCountry = 'India',
            BillingCountryCode = 'IN',
            BillingPostalCode = '400002',
            VF_IdentificationNo__c = 'GST12345678',
            Phone = '*********',
            VF_Hotline__c = '*********',
            AccountNumber = 'A123456'
        );
        insert acc;

        // Create a sample Order
        Order ord = new Order(
            Name = 'Test Order',
            Description = 'Test',
            AccountId = acc.Id,
            EffectiveDate = System.today(),
            Status = 'New',
            RecordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByName().get('Booking').getRecordTypeId(),
            Pricebook2Id = Test.getStandardPricebookId(),
            PAN__c = 'PAN*********',
            Vehicle_Information__c = 'Car Model X'
        );
        insert ord;

        // Create VF_PaymentInformation__c
        VF_PaymentInformation__c payment = new VF_PaymentInformation__c(
            Issued_Date__c = Date.today(),
            Receipt_Amount__c = 1234.56,
            Bank_Name__c = 'Test Bank',
            Payment_Method__c = 'Cash',
            Transaction_ID__c = 'TX123456',
            Transaction_Date__c = Datetime.now().addDays(-1),
            Payment_Type__c = 'Booking',
            VF_Order__c = ord.Id
        );
        insert payment;

        Test.startTest();
        // Setup VF Page parameters if needed
        ApexPages.currentPage().getParameters().put('currencyCode', 'USD');

        ApexPages.StandardController stdCtrl = new ApexPages.StandardController(payment);
        PaymentReceiptPdfExtension ext = new PaymentReceiptPdfExtension(stdCtrl);

        // Call methods
        String address = ext.getAccountBillingAddress();
        String gstNo = ext.getGstNo();
        String phone = ext.getPhone();
        String paymentDate = ext.getPaymentDate();
        String transactionDate = ext.getTransactionDate();
        String totalAmount = ext.getTotalAmount();
        String amountWords = ext.getTotalAmountWords();
        String formatted = ext.formatDecimal(100.00);
        Test.stopTest();

        // Assertions
        System.assertNotEquals(null, address, 'Billing address should not be null');
        System.assertEquals('GST12345678', gstNo, 'GST should prefer Identification No');
        System.assertEquals('*********', phone, 'Should pick Phone over Hotline');
        System.assertNotEquals(null, paymentDate, 'Payment Date should be formatted');
        System.assertNotEquals(null, transactionDate, 'Transaction Date should be formatted');
        System.assertEquals('1,234.56', totalAmount, 'Total amount should be formatted');
        System.assertNotEquals(null, amountWords, 'Amount in words should be returned');
        System.assertEquals('100', formatted, 'Formatted value should strip trailing zeros');
    }
}