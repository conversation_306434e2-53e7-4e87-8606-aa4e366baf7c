public class VF_CurrencyToWordsIndianHindi {
    static final String[] units = new String[]{
        '', 'एक', 'दो', 'तीन', 'चार', 'पांच', 'छह', 'सात', 'आठ', 'नौ', 'दस',
        'ग्यारह', 'बारह', 'तेरह', 'चौदह', 'पंद्रह', 'सोलह', 'सत्रह', 'अठारह', 'उन्नीस'
    };

    static final String[] tens = new String[]{
        '', '', 'बीस', 'तीस', 'चालीस', 'पचास', 'साठ', 'सत्तर', 'अस्सी', 'नब्बे'
    };

    private static List<Long> scales = new List<Long>{
        1000000000000000000L, // Quintillion
        1000000000000000L,    // Quadrillion
        1000000000000L,       // Trillion
        1000000000L,          // Billion
        10000000L,            // Crore (Indian system)
        100000L,              // Lakh
        1000L,                // Thousand
        100L                  // Hundred
    };
    
    private static List<String> scaleNames = new List<String>{
        ' शंख',     // Quintillion (Hindi name)
        ' पद्म',     // Quadrillion
        ' नील',     // Trillion
        ' खरब',     // Billion
        ' करोड़',   // Crore (Indian)
        ' लाख',     // Lakh
        ' हज़ार',    // Thousand
        ' सौ'       // Hundred
    };
    public static String convert(Decimal amount) {
        if (amount == 0) return 'शून्य';

        String result = '';

        if (amount < 0) {
            result += 'ऋण ';
            amount = amount.abs();
        }

        Long intPart = amount.longValue();
        Decimal decimalPart = amount - intPart;

        result += convertInteger(intPart);

        if (decimalPart > 0) {
            String decimalStr = String.valueOf(decimalPart).substringAfter('.');
            result += ' दशमलव';
            for (Integer i = 0; i < decimalStr.length(); i++) {
                Integer digit = Integer.valueOf(decimalStr.substring(i, i+1));
                result += ' ' + units[digit];
            }
        }

        return result.trim();
    }

    private static String convertInteger(Long num) {
        if (num == 0) return '';

        String words = '';

        for (Integer i = 0; i < scales.size(); i++) {
            Long scale = scales[i];
            if (num >= scale) {
                words += convertInteger(num / scale) + scaleNames[i] + ' ';
                num = Math.mod(num, scale);
            }
        }

        if (num > 0) {
            if (num < 20) {
                words += units[(Integer)num];
            } else {
                words += tens[(Integer)(num / 10)];
                Integer unit = (Integer)Math.mod(num, 10);
                if (unit > 0) {
                    words += ' ' + units[unit];
                }
            }
        }

        return words.trim();
    }
}