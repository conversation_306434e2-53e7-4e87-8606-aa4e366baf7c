@isTest
public with sharing class OrderTaxesControllerTest {

    // Mock class for successful HTTP response
    private class MockHttpSuccessResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{"result":{"status":"S","message":"Success"},"data":{"items":[{"priceObject":[{"priceType":"JTCB","priceValue":100000.0},{"priceType":"JOSG","priceAmount":9.0,"priceValue":9000.0},{"priceType":"JOCG","priceAmount":9.0,"priceValue":9000.0},{"priceType":"JOIG","priceAmount":9.0,"priceValue":9000.0},{"priceType":"JOUG","priceAmount":9.0,"priceValue":9000.0},{"priceType":"JTC1","priceAmount":9.0,"priceValue":9000.0}]}]}}');
            return res;
        }
    }

    // Mock class for failed HTTP response
    private class MockHttpFailedResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(400);
            res.setBody('{"result":{"status":"E","message":"Error occurred"}}');
            return res;
        }
    }

    @testSetup
    static void setupTestData() {
        // Create test Account
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        // Create test Order
        Order testOrder = new Order(
            AccountId = testAccount.Id,
            Status = 'New',
            EffectiveDate = Date.today(),
            BillingStateCode = 'KA',
            VF_Discount_Amount__c = 5000,
            Pricebook2Id = Test.getStandardPricebookId()
        );
        insert testOrder;

        // Create test Order Items
        Product2 product = new Product2(Name = 'Test Product', ProductCode = 'P001', IsActive = true);
        insert product;

        PricebookEntry pricebookEntry = new PricebookEntry(
            Product2Id = product.Id,
            Pricebook2Id = Test.getStandardPricebookId(),
            UnitPrice = 100000,
            IsActive = true
        );
        insert pricebookEntry;

        OrderItem orderItem = new OrderItem(
            OrderId = testOrder.Id,
            PricebookEntryId = pricebookEntry.Id,
            Quantity = 1,
            UnitPrice = 100000,
            Order_Product_Family_Code__c = 'MOD'
        );
        insert orderItem;
    }

    @isTest
    static void testGetOrderTaxesFromSAP_Success() {
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Fetch test Order
        Order testOrder = [SELECT Id FROM Order LIMIT 1];

        // Call the method
        Test.startTest();
        String result = OrderTaxesController.getOrderTaxesFromSAP(testOrder.Id);
        Test.stopTest();

        // Assert results
        Order updatedOrder = [SELECT Vehicle_Ex_Showroom_Price__c, SGST_Rate__c, SGST_Amount__c, CGST_Rate__c, CGST_Amount__c FROM Order WHERE Id = :testOrder.Id];
        System.assertEquals(100000.0, updatedOrder.Vehicle_Ex_Showroom_Price__c, 'Vehicle Ex-Showroom Price should be updated');
        System.assertEquals(9.0, updatedOrder.SGST_Rate__c, 'SGST Rate should be updated');
        System.assertEquals(9000.0, updatedOrder.SGST_Amount__c, 'SGST Amount should be updated');
        System.assertEquals(9.0, updatedOrder.CGST_Rate__c, 'CGST Rate should be updated');
        System.assertEquals(9000.0, updatedOrder.CGST_Amount__c, 'CGST Amount should be updated');
    }

    @isTest
    static void testGetOrderTaxesFromSAP_Failure() {
        Test.setMock(HttpCalloutMock.class, new MockHttpFailedResponse());

        // Fetch test Order
        Order testOrder = [SELECT Id FROM Order LIMIT 1];

        // Call the method and expect an exception
        Test.startTest();
        try {
            OrderTaxesController.getOrderTaxesFromSAP(testOrder.Id);
            System.assert(false, 'Exception should have been thrown');
        } catch (AuraHandledException ex) {
            System.assert(ex.getMessage().contains('Script-thrown exception'), 'Exception message should indicate failure');
        }
        Test.stopTest();
    }

    @isTest
    static void testGetOrderTaxesFromSAP_InvalidStatus() {
        // Update Order status to something other than 'New'
        Order testOrder = [SELECT Id, Status FROM Order LIMIT 1];
        testOrder.Status = 'Confirmed';
        update testOrder;

        // Call the method
        Test.startTest();
        String result = OrderTaxesController.getOrderTaxesFromSAP(testOrder.Id);
        Test.stopTest();

        // Assert results
        System.assertEquals('The Order status cannot be sent to SAP.', result, 'The response message should indicate invalid status');
    }
}
