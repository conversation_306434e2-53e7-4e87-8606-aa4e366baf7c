/*****Class Description*******************
* Name           : DealerPurchaseOrderSapHelper
* Test Class     : DealerPurchaseOrderSapHelperTest
* Purpose        : Provide helper methods to interact with SAP
* Except         : Use without sharing to allow Dealer to query Account parent's Data (bypass sharing)
* Version  Author      DateModified     Description
* 1.0      thangnv12    16/04/2025       Init
* *******************************************/
public without sharing class DealerPurchaseOrderSapHelper {
    private static final String SAP_GET_PRICE_URL = 'callout:MuleSoft_Credential/dms/api/v1/taxes-pricing';
    private static final String SAP_CREATE_SO_URL = 'callout:MuleSoft_Credential/dms/api/v1/saleorder';
    /**
     * Get Part Order Price from SAP
     * @param items Order Item
     * @return GetPriceResponseWrapper
     */
    public static GetPriceResponseWrapper getPartOrderPriceFromSAP(List<OrderItem> items) {
        String requestBody = buildPartPayloadGetPrice(items);
        System.debug('Request Payload:' + requestBody);
        return makeCalloutGetPrice(requestBody);
    }

    /**
     * Get Vehicle Order Pirce from SAP
     * @param items Order Item
     * @return GetPriceResponseWrapper
     */
    public static GetPriceResponseWrapper getVehicleOrderPriceFromSAP(List<Dealership_Purchase_Order_Item__c> items) {
        String requestBody = buildVehiclePayloadGetPrice(items);
        System.debug('Request Payload:' + requestBody);
        return makeCalloutGetPrice(requestBody);
    }

    /**
     * Make Callout to SAP to Get Wholesales price
     * @param requestBody
     * @return GetPriceResponseWrapper
     */
    public static GetPriceResponseWrapper makeCalloutGetPrice(String requestBody){
        HttpResponse response = VF_MakeCalloutGeneric.makeCallout(
            SAP_GET_PRICE_URL, 'POST',
            new Map<String, String>{ 'Content-Type' => 'application/json' },
            requestBody
        );
        System.debug('Response Body: ' + response.getBody());
        VF_Utility.createOutboundLog('SAP_PURCHASEORDER_GET_PRICE', 'POST', requestBody, response, null);
        return (GetPriceResponseWrapper) JSON.deserialize(response.getBody(), GetPriceResponseWrapper.class);
    }

    /**
     * Build Payload for Vehicle Order
     * @param List<Dealership_Purchase_Order_Item__c> orderItems
     * @return String json body
     */
    public static String buildVehiclePayloadGetPrice(List<Dealership_Purchase_Order_Item__c> orderItems) {
        Map<String, Object> header = buildGetPurchasePriceHeader('Vehicle');
        Set<String> allProductCodes = new Set<String>();
        for (Dealership_Purchase_Order_Item__c poItem : orderItems) {
            String productCode = poItem.Model__c + '_' + poItem.Product_Code__c;
            Set<String> productCodes = new Set<String>{productCode};
            if (poItem.Exterior_Color__c != null) {
                productCodes.add(productCode + '_' + poItem.Exterior_Color__c);
            }
            if (poItem.Interior_Color__c != null) {
                productCodes.add(productCode + '_' + poItem.Interior_Color__c);
            }
            if (poItem.Sunroof__c != null) {
                productCodes.add(productCode + '_' + poItem.Sunroof__c);
            }
            if (poItem.Interior_Equipment__c != null) {
                productCodes.add(productCode + '_' + poItem.Interior_Equipment__c);
            }
            allProductCodes.addAll(productCodes);
        }

        Id standardPriceBookId = Test.isRunningTest() ? Test.getStandardPricebookId() : [SELECT Id FROM Pricebook2 WHERE IsStandard = TRUE LIMIT 1]?.Id;
        // Query Products 
        Map<String, Id> productCodeToProductId = new Map<String, Id>();
        for (Product2 p : [SELECT Id, VF_ProductId__c FROM Product2 WHERE VF_ProductId__c IN :allProductCodes AND IsActive = true]) {
            productCodeToProductId.put(p.VF_ProductId__c, p.Id);
        }
        // Query PricebookEntries
        Map<Id, PricebookEntry> productIdToEntry = new Map<Id, PricebookEntry>();
        for (PricebookEntry pbe : [SELECT Id, toLabel(Product2.VF_Trim__c), toLabel(Product2.VF_Model__c), Product2Id, VF_UOM__c, UnitPrice FROM PricebookEntry WHERE Pricebook2Id = :standardPricebookId AND ProductCode IN :allProductCodes AND IsActive = true
        ]) {
            productIdToEntry.put(pbe.Product2Id, pbe);
        }

        // build items
        List<Map<String, Object>> items = new List<Map<String, Object>>();
        for (Dealership_Purchase_Order_Item__c oItem : orderItems) {
            Map<String, Object> itm = new Map<String, Object>();
            String productCode = oItem.Model__c + '_' + oItem.Product_Code__c;
            Id productId = productCodeToProductId.get(productCode);
            if (productId == null) throw new DealerPurchaseOrderSapException('The specified product could not be found: ' + productCode);
            PricebookEntry entry = productIdToEntry.get(productId);
            String productDesc = entry.Product2.VF_Model__c + ' ' + entry.Product2.VF_Trim__c;
            if (entry == null) throw new DealerPurchaseOrderSapException('The price of the specified product could not be found: ' + productDesc);
            if (entry.VF_UOM__c == null) throw new DealerPurchaseOrderSapException('The UOM of the specified product could not be found: ' + productDesc);
            itm.put('quantity', 1);
            itm.put('billClassification', 'D');
            itm.put('transactionItem', oItem.VF_TransID__c);
            itm.put('productCode', productCode);
            itm.put('productType', VF_Constants.SAP_TAX_PRODUCT_TYPE.get('Vehicle'));
            itm.put('UOM', entry.VF_UOM__c);

            Map<String, String> familyAndCode = new Map<String, String>{
                'MOD' => oItem.Product_Code__c,
                'R15' => oItem.Sunroof__c,
                'D32' => oItem.Interior_Equipment__c,
                'CE1' => oItem.Exterior_Color__c,
                'CI1' => oItem.Interior_Color__c
            };
                
            List<Map<String, Object>> vehicleOptions = new List<Map<String, Object>>(); 
            for (String familyCode : familyAndCode.keySet()) {
                if (String.isNotBlank(familyAndCode.get(familyCode))) {
                    vehicleOptions.add(new Map<String, Object>{
                        'itemID' => oItem.VF_TransID__c,
                        'productFamily' => familyCode,
                        'familyValue' => familyAndCode.get(familyCode)
                    });
                }
            }
            itm.put('vehicleOptions', vehicleOptions);
            items.add(itm);
        }
        header.put('items', items);
        return JSON.serialize(header);
    }

    /**
     * Build payload for part order
     * @param List<OrderItem> orderItems
     * @return String json body
     */
    public static String buildPartPayloadGetPrice(List<OrderItem> orderItems) {
        Map<String, Object> header = buildGetPurchasePriceHeader('Part');
        
        Set<String> allProductIds = new Set<String>();
        Set<String> allPricebookEntryIds = new Set<String>();
        for (OrderItem poItem : orderItems) {
            allProductIds.add(poItem.Product2Id);
            allPricebookEntryIds.add(poItem.PricebookEntryId);
        }

        Id standardPriceBookId = Test.isRunningTest() ? Test.getStandardPricebookId() : [SELECT Id FROM Pricebook2 WHERE IsStandard = TRUE LIMIT 1]?.Id;
        // Query Products 
        Map<Id, Product2> productMap = new Map<Id, Product2>([SELECT Id, VF_ProductId__c FROM Product2 WHERE Id IN :allProductIds AND IsActive = true]);

        Map<Id, PricebookEntry> pricebookEntryMap = new Map<Id, PricebookEntry>([SELECT Id, Product2.Name, Product2.Description, Product2Id, VF_UOM__c, UnitPrice FROM PricebookEntry WHERE Id IN :allPricebookEntryIds AND IsActive = true]);

        // build items
        List<Map<String, Object>> items = new List<Map<String, Object>>();
        for (OrderItem oItem : orderItems) {
            Product2 prod = productMap.get(oItem.Product2Id);
            String productCode = prod.VF_ProductId__c;
            PricebookEntry entry = pricebookEntryMap.get(oItem.PricebookEntryId);
            if (entry.VF_UOM__c == null) {
                throw new DealerPurchaseOrderSapException('The UOM of the specified product could not be found: ' + entry.Product2.Description + ' ('+entry.Product2.Name+')');
            }
            Map<String, Object> itm = new Map<String, Object>();
            itm.put('quantity', oItem.Quantity);
            itm.put('billClassification', 'D');
            itm.put('transactionItem', oItem.VF_TransID__c);
            itm.put('productCode', productCode);
            itm.put('productType', VF_Constants.SAP_TAX_PRODUCT_TYPE.get('Part'));
            itm.put('UOM', entry.VF_UOM__c);
            items.add(itm);
        }
        header.put('items', items);
        return JSON.serialize(header);
    }

    /**
     * Build header for purchase order
     * @param String orderType Order type
     * @return Map<String, Object> header
     */
    public static Map<String, Object> buildGetPurchasePriceHeader(String orderType) {
        Map<String, Object> header = new Map<String, Object>();
        User u = [SELECT AccountId FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        Id accountId = u.AccountId;
        
        Account dealerAccount = [SELECT Id, VF_DealerCode__c, ParentId, Parent.Plant_Vehicle__c, Parent.Plant_Part__c, BillingStateCode, BillingCountryCode FROM Account WHERE Id = :accountId LIMIT 1];
        Map<String, String> stateSAPCode = new Map<String, String>();
        Map<String, State_Masterdata__mdt> mapStateMetadata = State_Masterdata__mdt.getAll();
        for(String mapKey : mapStateMetadata.keySet()) {
            stateSAPCode.put(mapStateMetadata.get(mapKey).State_Code__c, mapStateMetadata.get(mapKey).State_Code_SAP__c);
        }

        DateTime currentDateTime = DateTime.now();
        String timestamp = String.valueOf(System.currentTimeMillis());
        header.put('requestID', String.format('{0}_{1}', new List<String>{u.Id, timestamp}));

        String transactionId = timestamp + UserInfo.getUserId(); // make temp transaction id
        transactionId = transactionId.substring(0, 20);

        header.put('transactionID', transactionId);
        header.put('transactionType', '3'); // default for Purchase Order
        String formatDate = 
            String.valueOf(currentDateTime.day()).leftPad(2, '0') + '.' +
            String.valueOf(currentDateTime.month()).leftPad(2, '0') + '.' +
            String.valueOf(currentDateTime.year());
        header.put('transactionDate', formatDate); // format dd.mm.yyyy
        header.put('buyerLocation', stateSAPCode.get(dealerAccount.BillingStateCode));
        header.put('sellerLocation', orderType != null && orderType == 'Vehicle' ? dealerAccount.Parent.Plant_Vehicle__c : dealerAccount.Parent.Plant_Part__c);
        header.put('currency', UserInfo.getDefaultCurrency());
        header.put('country', dealerAccount.BillingCountryCode);
        return header;
    }

    public static SyncOrderResult makeCalloutSyncOrder(Order ord) {
        // build body 
        String requestBody = JSON.serialize(new SyncOrderPayload(ord));

        String errorMessage;
        Order orderToUpdate = new Order(Id=ord.Id, Lock_Edit__c = false);
        HttpResponse res;
        try {
            res = VF_MakeCalloutGeneric.makeCallout(SAP_CREATE_SO_URL, 'POST',
                new Map<String, String>{ 'Content-Type' => 'application/json' },
                requestBody
            );
            System.debug('Response Body: ' + res.getBody());
            SyncOrderResponseWrapper resp = (SyncOrderResponseWrapper) JSON.deserialize(res.getBody(), SyncOrderResponseWrapper.class);
            if (res.getStatusCode() == 200 && resp.result.status == 'S') {
                orderToUpdate.Sync2SAP__c = true;
                orderToUpdate.Status = 'Confirmed';
            } else {
                errorMessage = 'Error: ' + res.getStatusCode() + ' - ' + res.getStatus();
                if (resp != null && resp.result != null && resp.result.message != null){
                    errorMessage = 'Error: ' + resp.result.status + ' - ' + resp.result.message;
                }
                orderToUpdate.Sync2SAP__c = false;
                orderToUpdate.SAP_Response_message__c = errorMessage;
            }
            orderToUpdate.Sync2SAP_Date__c = System.now();
        } catch (Exception ex) {
            errorMessage = 'Exception: ' + ex.getMessage() + '.Stack Trace: \n' + ex.getStackTraceString();
            System.debug('errorMessage: ' + errorMessage);
            orderToUpdate.Sync2SAP__c = false;
            orderToUpdate.SAP_Response_message__c = errorMessage;
        }
        Outbound_Log__c log = VF_Utility.buildOutboundLog(SAP_CREATE_SO_URL, 'POST', requestBody, res, errorMessage);
        log.Related_Record__c = ord.Id;

        return new SyncOrderResult(orderToUpdate, log);
    }

    public class SyncOrderResult {
        public Order order;
        public Outbound_Log__c log;
        public SyncOrderResult(Order ord, Outbound_Log__c log) {
            this.order = ord;
            this.log = log;
        }
    }

    public class GetPriceResponseWrapper {
        @AuraEnabled public cls_result result {get; set;}
        @AuraEnabled public cls_data data {get; set;}
    }

    public class cls_result {
		@AuraEnabled public String code;
		@AuraEnabled public String message;
		@AuraEnabled public String status;
    }
    public class cls_data {
		@AuraEnabled public String transactionID;
		@AuraEnabled public Integer transactionType;
		@AuraEnabled public cls_items[] items;
    }
    public class cls_items {
		@AuraEnabled public String transactionItemID;
		@AuraEnabled public String productCode;
		@AuraEnabled public Decimal quantity;
		@AuraEnabled public cls_priceObject[] priceObject;
	}
    
	public class cls_priceObject {
		@AuraEnabled public String priceType;
		@AuraEnabled public Decimal priceAmount;
		@AuraEnabled public Decimal priceValue;
		@AuraEnabled public Decimal unitPerPrice;
	}

    public class SyncOrderPayload {
        public String market;
        public String salesOrderType;
        public String division;
        public String customerID;
        public String referencePONumber;
        public String referencePODate; //dd.mm.yyyy
        public String requestedDeliveryDate; //dd.mm.yyyy
        public String shipTo;
        public List<SyncOrderItem> purchaseOrderDetails;
        public SyncOrderPayload(Order ord){
            //  build header
            this.market = ord.BillingCountryCode;
            this.salesOrderType = 'ZVOR';
            this.division = ord.RecordType.Name == 'Dealer Part Purchase' ? '50' : '20';
            this.customerID = ord.Account.VF_Level__c == 'Dealer Company' ? ord.Account.VF_DealerCode__c : ord.Account.Parent.VF_DealerCode__c;
            this.referencePONumber = ord.OrderNumber;
            this.referencePODate = VF_Utility.formatDateTimeToDDMMYYYY(ord.CreatedDate);
            this.requestedDeliveryDate = VF_Utility.formatDateTimeToDDMMYYYY(ord.Expected_Delivery_Date__c);
            this.shipTo = ord.Account.VF_DealerCode__c;

            // build items and characters (for vehicle)
            this.purchaseOrderDetails = new List<SyncOrderItem>();
            String priority = getSapPriority(ord.Purchase_Order_Category__c);
            List<OrderItem> orderItems = ord.OrderItems;

            if (ord.RecordType.Name == 'Dealer Vehicle Purchase') {
                List<OrderItem> products = new List<OrderItem>();
                List<OrderItem> characters = new List<OrderItem>();
                for (OrderItem ordItem : orderItems) {
                    if (ordItem.Product2.Family == 'MOD') {
                        products.add(ordItem);
                    } else {
                        characters.add(ordItem);
                    }
                }
                for (OrderItem prod : products) {
                    List<OrderItem> chars = new List<OrderItem>();
                    chars.add(prod);
                    for (OrderItem character : characters) {
                        if (character.Product2.VF_Model__c == prod.Product2.VF_Model__c && character.Product2.VF_Trim__c == prod.Product2.VF_Trim__c) {
                            chars.add(character);
                        }
                    }
                    SyncOrderItem item = new SyncOrderItem(prod, chars);
                    item.plant = ord.Account.Parent.Plant_Vehicle__c;
                    item.deliveryPriority = priority;
                    this.purchaseOrderDetails.add(item);
                }
            } else {
                for (OrderItem prod : orderItems) {
                    SyncOrderItem item = new SyncOrderItem(prod, null);
                    item.plant = ord.Account.Parent.Plant_Part__c;
                    item.deliveryPriority = priority;
                    this.purchaseOrderDetails.add(item);
                }
            }
        }
    }

    public class SyncOrderItem {
        public String orderItemNumber;
        public String plant;
        public String material;
        public Decimal quantity;
        public String deliveryPriority;
        public String UOM;
        List<SyncOrderItemCharacter> purchaseOrderDetailCharacteristics;
        public SyncOrderItem(OrderItem ordItem, List<OrderItem> characters) {
            this.orderItemNumber = ordItem.OrderItemNumber;
            this.material = ordItem.Product2.VF_ProductId__c;
            this.quantity = ordItem.Quantity;
            this.UOM = ordItem.PricebookEntry.VF_UOM__c;
            this.purchaseOrderDetailCharacteristics = new List<SyncOrderItemCharacter>();
            if (characters != null) {
                for (OrderItem character : characters) {
                    SyncOrderItemCharacter ch = new SyncOrderItemCharacter(character);
                    ch.orderItemNumber = ordItem.OrderItemNumber;
                    this.purchaseOrderDetailCharacteristics.add(ch);
                }
            }
        }

        public SyncOrderItem(OrderItem ordItem) {
            this(ordItem, null);
        }
    }

    public class SyncOrderItemCharacter {
        public String orderItemNumber;
        public String familyOption;
        public String familyValue;
        public SyncOrderItemCharacter(OrderItem ordItem) {
            this.familyOption = ordItem.Product2.VF_Model__c + '_' + ordItem.Product2.Family;
            this.familyValue = ordItem.Product2.VF_FamilyValue__c;
        }
    }

    public static String getSapPriority(string category) {
        switch on category {
            when 'Stock' {
                return '03';
            }
            when 'Customer' {
                return '06';
            }
            when 'VOR' {
                return '01';
            }
            when 'Urgent' {
                return '02';
            }
            when else {
                throw new DealerPurchaseOrderSapException('Invalid Purchase Order Category');
            }
        }
    }

    public class SyncOrderResponseWrapper {
        public SyncOrderSAPResult result {get; set;}
    }

    public class SyncOrderSAPResult {
        public String code;
        public String message;
        public String status;
        public String description;
        public Integer numberOfRecords;
    }

    public class DealerPurchaseOrderSapException extends Exception{}
}