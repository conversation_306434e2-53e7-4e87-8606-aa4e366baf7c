/*****Class Description*******************
* Name           : RepairOrderInventoryController
* Test Class     : RepairOrderInventoryControllerTest
* Purpose        : Handle Goods Issue/Receive to SAP
* Modification History  :
* Version  Author      DateModified     Description
* 1.0      NinhNN      11/04/2025       Init
* *******************************************/
@isTest
public class RepairOrderInventoryControllerTest {
    // Step 1: Create Mock Response
    class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HTTPResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setStatusCode(200);
            String t_res = '{"result":{"code":"S000","message":"Material document ********** has been created","status":"S","description":"","number_of_records":0},"data":{"status":"S","message":"Material document ********** has been created","SAPDoc":"**************"}}';
            String mockResponse = t_res;            
            res.setBody(mockResponse);
            return res;
        }
    }
    
    @isTest
    static void testProcessGIGR() {
        Account acc = new Account(Name='Test Account', BillingStateCode='DL', BillingCountryCode='IN', VF_DealerCode__c='D123');
        insert acc;
        Contact con = new Contact(LastName='Tester', AccountId=acc.Id);
        insert con;
        Pricebook2 pb = new Pricebook2();
        pb.Id = Test.getStandardPricebookId();
        WorkOrder wo = new WorkOrder(
            Subject = 'Test WO',
            Status = 'New',
            ContactId = con.Id,
            AccountId = acc.Id,
            Pricebook2Id = pb.Id
        );
        insert wo;
        Concern__c cc = new Concern__c();
        cc.Work_Order__c = wo.id;
        cc.Bill_classification__c = 'C';
        Insert cc;
        WorkType wt = new WorkType(Name='Test WorkType', EstimatedDuration=1, External_Id__c='EXT123', DurationType='Hours');
        insert wt;
        WorkOrderLineItem woli = new WorkOrderLineItem(
            WorkOrderId = wo.Id,
            WorkTypeId = wt.Id,
            Classification__c = 'C',
            Concern__c = cc.Id
        );
        insert woli;
        Product2 prod = new Product2(Name='Test Product', ProductCode='P123', QuantityUnitOfMeasure='EA', IsActive=true);
        insert prod;
        PricebookEntry pbe = new PricebookEntry();
        pbe.Product2Id = prod.id;
        pbe.Pricebook2Id = pb.id;
        pbe.IsActive = true;
        pbe.UnitPrice = 50;
       	Insert pbe;
        ProductConsumed pc = new ProductConsumed(
            WorkOrderId = wo.Id,
			PricebookentryId = pbe.id,            
            Request_Quantity__c = 5,
            Classification__c = 'C',
            Concern__c = cc.Id,
            QuantityConsumed = 5
        );
        insert pc;
        
        Inventory_Transaction__c it = new Inventory_Transaction__c(
            Product_Consumed__c = pc.Id,
            Quantity__c = 2,
            Type__c = 'Out'
        );
        
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        
        Test.startTest();
        List<List<Inventory_Transaction__c>> input = new List<List<Inventory_Transaction__c>>{ new List<Inventory_Transaction__c>{ it } };
        List<String> results = RepairOrderInventoryController.Process_GIGR(input);       
        Test.stopTest();
    }
}