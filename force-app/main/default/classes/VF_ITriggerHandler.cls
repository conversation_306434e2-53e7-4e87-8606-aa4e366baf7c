/**********************************************************************
Name:  VF_ITriggerHandler
Developed by: TCS
=======================================================================
Req:  - Use Trigger Framework
=======================================================================
Purpose:                                                            
This is an interface. It provides a framework to all Trigger
Handlers. All trigger handlers should implement this interface.
=======================================================================
History                                                            
-------                                                            
VERSION  AUTHOR            DATE              DETAIL
1.0 -    <PERSON>il <PERSON>hare        09/06/2021      Initial Development
***********************************************************************/

public interface VF_ITriggerHandler {
	void beforeInsert(List<SObject> newItems);
    void beforeUpdate(Map<Id, SObject> newItems, Map<Id, SObject> oldItems);
    void beforeDelete(Map<Id, SObject> oldItems);
    void afterInsert(Map<Id, SObject> newItems);
    void afterUpdate(Map<Id, SObject> newItems, Map<Id, SObject> oldItems);
    void afterDelete(Map<Id, SObject> oldItems);
    void afterUndelete(Map<Id, SObject> oldItems);
    Boolean isDisabled();
}