@isTest
private class VF_PDFGeneratorServiceTest {

    @isTest static void testConvertWithValidInput() {
        // Create a dummy Account record to use as recordId
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        // Prepare input
        VF_PDFGeneratorService.Input input = new VF_PDFGeneratorService.Input();
        input.recordId = acc.Id;
        input.pdfFileName = 'TestFile';
        input.pageUrl = '/apex/vfDummyPdfPage?id=' + acc.Id;
        input.shareType = 'V';
        input.visibility = 'InternalUsers';

        List<VF_PDFGeneratorService.Input> inputs = new List<VF_PDFGeneratorService.Input>{ input };

        Test.startTest();
        List<VF_PDFGeneratorService.Output> outputs = VF_PDFGeneratorService.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, outputs.size());
        System.assertNotEquals(null, outputs[0].contentDocumentId);
    }

    @isTest static void testFileNameWithExtension() {
        Account acc = new Account(Name = 'PDF Extension Account');
        insert acc;

        VF_PDFGeneratorService.Input input = new VF_PDFGeneratorService.Input();
        input.recordId = acc.Id;
        input.pdfFileName = 'Invoice.pdf';
        input.pageUrl = '/apex/vfDummyPdfPage?id=' + acc.Id;

        List<VF_PDFGeneratorService.Input> inputs = new List<VF_PDFGeneratorService.Input>{ input };
        List<VF_PDFGeneratorService.Output> outputs= VF_PDFGeneratorService.convert(inputs);
        System.assertEquals(1, outputs.size());
        System.assertNotEquals(null, outputs[0].contentDocumentId);
    }

    @isTest static void testMissingPageUrl() {
        VF_PDFGeneratorService.Input input = new VF_PDFGeneratorService.Input();
        input.recordId = '001XXXXXXXXXXXX'; // dummy id
        input.pdfFileName = 'MissingPage';
        input.pageUrl = null;

        List<VF_PDFGeneratorService.Input> inputs = new List<VF_PDFGeneratorService.Input>{ input };

        try {
            VF_PDFGeneratorService.convert(inputs);
        } catch (Exception e) {
            System.debug(e.getMessage());
        }
    }
}