@isTest
private class VF_OrderRestAPITest {

    @testSetup static void setupTestData() {
        Id dealerRecordType =  Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Dealer').getRecordTypeId();
        Account dealer = new Account(Name = 'Test Dealer', RecordTypeId = dealerRecordType,  VF_Level__c = 'Dealer Company');
        insert dealer;

        Account businessUnit = new Account(Name = 'Test Business Unit', ParentId = dealer.Id, RecordTypeId = dealerRecordType);
        insert businessUnit;

        Account customer = new Account(LastName = 'Test Customer', Phone = '**********', VF_Business_Unit__c = dealer.Id, RecordTypeId = VF_Constants.B2C_ACCOUNT_RECORDTYPE);
        insert customer;

        Pricebook2 standardPB = new Pricebook2(
            Id = Test.getStandardPricebookId(),
            IsActive = true
        );
        update standardPB;

        Product2 product = new Product2(Name = 'Test Vehicle Product', ProductCode = 'EC15_2023_GC15N', IsActive = true);
        insert product;

        PricebookEntry pbe = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = product.Id, UnitPrice = 100, IsActive = true);
        insert pbe;

        // Option
        Product2 productOption = new Product2(Name = 'Test Options', ProductCode = 'EC15_2023_GC15N_CE11', IsActive = true);
        insert productOption;

        PricebookEntry pbeOption = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = productOption.Id, UnitPrice = 125, IsActive = true);
        insert pbeOption;

        Accessory_Kit__c kit = new Accessory_Kit__c(Kit_Name__c = 'AK11', Kit_Family_Code__c = 'AK1', Kit_Pricing__c = 17000, Active__c = true);
        insert kit;

        VF_OrderRestAPI.ProductLineItems lineCover = new VF_OrderRestAPI.ProductLineItems();
        lineCover.id = '1';
        lineCover.name = 'EC15_2023_GC15N';
        lineCover.quantity = 1;
        lineCover.price = 100;
    }

    @isTest
    static void testDoPost_Success() {
       
        Account dealer = [SELECT ID FROM Account WHERE Name = 'Test Business Unit' LIMIT 1];

        // Prepare request payload
        String jsonBody = '{"orderType":"Pre-Booking","orderSources":"Online","creationDate":"2025-04-16T05:20:40.992Z","orderNo":"VF7-********-OT4DC","phone":"**********","orderToken":"3IU5Z-Qy-63kKtABvuuIGKiA-PYVA4Vj9CG7W8QVPz8","orderTotal":800000,"currencyCode":"INR","status":"New","cancelReason":"","dealerId":"'+ dealer.Id + '","productLineItems":[{"id":"PB_EC15_2023","name":"VF7","price":800000,"quantity":1}],"billingAddress":{"countryCode":"IN","countryName":"India","address1":"Hn","city":"Hn","stateCode":"TR","postalCode":"111111"},"shippingAddress":{"countryCode":"IN","countryName":"India","address1":"Hn","city":"Hn","stateCode":"TR","postalCode":"111111"},"customer":{"recordType":"012C1000000ntTBIAY","dmsCustomerID":"","phone":"**********","verifyOTP":true,"customerName":" Sale pre booking 01","customerEmail":"<EMAIL>","dealerId":"'+ dealer.Id + '"},"paymentInformation":{"amount":50000,"paymentStatus":"New","paymentType":"Booking","paymentMethod":"Payment Gateway","transactionId":"6809ad17193abedd8d3c5e78","paymentDate":"2025-04-24T03:20:58.566Z"},"vehicle":{"model":{"productId":"EC15_2023_GC15N","modelYear":"","name":"VF7 Earth","price":70000},"options":[{"id":"CE1","title":"CE1","selection":{"id":"CE11","productId":"EC15_2023_GC15N_CE11","name":"Jet Black","price":0}}],"packages":[],"finance":null,"promotions":[{"promotionId":"1744106037804","code":"PRE_BOOK_NOW_EXTERIOR_COLOURS_2505","name":"Pre book now and select premium exterior colours at no extra cost","termsApply":"Campaign Start Date: 15th May 2025\\nCampaign End Date: 14th June 2025\\nAmount: discount 100%\\nBooking Date:  15th May 2025 - 14th June 2025\\nDeposit Date: 15th May 2025 - 14th June 2025\\nDeposit Amount = 25,000\\nVehicle Model =  VF6\\nInvoice Date: before 31st Oct 2025\\nVehicle Delivery Date: before 30th Oct 2025","discountChanged":0,"promotionStartDate":"2025-04-26T09:50:37.880Z","promotionExpiryDate":"2025-04-26T09:50:37.880Z","eligibleForVehicle":true,"metadata":{"accessoryKit":{"familyCode":"AK1","code":"AK11","name":"name","amount":1}}}]}}';

        // Serialize request
        RestRequest req = new RestRequest();
        req.requestBody = Blob.valueOf(jsonBody);
        req.httpMethod = 'POST';
        req.requestURI = '/services/apexrest/order';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        // Call the method
        Test.startTest();
        VF_OrderRestAPI.doPost();
        Test.stopTest();

        // Assert response
        System.assertEquals(200, res.statusCode);
        String responseBody = res.responseBody.toString();
        System.assert(responseBody.contains('Order created successfully.'));
        List<VF_PaymentInformation__c> paymentInfoList = [SELECT Id FROM VF_PaymentInformation__c];
        System.assertEquals(1, paymentInfoList.size(), 'Payment information record should be created.');
    }

    @isTest
    static void testDoPost_SuccessNoAccountByPhone() {
       
        Account dealer = [SELECT ID FROM Account WHERE Name = 'Test Business Unit' LIMIT 1];

        // Prepare request payload
        String jsonBody = '{"orderType":"Pre-Booking","orderSources":"Online","creationDate":"2025-04-16T05:20:40.992Z","orderNo":"VF7-********-OT4DC","phone":"**********","orderToken":"3IU5Z-Qy-63kKtABvuuIGKiA-PYVA4Vj9CG7W8QVPz8","orderTotal":800000,"currencyCode":"INR","status":"New","cancelReason":"","dealerId":"'+ dealer.Id + '","productLineItems":[{"id":"PB_EC15_2023","name":"VF7","price":800000,"quantity":1}],"billingAddress":{"countryCode":"IN","countryName":"India","address1":"Hn","city":"Hn","stateCode":"TR","postalCode":"111111"},"shippingAddress":{"countryCode":"IN","countryName":"India","address1":"Hn","city":"Hn","stateCode":"TR","postalCode":"111111"},"customer":{"recordType":"012C1000000ntTBIAY","dmsCustomerID":"","phone":"**********","verifyOTP":true,"customerName":" Sale pre booking 01","customerEmail":"<EMAIL>","dealerId":"'+ dealer.Id + '"},"paymentInformation":{"amount":50000,"paymentStatus":"New","paymentType":"Booking","paymentMethod":"Payment Gateway","transactionId":"6809ad17193abedd8d3c5e78","paymentDate":"2025-04-24T03:20:58.566Z"},"vehicle":{"model":{"productId":"EC15_2023_GC15N","modelYear":"","name":"VF7 Earth","price":70000},"options":[{"id":"CE1","title":"CE1","selection":{"id":"CE11","productId":"EC15_2023_GC15N_CE11","name":"Jet Black","price":0}}],"packages":[],"finance":null,"promotions":[{"promotionId":"1744106037804","code":"PRE_BOOK_NOW_EXTERIOR_COLOURS_2505","name":"Pre book now and select premium exterior colours at no extra cost","termsApply":"Campaign Start Date: 15th May 2025\\nCampaign End Date: 14th June 2025\\nAmount: discount 100%\\nBooking Date:  15th May 2025 - 14th June 2025\\nDeposit Date: 15th May 2025 - 14th June 2025\\nDeposit Amount = 25,000\\nVehicle Model =  VF6\\nInvoice Date: before 31st Oct 2025\\nVehicle Delivery Date: before 30th Oct 2025","discountChanged":0,"promotionStartDate":"2025-04-26T09:50:37.880Z","promotionExpiryDate":"2025-04-26T09:50:37.880Z","eligibleForVehicle":true,"metadata":{"accessoryKit":{"familyCode":"AK1","code":"AK11","name":"name","amount":1}}}]}}';

        // Serialize request
        RestRequest req = new RestRequest();
        req.requestBody = Blob.valueOf(jsonBody);
        req.httpMethod = 'POST';
        req.requestURI = '/services/apexrest/order';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        // Call the method
        Test.startTest();
        VF_OrderRestAPI.doPost();
        Test.stopTest();

        // Assert response
        System.assertEquals(200, res.statusCode);
        String responseBody = res.responseBody.toString();
        System.assert(responseBody.contains('Order created successfully.'));
        List<VF_PaymentInformation__c> paymentInfoList = [SELECT Id FROM VF_PaymentInformation__c];
        System.assertEquals(1, paymentInfoList.size(), 'Payment information record should be created.');
    }

    @isTest
    static void testDoPost_SuccessOrderCancelled() {
       
        Account dealer = [SELECT ID FROM Account WHERE Name = 'Test Business Unit' LIMIT 1];

        // Prepare request payload
        String jsonBody = '{"orderType":"Pre-Booking","orderSources":"Online","creationDate":"2025-04-16T05:20:40.992Z","orderNo":"VF7-********-OT4DC","phone":"**********","orderToken":"3IU5Z-Qy-63kKtABvuuIGKiA-PYVA4Vj9CG7W8QVPz8","orderTotal":800000,"currencyCode":"INR","status":"Cancelled","cancelReason":"","dealerId":"'+ dealer.Id + '","productLineItems":[{"id":"PB_EC15_2023","name":"VF7","price":800000,"quantity":1}],"billingAddress":{"countryCode":"IN","countryName":"India","address1":"Hn","city":"Hn","stateCode":"TR","postalCode":"111111"},"shippingAddress":{"countryCode":"IN","countryName":"India","address1":"Hn","city":"Hn","stateCode":"TR","postalCode":"111111"},"customer":{"recordType":"012C1000000ntTBIAY","dmsCustomerID":"","phone":"**********","verifyOTP":true,"customerName":" Sale pre booking 01","customerEmail":"<EMAIL>","dealerId":"'+ dealer.Id + '"},"paymentInformation":{"amount":50000,"paymentStatus":"New","paymentType":"Booking","paymentMethod":"Payment Gateway","transactionId":"6809ad17193abedd8d3c5e78","paymentDate":"2025-04-24T03:20:58.566Z"},"vehicle":{"model":{"productId":"EC15_2023_GC15N","modelYear":"","name":"VF7 Earth","price":70000},"options":[{"id":"CE1","title":"CE1","selection":{"id":"CE11","productId":"EC15_2023_GC15N_CE11","name":"Jet Black","price":0}}],"packages":[],"finance":null,"promotions":[{"promotionId":"1744106037804","code":"PRE_BOOK_NOW_EXTERIOR_COLOURS_2505","name":"Pre book now and select premium exterior colours at no extra cost","termsApply":"Campaign Start Date: 15th May 2025\\nCampaign End Date: 14th June 2025\\nAmount: discount 100%\\nBooking Date:  15th May 2025 - 14th June 2025\\nDeposit Date: 15th May 2025 - 14th June 2025\\nDeposit Amount = 25,000\\nVehicle Model =  VF6\\nInvoice Date: before 31st Oct 2025\\nVehicle Delivery Date: before 30th Oct 2025","discountChanged":0,"promotionStartDate":"2025-04-26T09:50:37.880Z","promotionExpiryDate":"2025-04-26T09:50:37.880Z","eligibleForVehicle":true,"metadata":{"accessoryKit":{"familyCode":"AK1","code":"AK11","name":"name","amount":1}}}]}}';

        // Serialize request
        RestRequest req = new RestRequest();
        req.requestBody = Blob.valueOf(jsonBody);
        req.httpMethod = 'POST';
        req.requestURI = '/services/apexrest/order';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        // Call the method
        Test.startTest();
        VF_OrderRestAPI.doPost();
        Test.stopTest();

        // Assert response
        System.assertEquals(200, res.statusCode);
        String responseBody = res.responseBody.toString();
        System.assert(responseBody.contains('Order created successfully.'));
        List<VF_PaymentInformation__c> paymentInfoList = [SELECT Id FROM VF_PaymentInformation__c];
        System.assertEquals(1, paymentInfoList.size(), 'Payment information record should be created.');
    }

    @isTest
    static void testDoPost_AccountExisted() {
       
        Account dealer = [SELECT ID FROM Account WHERE Name = 'Test Business Unit' LIMIT 1];

        // Prepare request payload
        String jsonBody = '{"orderType":"Pre-Booking","orderSources":"Online","creationDate":"2025-04-16T05:20:40.992Z","orderNo":"VF7-********-OT4DC","phone":"**********","orderToken":"3IU5Z-Qy-63kKtABvuuIGKiA-PYVA4Vj9CG7W8QVPz8","orderTotal":800000,"currencyCode":"INR","status":"New","cancelReason":"","dealerId":"'+ dealer.Id + '","productLineItems":[{"id":"PB_EC15_2023","name":"VF7","price":800000,"quantity":1}],"billingAddress":{"countryCode":"IN","countryName":"India","address1":"Hn","city":"Hn","stateCode":"TR","postalCode":"111111"},"shippingAddress":{"countryCode":"IN","countryName":"India","address1":"Hn","city":"Hn","stateCode":"TR","postalCode":"111111"},"customer":{"recordType":"012C1000000ntTBIAY","dmsCustomerID":"","phone":"**********","verifyOTP":true,"customerName":" Sale pre booking 01","customerEmail":"<EMAIL>","dealerId":"'+ dealer.Id + '"},"paymentInformation":{"amount":50000,"paymentStatus":"Released","paymentType":"Booking","paymentMethod":"Payment Gateway","transactionId":"6809ad17193abedd8d3c5e78","paymentDate":"2025-04-24T03:20:58.566Z"},"vehicle":{"model":{"productId":"EC15_2023_GC15N","modelYear":"","name":"VF7 Earth","price":70000},"options":[{"id":"CE1","title":"CE1","selection":{"id":"CE11","productId":"EC15_2023_GC15N_CE11","name":"Jet Black","price":0}}],"packages":[],"finance":null,"promotions":[{"promotionId":"1744106037804","code":"PRE_BOOK_NOW_EXTERIOR_COLOURS_2505","name":"Pre book now and select premium exterior colours at no extra cost","termsApply":"Campaign Start Date: 15th May 2025\\nCampaign End Date: 14th June 2025\\nAmount: discount 100%\\nBooking Date:  15th May 2025 - 14th June 2025\\nDeposit Date: 15th May 2025 - 14th June 2025\\nDeposit Amount = 25,000\\nVehicle Model =  VF6\\nInvoice Date: before 31st Oct 2025\\nVehicle Delivery Date: before 30th Oct 2025","discountChanged":0,"promotionStartDate":"2025-04-26T09:50:37.880Z","promotionExpiryDate":"2025-04-26T09:50:37.880Z","eligibleForVehicle":true}]}}';

        // Serialize request
        RestRequest req = new RestRequest();
        req.requestBody = Blob.valueOf(jsonBody);
        req.httpMethod = 'POST';
        req.requestURI = '/services/apexrest/order';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        // Call the method
        Test.startTest();
        VF_OrderRestAPI.doPost();
        Test.stopTest();

        // Assert response
        System.assertEquals(200, res.statusCode);
        String responseBody = res.responseBody.toString();
        System.assert(responseBody.contains('Order created successfully.'));

        List<VF_PaymentInformation__c> paymentInfoList = [SELECT Id FROM VF_PaymentInformation__c];
        System.assertEquals(1, paymentInfoList.size(), 'Payment information record should be created.');
    }

    @isTest
    static void testDoPost_DealerEmpty() {
        // Prepare request payload with invalid dealerId
        VF_OrderRestAPI.OrderRequestWrapper request = new VF_OrderRestAPI.OrderRequestWrapper();
        request.dealerId = '';

        // Serialize request
        RestRequest req = new RestRequest();
        req.requestBody = Blob.valueOf(JSON.serialize(request));
        req.httpMethod = 'POST';
        req.requestURI = '/services/apexrest/order';
        RestContext.request = req;

        RestResponse res = new RestResponse();
        RestContext.response = res;

        // Call the method
        Test.startTest();
        VF_OrderRestAPI.doPost();
        Test.stopTest();

        // Assert response
        System.assertEquals(400, res.statusCode);
        String responseBody = res.responseBody.toString();
        System.assert(responseBody.contains('BAD_REQUEST'));
    }

    @isTest static void testJSONException() {
        String jsonBody = '{ "orderType": "prebooking", "orderSources": "Online", "creationDate": true, "dealerId": "INVALID" }';

        RestRequest request = new RestRequest();
        RestResponse res = new RestResponse();
        request.requestUri = '/services/apexrest/order';
        request.httpMethod = 'POST';
        request.requestBody = Blob.valueOf(jsonBody); 
        RestContext.request = request;
        RestContext.response= res;
        
        Test.startTest();
        VF_OrderRestAPI.doPost();
        Test.stopTest();

        // Assert response
        System.assertEquals(500, res.statusCode);
    }
}