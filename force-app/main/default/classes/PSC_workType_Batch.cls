/*****Class Description*********************
* Name 					: PSC_workType_Batch
* Test Class			: PSC_BatchChain_Test
* Date Created 			: (dd/mm/yyyy) 04/04/2025
* Purpose				: Apex class to handle checking PSCI by batch
* Author				: NinhNN
* Modification History	: NA
* Version	Author			DateModified	Description						
* 0.1		NinhNN       	04/04/2025      Initial Development
* *******************************************/

global class PSC_workType_Batch implements Database.Batchable<sObject> {
    private Id PscId;
    
    public PSC_workType_Batch(Id inputPSCId) {
        this.PscId = inputPSCId;
    }
    
    global Database.QueryLocator start(Database.BatchableContext BC) {      
        //Query Product Service Campaign Item not yet scan
        return Database.getQueryLocator([
            SELECT id, Work_Type__c, Raw_WorkType__c, Is_Scan__c 
            FROM Product_Service_Campaign_Work_Type__c 
            WHERE Is_Scan__c = FALSE 
            AND Work_Type__c = ''
            AND Product_Service_Campaign__c =: PscId
        ]);
    }
    
    global void execute(Database.BatchableContext BC, List<Product_Service_Campaign_Work_Type__c> lst_pscwt) {       
        //Create Set of Raw WTs 
        Set<String> Set_wt = new Set<String>();    
        for (Product_Service_Campaign_Work_Type__c pscwt : lst_pscwt) {
            Set_wt.add(pscwt.Raw_WorkType__c);
        }
        
        //Create Map<WT, AssetID> 
        Map<String, Id> Map_wt = new Map<String, Id>();       
        for (Worktype wt : [SELECT Id, Name, External_Id__c FROM WorkType WHERE External_Id__c IN :Set_wt]) {
            Map_wt.put(wt.External_Id__c, wt.Id);
        }
        
        //Update PSCI
        for(Product_Service_Campaign_Work_Type__c pscwt : lst_pscwt) {        
            pscwt.Is_Scan__c = true;
            pscwt.Work_Type__c = (Map_wt.get(pscwt.Raw_WorkType__c) == null ? pscwt.Work_Type__c : Map_wt.get(pscwt.Raw_WorkType__c));
        }
        try {
            Update lst_pscwt;            
        } catch(Exception e) {
            SYS_Exception__c ex = new SYS_Exception__c(Need_FollowUp__c = TRUE, Description__c = 'PSC_Vin_Batch failed.', Exception_Message__c = e.getMessage(), Type__c = e.getTypeName(), SYS_StackTrace_c__c = e.getStackTraceString()); Insert ex;
        }         
    }   
    
    global void finish(Database.BatchableContext BC) {
        system.debug('START VIN batch --- HERE');
        Database.executeBatch(new PSC_Vin_Batch(PscId), 200);
    }
}