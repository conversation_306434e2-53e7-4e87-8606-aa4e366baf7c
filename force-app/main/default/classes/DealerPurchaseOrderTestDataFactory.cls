public with sharing class DealerPurchaseOrderTestDataFactory {
    public static void createTestData() {
        UserRole userRole = new UserRole(DeveloperName = 'TestRole', Name = 'Test Role');
        insert userRole;
        User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' Limit 1];
        adminUser.UserRoleId = userRole.Id;
        update adminUser;
        System.runAs(adminUser){
            Account acc = new Account(Name = 'Test Account', RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Dealer').getRecordTypeId());
            insert acc;

            Contact con = new Contact(LastName = 'Test Contact', AccountId = acc.Id, RecordTypeId = Schema.SObjectType.Contact.getRecordTypeInfosByName().get('Dealer').getRecordTypeId());
            insert con;

            List<Product2> products = new List<Product2>();
            products.add(new Product2(
                Name = 'Test Vehicle',
                ProductCode = 'EC15_2023_GC12N',
                VF_ProductId__c = 'EC15_2023_GC12N',
                IsActive = true,
                Family = 'MOD',
                VF_Model__c = 'EC15_2023',
                VF_Trim__c = 'GC12N',
                VF_FamilyValue__c = 'GC12N',
                VF_FamilyValueName__c = 'Test',
                RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByName().get('Vehicle').getRecordTypeId()
            ));
            products.add(new Product2(
                Name = 'Test 1',
                ProductCode = 'EC15_2023_GC12N_CE17',
                VF_ProductId__c = 'EC15_2023_GC12N_CE17',
                IsActive = true,
                Family = 'CE1',
                VF_Model__c = 'EC15_2023',
                VF_Trim__c = 'GC12N',
                VF_FamilyValue__c = 'CE17',
                VF_FamilyValueName__c = 'Test',
                RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByName().get('Vehicle').getRecordTypeId()
            ));
            products.add(new Product2(
                Name = 'Test 2',
                ProductCode = 'EC15_2023_GC12N_CI19',
                VF_ProductId__c = 'EC15_2023_GC12N_CI19',
                IsActive = true,
                Family = 'CI1',
                VF_Model__c = 'EC15_2023',
                VF_Trim__c = 'GC12N',
                VF_FamilyValue__c = 'CI19',
                VF_FamilyValueName__c = 'Test',
                RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByName().get('Vehicle').getRecordTypeId()
            ));
            products.add(new Product2(
                Name = 'Test Part',
                ProductCode = 'BAT11002004',
                VF_ProductId__c = 'BAT11002004',
                IsActive = true,
                RecordTypeId = Schema.SObjectType.Product2.getRecordTypeInfosByName().get('Part').getRecordTypeId()
            ));
            insert products;

            // Create Standard Pricebook
            List<PricebookEntry> entries = new List<PricebookEntry>();
            for (Product2 p : products) {
                entries.add(new PricebookEntry(
                    Pricebook2Id = Test.getStandardPricebookId(),
                    Product2Id = p.Id,
                    VF_UOM__c = 'PE',
                    UnitPrice = 1000,
                    IsActive = true
                ));
            }
            insert entries;
        }
        Profile profile = [SELECT Id FROM Profile WHERE Name = 'Dealer User' LIMIT 1];
        Id contactId = [SELECT Id FROM Contact LIMIT 1]?.Id;
        User user = new User(
            Username = '<EMAIL>',
            LastName = 'Test User', 
            Email = '<EMAIL>',
            Alias = 'dealeru',
            ProfileId = profile.Id,
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ContactId = contactId
        );
        insert user;
        System.runAs(user) {
            Id accountId = [SELECT Id FROM Account LIMIT 1]?.Id;
            Dealership_Purchase_Order__c order = new Dealership_Purchase_Order__c(Account__c = accountId, Customer_Order_Number__c = 'Test', Purchase_Order_Category__c = 'Stock', Expected_Delivery_Date__c = System.now().addDays(1), Estimated_Wholesales_Amount__c = 0, Status__c = 'Draft', Lock_Edit__c = false);
            insert order;

            Product2 prod = [SELECT Id FROM Product2 WHERE RecordType.Name = 'Part' LIMIT 1];
            PricebookEntry pbe = [SELECT Id, UnitPrice FROM PricebookEntry WHERE Product2Id = :prod.Id LIMIT 1];

            Dealership_Purchase_Order_Item__c item = new Dealership_Purchase_Order_Item__c(
                Exterior_Color__c = 'CE17', 
                Interior_Color__c = 'CI19', 
                Model__c = 'EC15_2023',
                Product_Code__c = 'GC12N', 
                Quantity__c = 2, 
                Unit_Price__c = 1000,
                Dealership_Purchase_Order__c = order.Id
            );
            insert item;

            Order o = new Order(
                Name = 'Test Order',
                Reference_WO_Number__c = 'Test',
                Expected_Delivery_Date__c = System.today(),
                Description = 'Test',
                AccountId = accountId,
                EffectiveDate = System.today(),
                Status = 'New',
                Purchase_Order_Category__c = 'Stock',
                Lock_Edit__c = false,
                RecordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByName().get('Dealer Part Purchase').getRecordTypeId(),
                Pricebook2Id = Test.getStandardPricebookId()
            );
            insert o;
            List<OrderItem> items = new List<OrderItem>{
                new OrderItem(
                    PricebookEntryId = pbe.Id,
                    Product2Id = prod.Id,
                    Quantity = 1,
                    UnitPrice = pbe.UnitPrice,
                    OrderId = o.Id
                )
            };
            insert items;

            Order oVehicle = new Order(
                Name = 'Test Order',
                Reference_WO_Number__c = 'Test',
                Expected_Delivery_Date__c = System.today(),
                Description = 'Test',
                AccountId = accountId,
                EffectiveDate = System.today(),
                Status = 'New',
                Lock_Edit__c = false,
                Purchase_Order_Category__c = 'Stock',
                RecordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByName().get('Dealer Vehicle Purchase').getRecordTypeId(),
                Pricebook2Id = Test.getStandardPricebookId()
            );
            insert oVehicle;
            List<OrderItem> oVehicleItems = new List<OrderItem>();

            List<Product2> vehicles = [SELECT Id FROM Product2 WHERE RecordType.Name = 'Vehicle' AND IsActive = true];
            Map<Id, PricebookEntry> productIdToEntry = new Map<Id, PricebookEntry>();

            for (PricebookEntry et : [SELECT Id, UnitPrice, Product2Id FROM PricebookEntry WHERE Product2.RecordType.Name = 'Vehicle' AND Product2.IsActive = true]) {
                productIdToEntry.put(et.Product2Id, et);
            }
            for (Product2 p :vehicles) {
                oVehicleItems.add(new OrderItem(
                    PricebookEntryId = productIdToEntry.get(p.Id).Id,
                    Product2Id = p.Id,
                    Quantity = 1,
                    UnitPrice = productIdToEntry.get(p.Id).UnitPrice,
                    OrderId = oVehicle.Id
                ));
            }
             
            insert oVehicleItems;
        }
    }

    public class DealerPurchaseOrderPriceSapMock implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"result":{"code":"200","message":"Success","status":"S"},"data":{"transactionID":"TX123456","transactionType":1,"items":[{"productCode":"PROD001","quantity":10.5,"priceObject":[{"priceType":"Retail","priceAmount":100,"priceValue":95,"unitPerPrice":1},{"priceType":"Wholesale","priceAmount":90,"priceValue":85,"unitPerPrice":10}]},{"productCode":"PROD002","quantity":5,"priceObject":[{"priceType":"Retail","priceAmount":200,"priceValue":190,"unitPerPrice":1}]}]}}');
            res.setStatusCode(200);
            return res;
        }
    }

    public class DealerPurchaseOrderSapMock implements HttpCalloutMock {
        public Integer statusCode;
        public String body;
        public DealerPurchaseOrderSapMock(Integer statusCode, String body) {
            this.statusCode = statusCode;
            this.body = body;
        }
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody(body);
            res.setStatusCode(statusCode);
            return res;
        }
    }
}