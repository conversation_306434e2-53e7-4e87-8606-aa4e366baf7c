@isTest(isParallel=true)
public class VfCustomLookupSearchResultTest {
    @isTest
    static void compareTo_should_work_with_two_null_titles() {
        VfCustomLookupSearchResult r1 = getSearchResult(null);
        VfCustomLookupSearchResult r2 = getSearchResult(null);

        Integer compareResult = r1.compareTo(r2);

        Assert.areEqual(0, compareResult);
    }

    @isTest
    static void compareTo_should_work_with_this_null_title() {
        VfCustomLookupSearchResult r1 = getSearchResult(null);
        VfCustomLookupSearchResult r2 = getSearchResult('a');

        Integer compareResult = r1.compareTo(r2);

        Assert.areEqual(1, compareResult);
    }

    @isTest
    static void compareTo_should_work_with_other_null_title() {
        VfCustomLookupSearchResult r1 = getSearchResult('a');
        VfCustomLookupSearchResult r2 = getSearchResult(null);

        Integer compareResult = r1.compareTo(r2);

        Assert.areEqual(-1, compareResult);
    }

    @isTest
    static void compareTo_should_work_with_non_null_titles() {
        VfCustomLookupSearchResult r1 = getSearchResult('a');
        VfCustomLookupSearchResult r2 = getSearchResult('b');

        Integer compareResult = r1.compareTo(r2);

        Assert.areEqual(-1, compareResult);
    }

    @isTest
    static void getters_should_work() {
        VfCustomLookupSearchResult r1 = new VfCustomLookupSearchResult();
        // For the sake of code coverage
        VfCustomLookupSearchResult r = new VfCustomLookupSearchResult('0010R00000yvEyRQAU', '123ABC', 'type', 'icon', 'title', 'subtitle');

        Assert.areEqual('0010R00000yvEyRQAU', r.getId());
        Assert.areEqual('123ABC', r.getKey());
        Assert.areEqual('type', r.getSObjectType());
        Assert.areEqual('icon', r.getIcon());
        Assert.areEqual('title', r.getTitle());
        Assert.areEqual('subtitle', r.getSubtitle());
    }

    private static VfCustomLookupSearchResult getSearchResult(String title) {
        return new VfCustomLookupSearchResult(null, null, null, null, title, null);
    }
}