@isTest
private class DealerPartPurchaseOrderSubmitSapTest {

    @testSetup
    static void setupTestData() {
        DealerPurchaseOrderTestDataFactory.createTestData();
    }

    @isTest
    static void testSyncOrderSuccess() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Order> orders = [SELECT Id FROM Order WHERE RecordType.Name = 'Dealer Vehicle Purchase' LIMIT 1];
            DealerPartPurchaseOrderSubmitSap.Input input = new DealerPartPurchaseOrderSubmitSap.Input();
            input.orderId = orders[0].Id;

            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderSapMock(200, '{"result":{"code":"200","message":"Success","status":"S"},"data":{}}'));

            Test.startTest();
            List<DealerPartPurchaseOrderSubmitSap.Output> results = DealerPartPurchaseOrderSubmitSap.execute(new List<DealerPartPurchaseOrderSubmitSap.Input>{input});
            System.debug(results);
            Test.stopTest();

            orders = [SELECT Id, Sync2SAP__c FROM Order WHERE Id = :orders[0].Id];
            System.debug(orders);
            System.assert(orders[0].Sync2SAP__c, 'Order should be sync to SAP');
        }
    }

    @isTest
    static void testSyncOrderError1() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Order> orders = [SELECT Id FROM Order WHERE RecordType.Name = 'Dealer Vehicle Purchase' LIMIT 1];
            DealerPartPurchaseOrderSubmitSap.Input input = new DealerPartPurchaseOrderSubmitSap.Input();
            input.orderId = orders[0].Id;

            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderSapMock(400, '{"result":{"code":"400","message":"Success","status":"F"},"data":{}}'));

            Test.startTest();
            List<DealerPartPurchaseOrderSubmitSap.Output> results = DealerPartPurchaseOrderSubmitSap.execute(new List<DealerPartPurchaseOrderSubmitSap.Input>{input});
            System.debug(results);
            Test.stopTest();

            orders = [SELECT Id, Sync2SAP__c FROM Order WHERE Id = :orders[0].Id];
            System.debug(orders);
            System.assert(!orders[0].Sync2SAP__c, 'Order should not sync to SAP');
        }
    }

    @isTest
    static void testSyncOrderError2() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Order> orders = [SELECT Id FROM Order WHERE RecordType.Name = 'Dealer Vehicle Purchase' LIMIT 1];
            DealerPartPurchaseOrderSubmitSap.Input input = new DealerPartPurchaseOrderSubmitSap.Input();
            input.orderId = orders[0].Id;

            Test.setMock(HttpCalloutMock.class, new DealerPurchaseOrderTestDataFactory.DealerPurchaseOrderSapMock(200, '{"result":{"code":"400","message":"Success","status":"F"},"data":{}}'));

            Test.startTest();
            List<DealerPartPurchaseOrderSubmitSap.Output> results = DealerPartPurchaseOrderSubmitSap.execute(new List<DealerPartPurchaseOrderSubmitSap.Input>{input});
            System.debug(results);
            Test.stopTest();

            orders = [SELECT Id, Sync2SAP__c FROM Order WHERE Id = :orders[0].Id];
            System.debug(orders);
            System.assert(!orders[0].Sync2SAP__c, 'Order should not sync to SAP');
        }
    }
}