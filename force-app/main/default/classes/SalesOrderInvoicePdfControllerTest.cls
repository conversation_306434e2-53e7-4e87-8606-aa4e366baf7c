@IsTest
public with sharing class SalesOrderInvoicePdfControllerTest {
    @isTest
    static void testGenerateProformaInvoice() {
        Account acc = new Account(Name = 'Test Account');
        insert acc;

        // Create a sample Order
        Order o = new Order(
            Name = 'Test Order',
            Description = 'Test',
            AccountId = acc.Id,
            EffectiveDate = System.today(),
            Status = 'New',
            RecordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByName().get('Booking').getRecordTypeId(),
            Pricebook2Id = Test.getStandardPricebookId()
        );
        insert o;

        ContentVersion contentVersionInsert = new ContentVersion(
            Title = 'Test',
            PathOnClient = 'Test.jpg',
            VersionData = Blob.valueOf('Test Content Data'),
            IsMajorVersion = true
        );
        insert contentVersionInsert;

        ContentVersion contentVersionSelect = [SELECT Id, Title, ContentDocumentId FROM ContentVersion WHERE Id = :contentVersionInsert.Id LIMIT 1];

        List<ContentDocument> documents = [SELECT Id, Title, LatestPublishedVersionId FROM ContentDocument];
        
        ContentDocumentLink contentlink = new ContentDocumentLink();
        contentlink.LinkedEntityId = o.Id;
        contentlink.ContentDocumentId = documents[0].Id;
        contentlink.ShareType = 'V';
        contentlink.Visibility = 'AllUsers';
        insert contentlink;

        String invNumber = SalesOrderInvoicePdfController.generateInvoiceNumber(o.Id, 'PIV');
        String hisId = SalesOrderInvoicePdfController.createOrderInvoiceHistory(o.Id, invNumber, 'PIV', documents[0].Id);
        System.assertNotEquals(null, invNumber);
        System.assertNotEquals(null, hisId);
    }
}