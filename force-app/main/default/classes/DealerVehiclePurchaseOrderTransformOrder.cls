/*****Class Description*******************
* Name           : DealerVehiclePurchaseOrderTransformOrder
* Test Class     : DealerVehiclePurchaseOrderTransformOrderTest
* Purpose        : Transform (Create) Order & Order Item from Dealership_Purchase_Order__c 
* Version  Author      DateModified     Description
* 1.0      thangnv12    26/04/2025       Init
* *******************************************/
public with sharing class DealerVehiclePurchaseOrderTransformOrder {
    public static final String ITEM_NOT_FOUND_ERR_MESS = 'No products were found for this Purchase Order.';
    public static final String PRODUCT_NOT_FOUND_ERR_MESS = 'No product was found for the given product code, or the product is missing from the price book.';

    @InvocableMethod(label='Dealer Purchase Order - Create Order' description='Create Standard Order from Dealership Purchase Order' category='Purchase Order')
    public static List<Output> execute(List<Input> inputs) {
        List<Output> results = new List<Output>();
        // Extract IDs
        Set<Id> poIds = new Set<Id>();
        for (Input input : inputs) {
            if (input.orderId != null) {
                poIds.add(input.orderId);
            }
        }

        // Query all relevant Dealership Purchase Orders
        List<Dealership_Purchase_Order__c> poRecords = [
            SELECT Id, Name, Account__c, Description__c, Status__c, Customer_Order_Number__c, Expected_Delivery_Date__c, Purchase_Order_Category__c,Estimated_Wholesales_Amount__c, (SELECT Battery__c, Exterior_Color__c, Interior_Color__c, Model__c, Sunroof__c, Interior_Equipment__c, Model_Year__c, Product_Code__c, Quantity__c, Unit_Price__c, Wheels__c FROM Dealership_Purchase_Order_Items__r)
            FROM Dealership_Purchase_Order__c
            WHERE Id IN :poIds
        ];
        Set<String> allProductCodes = new Set<String>();
        Map<Id, Set<String>> mapPoItemAndProductCodes = new Map<Id, Set<String>>();
        String errorMessage;
        Boolean hasError = false;
        for (Dealership_Purchase_Order__c po : poRecords) {
            System.debug(JSON.serialize(po));
            if (errorMessage != null) {
                results.add(new Output(false, errorMessage));
                continue;
            }
            try {
                System.debug(po.Dealership_Purchase_Order_Items__r);
                if (po.Dealership_Purchase_Order_Items__r.isEmpty()) throw new DealerPurchaseOrderException(ITEM_NOT_FOUND_ERR_MESS + ' Order: ' + po.Name);

                for (Dealership_Purchase_Order_Item__c poItem : po.Dealership_Purchase_Order_Items__r) {
                    Set<String> productCodes = new Set<String>{poItem.Model__c + '_' + poItem.Product_Code__c};
                    if (poItem.Exterior_Color__c != null) productCodes.add(poItem.Model__c + '_' + poItem.Product_Code__c + '_' + poItem.Exterior_Color__c);
                    if (poItem.Interior_Color__c != null) productCodes.add(poItem.Model__c + '_' + poItem.Product_Code__c + '_' + poItem.Interior_Color__c);
                    if (poItem.Sunroof__c != null) productCodes.add(poItem.Model__c + '_' + poItem.Product_Code__c + '_' + poItem.Sunroof__c);
                    if (poItem.Interior_Equipment__c != null) productCodes.add(poItem.Model__c + '_' + poItem.Product_Code__c + '_' + poItem.Interior_Equipment__c);
                    mapPoItemAndProductCodes.put(poItem.Id, productCodes);
                    allProductCodes.addAll(productCodes);
                }
                results.add(new Output(true, 'Processed: ' + po.Name));
            } catch (Exception e) {
                errorMessage = e.getMessage();
                results.add(new Output(false, errorMessage));
                hasError = true;
            }
        }
        if (hasError) return results;
        Id standardPriceBookId = Test.isRunningTest() ? Test.getStandardPricebookId() : [SELECT Id FROM Pricebook2 WHERE IsStandard = TRUE LIMIT 1]?.Id;
        // Query Products 
        Map<String, Id> productCodeToProductId = new Map<String, Id>();
        for (Product2 p : [
            SELECT Id, VF_ProductId__c FROM Product2 WHERE VF_ProductId__c IN :allProductCodes
        ]) {
            productCodeToProductId.put(p.VF_ProductId__c, p.Id);
        }

        // Query PricebookEntries
        Map<Id, PricebookEntry> productIdToEntry = new Map<Id, PricebookEntry>();
        for (PricebookEntry pbe : [SELECT Id, Product2Id, UnitPrice FROM PricebookEntry WHERE Pricebook2Id = :standardPricebookId AND Product2.VF_ProductId__c IN :allProductCodes AND IsActive = true
        ]) {
            productIdToEntry.put(pbe.Product2Id, pbe);
        }
                
        // Create Order Items from PO Items
        List<Order> orders = new List<Order>();
        String orderRecordTypeId = Schema.SObjectType.Order.getRecordTypeInfosByName().get('Dealer Vehicle Purchase').getRecordTypeId();
        Map<Id, List<OrderItem>> mapPoItemAndOrderItems = new Map<Id, List<OrderItem>>();
        results = new List<Output>();
        errorMessage = null;
        for (Dealership_Purchase_Order__c po : poRecords) {
            if (errorMessage != null) {
                results.add(new Output(false, errorMessage));
                continue;
            }
            try {
                for (Dealership_Purchase_Order_Item__c poItem : po.Dealership_Purchase_Order_Items__r) {
                    Integer quantity = Integer.valueOf(poItem.Quantity__c);
                    Order newOrder = new Order();
                    newOrder.AccountId = po.Account__c;
                    newOrder.EffectiveDate = Date.today();
                    newOrder.Status = 'New'; 
                    newOrder.Description = po.Description__c;
                    newOrder.Pricebook2Id  = standardPriceBookId;
                    newOrder.RecordTypeId  = orderRecordTypeId;
                    newOrder.Customer_Order_Number__c = po.Customer_Order_Number__c;
                    newOrder.Expected_Delivery_Date__c = po.Expected_Delivery_Date__c;
                    newOrder.Purchase_Order_Category__c = po.Purchase_Order_Category__c;
                    newOrder.Estimated_Wholesales_Amount__c = po.Estimated_Wholesales_Amount__c;
                    newOrder.Dealership_Purchase_Order__c = po.Id;
                    newOrder.Dealership_Purchase_Order_Item__c = poItem.Id;
                    newOrder.Lock_Edit__c = false;
                    for (Integer i = 0; i < quantity; i++) {
                        orders.add(newOrder.clone(false));
                    }
                    List<OrderItem> orderItems = new List<OrderItem>();
                    Set<String> productCodes = mapPoItemAndProductCodes.get(poItem.Id);
                    for (String productCode :productCodes) {
                        Id productId = productCodeToProductId.get(productCode);
                        PricebookEntry entry = productIdToEntry.get(productId);
                        if (productId == null || entry == null) throw new DealerPurchaseOrderException(PRODUCT_NOT_FOUND_ERR_MESS + ' Product Code: ' + productCode);
                        OrderItem item = new OrderItem();
                        item.Product2Id = productId;
                        item.PricebookEntryId = entry.Id;
                        item.Quantity = 1;
                        item.UnitPrice = entry.UnitPrice;
                        orderItems.add(item);
                    }
                    mapPoItemAndOrderItems.put(poItem.Id, orderItems);
                }
                results.add(new Output(true, 'Processed: ' + po.Name));
            } catch (Exception e) {
                errorMessage = e.getMessage();
                results.add(new Output(false, errorMessage));
                hasError = true;
            }
        }
        if (hasError) return results;
        if (mapPoItemAndOrderItems.values().size() <= 0) throw new DealerPurchaseOrderException(ITEM_NOT_FOUND_ERR_MESS);
        Savepoint sp = Database.setSavepoint();
        errorMessage = null;
        try {
            // insert orders
            Database.SaveResult[] orderSaveResults = Database.insert(orders, true);
            System.debug(orderSaveResults);
            Set<Id> orderIds = new Set<Id>();
            for (Database.SaveResult sr : orderSaveResults) {
                if (sr.isSuccess()) {
                    orderIds.add(sr.getId());
                }
            }
            
            List<Order> orderList = [SELECT Id, Name, Dealership_Purchase_Order_Item__c FROM Order WHERE Id IN :orderIds];
            List<OrderItem> allOrderItems = new List<OrderItem>();
            for (Order order : orderList) {
                Id poItemId = order.Dealership_Purchase_Order_Item__c;
                List<OrderItem> orderItems = mapPoItemAndOrderItems.get(poItemId);
                for (OrderItem item : orderItems) {
                    item.OrderId = order.Id;
                    allOrderItems.add(item.clone(false));
                }
            }

            // insert order items
            Database.SaveResult[] orderItemSaveResults = Database.insert(allOrderItems, true);
            System.debug(orderItemSaveResults);
        } catch (Exception e) {
            Database.rollback(sp);
            errorMessage = e.getMessage();
            hasError = true;
        }
        results = new List<Output>();
        for (Dealership_Purchase_Order__c po : poRecords) {
            results.add(new Output(!hasError, hasError ? errorMessage : 'Created'));
        }
        return results;
    }

    public class Input {
        @InvocableVariable(required=true label='Order Id' description='Id of the Dealership Purchase Order record')
        public String orderId;
    }

    public class Output {
        @InvocableVariable(label='Is Success')
        public Boolean success;
        @InvocableVariable(label='Result Message')
        public String message;
        public Output (Boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }

    public class DealerPurchaseOrderException extends Exception {}
}