@isTest
private class DealerVehiclePurchaseOrderTransformTest {

    @testSetup
    static void setupTestData() {
        DealerPurchaseOrderTestDataFactory.createTestData();
    }

    @isTest
    static void testCreateOrderFromDealerPuchase() {
        User user = [SELECT Id FROM User WHERE userName = '<EMAIL>'];
        System.runAs(user) {
            List<Dealership_Purchase_Order__c> orderPO = [SELECT Id FROM Dealership_Purchase_Order__c LIMIT 1];
            // Prepare input for invocable method
            DealerVehiclePurchaseOrderTransformOrder.Input input = new DealerVehiclePurchaseOrderTransformOrder.Input();
            input.orderId = orderPo[0].Id;

            Test.startTest();
            List<DealerVehiclePurchaseOrderTransformOrder.Output> results = DealerVehiclePurchaseOrderTransformOrder.execute(new List<DealerVehiclePurchaseOrderTransformOrder.Input>{input});
            System.debug(results);
            Test.stopTest();

            // Verify Order was created
            List<Order> orders = [SELECT Id, AccountId, Dealership_Purchase_Order__c, Dealership_Purchase_Order_Item__c FROM Order WHERE Dealership_Purchase_Order__c = :orderPo[0].Id];
            System.debug(orders);
            System.assert(orders.size() == 2, 'Order should be created');

            // Verify OrderItems were created
            List<OrderItem> orderItems = [SELECT Id, OrderId, Product2Id FROM OrderItem WHERE OrderId IN :orders];
            System.assert(orderItems.size() == 6, 'OrderItems should be created');
        }
    }
}