public class SalesOrderInvoicePdfExtension {
    public Order order { get; set; }

    public SalesOrderInvoicePdfExtension(ApexPages.StandardController stdCtrl) {
        this.order = (Order)stdCtrl.getRecord();
        this.order = [SELECT Id, OrderNumber, Status, Invoiced_Date__c, GSTN__c, Relationship_Account__c, TotalAmount, BillingStreet, BillingCity, BillingState, BillingCountry, ShippingStreet, ShippingCity, ShippingState, ShippingCountry, Account.RecordType.DeveloperName, Account.Name, Account.AccountNumber, Account.BillingAddress, Account.BillingStreet, Account.BillingCity, Account.BillingState, Account.BillingCountry, Account.Phone, Account.VF_IdentificationNo__c, PAN__c, Vehicle_Information__c, Sale_Options_Retail_Base_Price__c, Vehicle_Retail_Base_Price__c, Dealer_Discount_Amount__c, VF_Discount_Amount__c, CGST_Amount__c, CGST_Rate__c, IGST_Amount__c, IGST_Rate__c, SGST_Amount__c, SGST_Rate__c, UGST_Amount__c, UGST_Rate__c, Vehicle_Ex_Showroom_Price__c, TCS_Rate__c, TCS_Amount__c, Grand_Total_Without_Additional_Fees__c, VIN_Number__c, VIN_Number__r.Name, VIN_Number__r.Engine_Number_1__c, VIN_Number__r.Engine_Number_2__c, VIN_Number__r.Product2.VF_HSNSAC__c, Business_Unit__c, Business_Unit__r.Name, Business_Unit__r.VF_Hotline__c, Business_Unit__r.VF_IdentificationNo__c, Business_Unit__r.BillingStreet, Business_Unit__r.BillingCity, Business_Unit__r.BillingState, Business_Unit__r.BillingCountry FROM Order WHERE Id = :order.Id];
    }

    public String getAccountBillingAddress() {
        String str = '';
        if (this.order.Business_Unit__c != null) {
            if (this.order.Business_Unit__r.BillingStreet != null) {
                str += this.order.Business_Unit__r.BillingStreet + ', ';
            }
            if (this.order.Business_Unit__r.BillingCity != null) {
                str += this.order.Business_Unit__r.BillingCity + ', '; 
            }
            if (this.order.Business_Unit__r.BillingState != null) {
                str += this.order.Business_Unit__r.BillingState + ', ';
            }
            if (this.order.Business_Unit__r.BillingCountry != null) {
                str += this.order.Business_Unit__r.BillingCountry;   
            }
        }
        return str;
    }

    public String getOrderBillingAddress() {
        String str = '';
        if (this.order.BillingStreet != null) {
            str += this.order.BillingStreet + ', ';
        }
        if (this.order.BillingCity != null) {
            str += this.order.BillingCity + ', '; 
        }
        if (this.order.BillingState != null) {
            str += this.order.BillingState + ', ';
        }
        if (this.order.BillingCountry != null) {
            str += this.order.BillingCountry;   
        }
        return str;
    }

    public String getOrderShippingAddress() {
        String str = '';
        if (this.order.ShippingStreet != null) {
            str += this.order.ShippingStreet + ', ';
        }
        if (this.order.ShippingCity != null) {
            str += this.order.ShippingCity + ', '; 
        }
        if (this.order.ShippingState != null) {
            str += this.order.ShippingState + ', ';
        }
        if (this.order.ShippingCountry != null) {
            str += this.order.ShippingCountry;   
        }
        return str;
    }

    public String getProductPrice() {
        return formatDecimal((this.Order.Sale_Options_Retail_Base_Price__c != null ? this.Order.Sale_Options_Retail_Base_Price__c : 0) + (this.Order.Vehicle_Retail_Base_Price__c != null ? this.Order.Vehicle_Retail_Base_Price__c : 0));
    }

    public String getDiscount() {
        return formatDecimal((this.Order.Dealer_Discount_Amount__c != null ? this.Order.Dealer_Discount_Amount__c : 0) + (this.Order.VF_Discount_Amount__c != null ? this.Order.VF_Discount_Amount__c : 0));
    }

    public String getNetSellingPrice() {
        return formatDecimal((this.Order.Sale_Options_Retail_Base_Price__c != null ? this.Order.Sale_Options_Retail_Base_Price__c : 0) + (this.Order.Vehicle_Retail_Base_Price__c != null ? this.Order.Vehicle_Retail_Base_Price__c : 0) - (this.Order.Dealer_Discount_Amount__c != null ? this.Order.Dealer_Discount_Amount__c : 0) - (this.Order.VF_Discount_Amount__c != null ? this.Order.VF_Discount_Amount__c : 0));
    }

    public Integer getCount() {
        return (this.Order.Dealer_Discount_Amount__c != null ? this.Order.Dealer_Discount_Amount__c : 0) + (this.Order.VF_Discount_Amount__c != null ? this.Order.VF_Discount_Amount__c : 0) > 0 ? 1 : 0;
    }

    public String getInvoiceNumber() {
        return ApexPages.currentPage().getParameters().get('invoiceNumber');
    }

    public String getInvoiceName() {
        return ApexPages.currentPage().getParameters().get('invoiceName');
    }

    public List<GSTPrice> getGSTPriceList() {
        Integer indx = 1;
        List<GSTPrice> prices = new List<GSTPrice>();
        if (this.Order.CGST_Amount__c != null && this.Order.CGST_Amount__c > 0) {
            prices.add(new GSTPrice('CGST', indx, formatDecimal(this.Order.CGST_Rate__c), formatDecimal(this.Order.CGST_Amount__c)));
            indx ++;
        }
        if (this.Order.SGST_Amount__c != null && this.Order.SGST_Amount__c > 0) {
            prices.add(new GSTPrice('SGST', indx, formatDecimal(this.Order.SGST_Rate__c), formatDecimal(this.Order.SGST_Amount__c)));
            indx ++;
        }
        if (this.Order.IGST_Amount__c != null && this.Order.IGST_Amount__c > 0) {
            prices.add(new GSTPrice('IGST', indx, formatDecimal(this.Order.IGST_Rate__c), formatDecimal(this.Order.IGST_Amount__c)));
            indx ++;
        }
        if (this.Order.UGST_Amount__c != null && this.Order.UGST_Amount__c > 0) {
            prices.add(new GSTPrice('UGST', indx, formatDecimal(this.Order.UGST_Rate__c), formatDecimal(this.Order.UGST_Amount__c)));
            indx ++;
        }
        return prices;
    }

    public String getInvoiceDate() {
        return this.order.Status == '' ? Datetime.now().format('dd/MM/yyyy') : (this.order.Invoiced_Date__c != null ? this.order.Invoiced_Date__c.format('dd/MM/yyyy') : null); 
    }

    public String getGrandTotal() {
        return formatDecimal(this.Order.Grand_Total_Without_Additional_Fees__c);
    }

    public String getVehicleExShowroomPrice() {
        return formatDecimal(this.Order.Vehicle_Ex_Showroom_Price__c);
    }
    
    public String getTCSAmount() {
        return formatDecimal(this.Order.TCS_Amount__c);
    }
    
    public String getTCSRate() {
        return formatDecimal(this.Order.TCS_Rate__c);
    }
    
    public class GSTPrice {
        public String label { get; set; }
        public Integer index { get; set; }
        public String rate { get; set; }
        public String amount { get; set; }
        public GSTPrice(String label, Integer index, String rate, String amount) {
            this.label = label;
            this.index = index;
            this.rate = rate;
            this.amount = amount;
        }
    }

    public String formatDecimal(Decimal value) {
        if (value == null) return '';
        value = value.setScale(2, RoundingMode.HALF_UP);
        return value.stripTrailingZeros().format();
    }
}