/*****Class Description*******************
* Name           : DealerPartPurchaseOrderController
* Test Class     : DealerPartPurchaseOrderControllerTest
* Purpose        : Create Order & Order Item for Part Purchase Order
* Version  Author      DateModified     Description
* 1.0      thangnv12    07/04/2025       Init
* *******************************************/
public with sharing class DealerPartPurchaseOrderController {
    private final static Integer MAX_RESULTS = 50;

    /**
     * Get Product Unit Price
     * @param productId Product Id
     * @return PricebookEntry
     */
    @AuraEnabled(cacheable=true)
    public static PricebookEntry getProductUnitPrice(String productId) {
        Id standardPriceBookId = Test.isRunningTest() ? Test.getStandardPricebookId() : [SELECT Id FROM Pricebook2 WHERE IsStandard = TRUE LIMIT 1]?.Id;
        List<PricebookEntry> priceEntries = [
            SELECT UnitPrice, Product2Id, VF_UOM__c 
            FROM PricebookEntry 
            WHERE Pricebook2Id = :standardPriceBookId 
            AND Product2Id = :productId
            AND IsActive = TRUE
        ];
        if (priceEntries.isEmpty()) {
            throw new DealerPurchaseOrderException('UNIT_PRICE_NOT_FOUND');
        }
        return priceEntries[0];
    }

    /**
     * Search products
     * @param searchTerm Search term
     * @param selectedIds Previously selected Ids
     */
    @AuraEnabled(cacheable=true scope='global')
    public static List<VfCustomLookupSearchResult> search(String searchTerm, List<String> selectedIds) {
        searchTerm += '*';
        List<List<SObject>> searchResults = [
            FIND :searchTerm
            IN ALL FIELDS
            RETURNING
                Product2(Id, Name, VF_ProductId__c, Description WHERE id NOT IN :selectedIds AND RecordType.DeveloperName = 'Part')
            LIMIT :MAX_RESULTS
        ];

        List<VfCustomLookupSearchResult> results = new List<VfCustomLookupSearchResult>();

        String icon = 'standard:product';
        Product2[] products = (List<Product2>) searchResults[0];
        for (Product2 product : products) {
            results.add(new VfCustomLookupSearchResult(product.Id, product.VF_ProductId__c, 'Product', icon, product.Name, product.Description));
        }
        results.sort();
        return results;
    }

    /**
     * Create Dealer Part Purchase Order
     * @param {Order} recordOrder: order object
     * @param {List<OrderItem>}
     */
    @AuraEnabled
    public static Id createDealerPartPurchaseOrder(Order recordOrder, List<OrderItem> orderItems){
        try {
            User u = [
                SELECT AccountId
                FROM User
                WHERE Id = :UserInfo.getUserId()
                LIMIT 1
            ];
            Id standardPriceBookId = Test.isRunningTest() ? Test.getStandardPricebookId() : [SELECT Id FROM Pricebook2 WHERE IsStandard = TRUE LIMIT 1]?.Id;
            if (Contact.AccountId == null) throw new DealerPurchaseOrderException('No account found for the current user.');
            recordOrder.AccountId = u.AccountId;
            recordOrder.Status = 'New';
            recordOrder.EffectiveDate = System.today();
            recordOrder.Pricebook2Id  = standardPriceBookId;
            recordOrder.RecordTypeId  = Schema.SObjectType.Order.getRecordTypeInfosByName().get('Dealer Part Purchase').getRecordTypeId();
            if (recordOrder.Id != null) {
                update recordOrder;
            } else {
                insert recordOrder;
            }
            // delete old items
            List<OrderItem> currentItems = [SELECT Id FROM OrderItem WHERE OrderId = :recordOrder.Id];
            if (!currentItems.isEmpty()) delete currentItems;
            // insert new items
            for (OrderItem item : orderItems) {
                item.OrderId = recordOrder.Id;
            }
            insert orderItems;
            return recordOrder.Id;
        } catch (Exception e) {
            System.debug(e.getMessage());
            System.debug(e.getLineNumber());
            throw new DealerPurchaseOrderException(e.getMessage());
        }
    }

    /**
     * Get SAP Order Price
     * @return DealerPurchaseOrderSapHelper.ResponseWrapper SAP order price response
     */
    @AuraEnabled
    public static DealerPurchaseOrderSapHelper.GetPriceResponseWrapper getSapOrderPrice(List<OrderItem> orderItems) {
        try {
            return DealerPurchaseOrderSapHelper.getPartOrderPriceFromSAP(orderItems);
        } catch (Exception e) {
            System.debug(e.getLineNumber());
            System.debug(e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    /**
     * Get Dealer Part Purchase Order
     * @param {String} recordId: order id
     * @return {DealerPurhaseOrderData}
     */
    @AuraEnabled(cacheable=true)
    public static DealerPurhaseOrderData getDealerPartPurchaseOrder(String recordId){
        try {
            List<Order> orders = [SELECT Id, Reference_WO_Number__c, Description, Expected_Delivery_Date__c, Purchase_Order_Category__c, Estimated_Wholesales_Amount__c, (SELECT Id, Product2Id, Product2.Name, Product2.Description, pricebookEntryId, UnitPrice, Quantity, PricebookEntry.ProductCode, PricebookEntry.VF_UOM__c FROM OrderItems) FROM Order WHERE Id = :recordId];
            if (orders.isEmpty()) throw new DealerPurchaseOrderException('No part purchase order found.');
            return new DealerPurhaseOrderData(orders[0]);
        } catch (Exception e) {
            System.debug(e.getMessage());
            System.debug(e.getLineNumber());
            throw new DealerPurchaseOrderException(e.getMessage());
        }
    }

    /**
     * wrapper class for Dealer Purchase Order Data
     */
    public class DealerPurhaseOrderData {
        @AuraEnabled
        public Order header {get; set;}
        @AuraEnabled
        public List<OrderItem> items {get; set;}
        public DealerPurhaseOrderData(Order header) {
            this.header = header;
            this.items = header.OrderItems;
        }
    }

    /**
     * Exception class for Dealer Purchase Order
     */
    public class DealerPurchaseOrderException extends Exception {}
}