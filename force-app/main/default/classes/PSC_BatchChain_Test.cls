/*****Class Description*********************
* Name 					: PSC_BatchChain_Test
* Test Class			: PSC_BatchChain_Test
* Date Created 			: (dd/mm/yyyy) 04/04/2025
* Purpose				: Test class for PSC process (PSC_REST, PSC_workType_Batch, PSC_Vin_Batch, PSC_CallFAMS_Queueable)
* Author				: NinhNN
* Modification History	: NA
* Version	Author			DateModified	Description						
* 0.1		NinhNN       	04/04/2025      Initial Development
* *******************************************/

@isTest
public class PSC_BatchChain_Test {

    class MockFAMSCallout implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{"success": true, "message": "Completed"}');
            return res;
        }
    }

    @isTest static void testFullBatchChain() {
        Test.startTest();

        // Create supporting data
        System_Default__c defaultSetting = new System_Default__c(Name = 'Default', Dummy_Asset__c = null);
        insert defaultSetting;

        Custom_Attribute__mdt dummyVin = new Custom_Attribute__mdt(
            MasterLabel = 'DummyAsset',
            DeveloperName = 'DummyAsset'
        );
        // Normally you'd use Metadata API or Test.loadData for Custom Metadata

        Account acc = new Account(Name = 'Test Dealer', VF_DealerCode__c = 'D001');
        insert acc;

        Asset dummyAsset = new Asset(Name = 'DUMMYVIN', AccountId = acc.Id);
        insert dummyAsset;

        defaultSetting.Dummy_Asset__c = dummyAsset.Id;
        update defaultSetting;

        ProductServiceCampaign psc = new ProductServiceCampaign(
            ProductServiceCampaignName = 'CAMP-001',
            Type = '10',
            Campaign_number__c = 'CAMP-001', 
            Status = 'New', 
            TSB_Number__c = 'CAMP-001', 
            VPAC__c = 'CAMP-001',
            StartDate = date.today()
        );
        insert psc;

        WorkType wt = new WorkType(Name='Test WorkType', EstimatedDuration=1, External_Id__c='EXT123', DurationType='Hours');
        insert wt;
		
        Product_Service_Campaign_Work_Type__c pscwt = new Product_Service_Campaign_Work_Type__c(
            Product_Service_Campaign__c = psc.Id,
            Raw_WorkType__c = 'EXT123',
            Is_Scan__c = false
        );
        insert pscwt;

        ProductServiceCampaignItem psci = new ProductServiceCampaignItem(
            ProductServiceCampaignId = psc.Id,
            Raw_VIN__c = 'DUMMYVIN',
            AssetId = dummyAsset.Id,
            Is_Scan__c = false
        );
        insert psci;

        Test.setMock(HttpCalloutMock.class, new MockFAMSCallout());

        // Call REST entry point
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestUri = '/services/apexrest/psc-batch';
        req.httpMethod = 'POST';
        req.requestBody = Blob.valueOf('{"pscId":"' + psc.Id + '"}');
        RestContext.request = req;
        RestContext.response = res;

        String result = PSC_REST.runBatchJob();
        System.debug('REST Result: ' + result);

        Test.stopTest();
    }
    
     @isTest static void testFullBatchChain_NoParam() {
        Test.setMock(HttpCalloutMock.class, new MockFAMSCallout());
		
        Test.startTest();
        // Call REST entry point
        RestRequest req = new RestRequest();
        RestResponse res = new RestResponse();
        req.requestUri = '/services/apexrest/psc-batch';
        req.httpMethod = 'POST';
        req.requestBody = Blob.valueOf('{"pscId":""}');
        RestContext.request = req;
        RestContext.response = res;
        try{
            String result = PSC_REST.runBatchJob();
        }catch (exception ex){
            System.assert(ex.getMessage().contains('Missing pscId parameter'));
        }
        

        Test.stopTest();
    }
}