@isTest
public class QuoteTriggerHelperTest {
    @testSetup static void setupTestData() {
        SYS_GlobalOnOffSwitch__c globalOnOffSwitchObj = new SYS_GlobalOnOffSwitch__c();
        globalOnOffSwitchObj.SYS_IsTriggerActive__c = TRUE;
        insert globalOnOffSwitchObj;

        Id dealerRecordType =  Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Dealer').getRecordTypeId();
        Account dealer = new Account(Name = 'Test Dealer', RecordTypeId = dealerRecordType,  VF_Level__c = 'Dealer Company', BillingStreet = 'street',BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert dealer;

        Account businessUnit = new Account(Name = 'Test Business Unit', ParentId = dealer.Id, RecordTypeId = dealerRecordType, BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert businessUnit;

        Account customer = new Account(LastName = 'Test Customer', Phone = '**********', VF_Business_Unit__c = dealer.Id, RecordTypeId = VF_Constants.B2C_ACCOUNT_RECORDTYPE,BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert customer;

        Pricebook2 standardPB = new Pricebook2(
            Id = Test.getStandardPricebookId(),
            IsActive = true
        );
        update standardPB;

        Product2 product = new Product2(Name = 'Test Vehicle Product', ProductCode = 'EC15_2023_GC15N', IsActive = true);
        insert product;

        PricebookEntry pbe = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = product.Id, UnitPrice = 100, IsActive = true);
        insert pbe;

        // Option
        Product2 productOption = new Product2(Name = 'Test Options', ProductCode = 'EC15_2023_GC15N_CE11', IsActive = true);
        insert productOption;

        PricebookEntry pbeOption = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = productOption.Id, UnitPrice = 125, IsActive = true);
        insert pbeOption;

        // Create a test Quote with a non-zero amount
        Contact con = [SELECT ID FROM Contact WHERE AccountId = :customer.Id  LIMIT 1];
        Quote testQuote = new Quote(
            Name = 'Test Quote',
            QuoteAccountId = customer.Id,
            ContactId = con.Id,
            Grand_Total_Without_Additional_Fees__c = 12345.67,
            BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH'
        );
        insert testQuote;

    }

    @isTest
    static void testUpdate() {
        Quote rec = [SELECT ID FROM Quote LIMIT 1];
        rec.Vehicle_Information__c = 'VF7 Plus';

        Test.startTest();
        Database.SaveResult result = Database.update(rec, false);
        Test.stopTest();
    }

    @isTest
    static void testDelete() {
        Quote rec = [SELECT ID FROM Quote LIMIT 1];

        Test.startTest();
        Database.DeleteResult result = Database.delete(rec, false);
        Test.stopTest();
    }
}