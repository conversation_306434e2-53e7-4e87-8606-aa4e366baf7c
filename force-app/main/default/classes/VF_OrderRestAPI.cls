/*****Class Description*******************
* Name           : VF_OrderRestAPI
* Test Class     : VF_OrderRestAPITest
* Purpose        : Apex REST service class to manage Order creation and updation
* Modification History  : NA
* Version  Author      DateModified     Description
* 1.0      thanhnm     01/04/2025       Init Create/Update Customer, Booking Order
* 1.1      thangnv12     24/04/2025       create payment 
* *******************************************/
@RestResource(urlMapping='/order')
global with sharing class VF_OrderRestAPI {
    
    @HttpPost global static void doPost() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        Savepoint sp = Database.setSavepoint();

        try {
            // Deserialize the request body into a wrapper class
            System.debug(req.requestBody.toString());
            OrderRequestWrapper request = (OrderRequestWrapper) JSON.deserialize(req.requestBody.toString(), OrderRequestWrapper.class);
            System.debug(JSON.serialize(request));
            // TODO: Validate the input
            if(String.isBlank(request.dealerId)) {
                throw new CustomException('DEALERID_NOT_EMPTY');
            }

            // Check Dealer Account
            List<Account> dealers = [SELECT Id,VF_SaleManager__c  FROM Account WHERE Id = :request.dealerId LIMIT 1];
            if (dealers.isEmpty()) {
                throw new CustomException('Dealer does not exist.');
            }
            Id orderOwnerId = dealers[0].VF_SaleManager__c != null ? dealers[0].VF_SaleManager__c : UserInfo.getUserId();

            // get Vehicle Information
            String vehicleInfo = '';
            if (request.vehicle != null && request.vehicle.model != null) {
                vehicleInfo = String.format('{0} - {1}', new List<String>{request.vehicle.model.name, request.vehicle.model.modelYear});
            }

            // Search for existing Account by Phone
            Account acc;
            if (!String.isBlank(request.phone)) {
                List<Account> accList = [
                    SELECT Id,RecordTypeId
                    FROM Account
                    WHERE Phone = :request.customer.phone
                    LIMIT 1
                ];
                if (!accList.isEmpty()) {
                    acc = accList[0];
                    System.debug(acc);
                    // Only Update Account with new info if B2C Account
                    if (acc.RecordTypeId == VF_Constants.B2C_ACCOUNT_RECORDTYPE) {
                        acc.LastName = request.customer.customerName;
                        acc.PersonEmail = request.customer.customerEmail;
                        acc.VF_Email__c = request.customer.customerEmail;
                        // acc.Phone = request.customer.phone;
                        acc.BillingStreet = request.billingAddress.address1;
                        acc.BillingCity = request.billingAddress.city;
                        acc.BillingStateCode = request.billingAddress.stateCode;
                        acc.BillingPostalCode = request.billingAddress.postalCode;
                        acc.BillingCountryCode = request.billingAddress.countryCode;
                        acc.BillingCountry = request.billingAddress.countryName;
                        acc.ShippingStreet = request.shippingAddress.address1;
                        acc.ShippingCity = request.shippingAddress.city;
                        acc.ShippingStateCode = request.shippingAddress.stateCode;
                        acc.ShippingPostalCode = request.shippingAddress.postalCode;
                        acc.ShippingCountryCode = request.shippingAddress.countryCode;
                        acc.ShippingCountry = request.shippingAddress.countryName;
                        acc.VF_VerifiedOTP__c = request.customer.verifyOTP;

                        update acc;
                    }
                }
            }

            // If no Account found, create one
            if (acc == null) {
                // get Business Unit for Person Account
                List<Account> bus = [SELECT Id,ParentId,VF_Level__c, VF_SaleManager__c  FROM Account WHERE Id = :request.customer.dealerId LIMIT 1];
                System.debug(bus);
                acc = new Account(
                    RecordTypeId = VF_Constants.B2C_ACCOUNT_RECORDTYPE,
                    LastName = request.customer.customerName,
                    PersonEmail = request.customer.customerEmail,
                    Phone = request.customer.phone,
                    BillingStreet = request.billingAddress.address1,
                    BillingCity = request.billingAddress.city,
                    BillingStateCode = request.billingAddress.stateCode,
                    BillingPostalCode = request.billingAddress.postalCode,
                    BillingCountryCode = request.billingAddress.countryCode,
                    BillingCountry = request.billingAddress.countryName,
                    ShippingStreet = request.shippingAddress.address1,
                    ShippingCity = request.shippingAddress.city,
                    ShippingStateCode = request.shippingAddress.stateCode,
                    ShippingPostalCode = request.shippingAddress.postalCode,
                    ShippingCountryCode = request.shippingAddress.countryCode,
                    ShippingCountry = request.shippingAddress.countryName,
                    VF_VerifiedOTP__c = request.customer.verifyOTP,
                    VF_Classification_Types__c = 'Personal'
                );
                if (!bus.isEmpty()) {
                    acc.VF_Business_Unit__c = bus[0].VF_Level__c == 'Dealer Company' ? bus[0].Id : bus[0].ParentId;
                    if (bus[0].VF_Level__c != 'Dealer Company' && bus[0].VF_SaleManager__c != null) {
                        acc.OwnerId = bus[0].VF_SaleManager__c;
                    }
                }
                insert acc;
            }
            System.debug(acc);
            // Get Standard Pricebook
            Id standardPBId = [SELECT Id FROM Pricebook2 WHERE IsStandard = true LIMIT 1]?.Id;

            
            Id accessoryKitId;
            List<VinFast_Promotion__c> promotions = new List<VinFast_Promotion__c>();
            if (request.vehicle != null && request.vehicle.promotions != null && !request.vehicle.promotions.isEmpty()) {
                String kitFamilyCode = null;
                String kitCode = null;
                
                for (Promotion pw : request.vehicle.promotions) {
                    
                    VinFast_Promotion__c obj = new VinFast_Promotion__c();
                    obj.VF_Promotion_ID__c = pw.promotionId;
                    obj.VF_Promotion_Code__c = pw.code;
                    obj.VF_Promotion_Name__c = pw.name;
                    obj.VF_Applied_Term__c = pw.termsApply;
                    obj.VF_Fixed_Amount__c = pw.discountChanged;
                    if (pw.promotionStartDate != null) {
                        obj.VF_Start_Date__c = pw.promotionStartDate.date();
                    }
                    if (pw.promotionExpiryDate != null) {
                        obj.VF_End_Date__c = pw.promotionExpiryDate.date();
                    }
                    obj.VF_Promotion_Type__c = pw.eligibleForVehicle.equalsIgnoreCase('YES') ? 'Vehicle Order Amount Deduction' : 'No Vehicle Order Amount Deduction';
                    obj.External_Key__c = String.format('{0}_{1}', new List<String>{pw.code, pw.promotionId });
                    promotions.add(obj);
                    if (pw.metadata != null && pw.metadata.accessoryKit != null && pw.metadata.accessoryKit.code != null && kitCode == null) {
                        kitFamilyCode = pw.metadata.accessoryKit.familyCode;
                        kitCode = pw.metadata.accessoryKit.code;
                    }
                }
                if (kitFamilyCode != null && kitCode != null) {
                    accessoryKitId =  [SELECT Id FROM Accessory_Kit__c WHERE Name = :kitCode AND Kit_Family_Code__c = :kitFamilyCode AND Active__c = TRUE LIMIT 1]?.Id;
                }
            }

            // Create the Order record
            Order newOrder = new Order(
                AccountId = acc.Id,
                RecordTypeId = VF_Constants.BOOKING_ORDER_RECORDTYPE,
                Vehicle_Information__c = vehicleInfo,
                Type = request.orderType,
                Order_Sources__c = request.orderSources,
                EffectiveDate = Date.today(),
                Booking_Date__c = request.creationDate,
                VF_OnlineOrderNumber__c = request.orderNo,
                Order_Token__c = request.orderToken,
                Booking_Amount__c = request.orderTotal,
                Status = request.status != 'Cancelled' ? request.status : 'New', // must to cancel after create
                Business_Unit__c = request.dealerId,
                Cancelled_Reason__c = request.cancelReason,
                Pricebook2Id = standardPBId,
                OwnerId = orderOwnerId,
                Accessory_Kit__c = accessoryKitId
            );
            insert newOrder;
            if (promotions != null && !promotions.isEmpty()) {
                for (VinFast_Promotion__c obj : promotions) {
                    obj.VF_SO_No__c = newOrder.Id;
                    obj.OwnerId = newOrder.OwnerId;
                    obj.External_Key__c =  newOrder.Id + '_' + obj.External_Key__c;
                }
                insert promotions;
            }

            // Create the Order Items
            List<OrderItem> orderItems = new List<OrderItem>();

            // Insert model Order Item
            if (request.vehicle != null && request.vehicle.model != null && String.isNotBlank(request.vehicle.model.productId)) {
                List<PricebookEntry> modelEntry = [
                    SELECT Id, UnitPrice FROM PricebookEntry 
                    WHERE ProductCode = :request.vehicle.model.productId AND Pricebook2Id = :standardPBId 
                    LIMIT 1
                ];
                if (!modelEntry.isEmpty()) {
                    PricebookEntry entry = modelEntry[0];
                    orderItems.add(new OrderItem(
                        OrderId = newOrder.Id,
                        PricebookEntryId = entry.Id,
                        Quantity = 1,
                        UnitPrice = entry.UnitPrice
                    ));
                }
            }
            
            // Insert options Order Item 
            if (request.vehicle != null && request.vehicle.options != null) {
                for (Options opt : request.vehicle.options) {
                    if (opt.selection != null && String.isNotBlank(opt.selection.productId)) {
                        List<PricebookEntry> optionEntry = [
                            SELECT Id, UnitPrice FROM PricebookEntry 
                            WHERE ProductCode = :opt.selection.productId AND Pricebook2Id = :standardPBId 
                            LIMIT 1
                        ];
                        if (!optionEntry.isEmpty()) {
                            PricebookEntry entry = optionEntry[0];
                            orderItems.add(new OrderItem(
                                OrderId = newOrder.Id,
                                PricebookEntryId = entry.Id,
                                Quantity = 1,
                                UnitPrice = entry.UnitPrice
                            ));
                        }
                    }
                }
            }

            // Insert all collected OrderItems
            if (!orderItems.isEmpty()) {
                insert orderItems;
            }

            // create payment
            if (request.paymentInformation != null) {
                VF_PaymentInformation__c payment = new VF_PaymentInformation__c(
                    VF_Order__c = newOrder.Id,
                    Receipt_Amount__c = request.paymentInformation.amount,
                    Payment_Status__c = request.paymentInformation.paymentStatus,
                    Payment_Type__c = request.paymentInformation.paymentType,
                    Payment_Method__c = request.paymentInformation.paymentMethod,
                    Transaction_ID__c = request.paymentInformation.transactionId,
                    Transaction_Date__c = request.paymentInformation.paymentDate,
                    OwnerId = newOrder.OwnerId
                );
                insert payment;
                if (payment.Payment_Status__c == 'Released') {
                    newOrder.Finish_booking__c = true;
                    update newOrder;
                }
            }

            // Update Cancel order
            if (request.status == 'Cancelled') {
                newOrder.Status = 'Cancelled';
                newOrder.Status_Change_Number__c = 1;
                update newOrder;
            }


            // Prepare the response
            OrderResponseWrapper response = new OrderResponseWrapper();
            response.orderId = newOrder.Id;
            response.accountId = acc.Id;
            response.message = 'Order created successfully.';

            res.responseBody = Blob.valueOf(JSON.serialize(response));
            res.statusCode = 200; // HTTP Success
        } catch (CustomException e) {
            handleError(res, 400, 'BAD_REQUEST', e.getMessage());
            VF_Utility.saveException(e);
            Database.rollback(sp);
        } catch (Exception e) {
            handleError(res, 500, 'SERVER_ERROR', e.getMessage());
            VF_Utility.saveException(e);
            Database.rollback(sp);
        }
    }

    // Handle errors
    private static void handleError(RestResponse res, Integer statusCode, String errorCode, String errorMessage) {
        res.statusCode = statusCode;
        res.responseBody = Blob.valueOf(JSON.serialize(new ErrorResponseWrapper(errorCode, errorMessage)));
    }

    // Custom exception class
    public class CustomException extends Exception {}

    public class OrderRequestWrapper {
		public Customer customer{get;set;}
		public String status{get;set;}
		public String currencyCode{get;set;}
		public String cancelReason{get;set;}
		public Decimal orderTotal{get;set;}
		public String dealerId{get;set;}
		public String orderToken{get;set;}
		public List<ProductLineItems> productLineItems{get;set;}
		public String phone{get;set;}
		public Address billingAddress{get;set;}
		public String orderNo{get;set;}
		public Address shippingAddress{get;set;}
		public Datetime creationDate{get;set;}
		public PaymentInformation paymentInformation{get;set;}
		public String orderSources{get;set;}
		public Vehicle vehicle{get;set;}
		public String orderType{get;set;}
    }

    public class Customer {
        public String dmsCustomerID{get;set;}
        public String phone{get;set;}
        public Boolean verifyOTP{get;set;}
        public String customerName{get;set;}
        public String dealerId{get;set;}
        public String customerEmail{get;set;}
    }

    public class Address {
        public String postalCode{get;set;}
        public String address1{get;set;}
        public String city{get;set;}
        public String countryName{get;set;}
        public String stateCode{get;set;}
        public String countryCode{get;set;}
    }

    public class ProductLineItems {
        public String id{get;set;}
        public String name{get;set;}
        public Decimal price{get;set;}
        public Integer quantity{get;set;}
    }

    public class PaymentInformation {
        public Datetime paymentDate{get;set;}
        public String paymentType{get;set;}
        public String paymentStatus{get;set;}
        public String transactionId{get;set;}
        public String paymentMethod{get;set;}
        public Decimal amount{get;set;}
    }

    public class Vehicle {
        public Model model{get;set;}
        public List<Options> options{get;set;}
        public List<String> packages{get;set;}
        public List<Promotion> promotions{get;set;}
        public String shipping{get;set;}
        public String finance{get;set;}
    }

    public class Model {
        public String productId{get;set;}
        public String modelYear{get;set;}
        public String name{get;set;}
        public Decimal price{get;set;}
    }

    public class Options {
        public String id{get;set;}
        public String title{get;set;}
        public Selection selection{get;set;}
    }

    public class Selection {
        public String id{get;set;}
        public String productId{get;set;}
        public String name{get;set;}
        public Decimal price{get;set;}
    }

    public class OrderResponseWrapper { //@TODO: Add more fields as needed
        public String orderId;
        public String accountId;
        public String message;
    }

    public class ErrorResponseWrapper {
        public String errorCode;
        public String errorMessage;
    
        public ErrorResponseWrapper(String errorCode, String errorMessage) {
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }
    }

    public class Promotion {
        public String promotionId{get;set;}
        public String code{get;set;}
        public String name{get;set;}
        public String termsApply{get;set;}
        public Decimal discountChanged{get;set;}
        public DateTime promotionStartDate{get;set;}
        public DateTime promotionExpiryDate{get;set;}
        public String eligibleForVehicle{get;set;}
        public Metadata metadata{get;set;}
    }

    public class Metadata {
        public AccessoryKit accessoryKit;
    }

    public class AccessoryKit {
        public String familyCode{get;set;}
        public String code{get;set;}
        public String name{get;set;}
        public Decimal amount{get;set;}
    }
}