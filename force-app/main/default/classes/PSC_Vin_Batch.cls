/*****Class Description*********************
* Name 					: PSC_Vin_Batch
* Test Class			: PSC_BatchChain_Test
* Date Created 			: (dd/mm/yyyy) 04/04/2025
* Purpose				: Apex class to handle checking PSCI by batch
* Author				: NinhNN
* Modification History	: NA
* Version	Author			DateModified	Description						
* 0.1		NinhNN       	04/04/2025      Initial Development
* *******************************************/

global class PSC_Vin_Batch implements Database.Batchable<sObject> {
    private Id PscId;
    
    public PSC_Vin_Batch(Id inputPSCId) {
        this.PscId = inputPSCId;
    }
    
    global Database.QueryLocator start(Database.BatchableContext BC) {
        //Get Dummy Asset Id
        Custom_Attribute__mdt dummy_vin = Custom_Attribute__mdt.getInstance('DummyAsset');
        System_Default__c setting = System_Default__c.getOrgDefaults();
        
        //Query Product Service Campaign Item not yet scan
        return Database.getQueryLocator([
            SELECT id, AssetId, Raw_VIN__c, Is_Scan__c 
            FROM ProductServiceCampaignItem 
            WHERE Is_Scan__c = FALSE 
            AND AssetId =: setting.Dummy_Asset__c
            AND ProductServiceCampaignId =: PscId
        ]);
    }
    
    global void execute(Database.BatchableContext BC, List<ProductServiceCampaignItem> lst_psci) {       
        //Create Set of Raw VINs 
        Set<String> Set_vin = new Set<String>();    
        for (ProductServiceCampaignItem psci : lst_psci) {
            set_vin.add(psci.Raw_VIN__c);
        }
        
        //Create Map<VIN, AssetID> 
        Map<String, Id> Map_vin = new Map<String, Id>();       
        for (Asset vin : [SELECT Id, Name FROM Asset WHERE Name IN :set_vin]) {
            Map_vin.put(vin.Name, vin.Id);
        }
        
        //Update PSCI
        for(ProductServiceCampaignItem psci : lst_psci) {        
            psci.Is_Scan__c = true;
            psci.AssetId = (Map_vin.get(psci.Raw_VIN__c) == null ? psci.AssetId : Map_vin.get(psci.Raw_VIN__c));
        }
        system.debug(lst_psci);
        try {
            List<Id> lst_success = new List<Id>();
            Database.SaveResult[] srList = Database.update(lst_psci, false);
            for (Database.SaveResult sr : srList) {
                if (sr.isSuccess()) {
                    // Operation was successful, so get the ID of the record that was processed
                    lst_success.add(sr.getId());
                }
            } 
            If (lst_success.size() > 0){
                List<Product_Service_campaign_work_Type__c> lst_wt = [SELECt id 
                                                                      FROM Product_Service_campaign_work_Type__c
                                                                      WHERE Product_Service_Campaign__c =: PscId
                                                                      AND Is_Scan__c = TRUE
                                                                      AND Work_Type__c != ''];
                If (lst_wt.size() >0){
                    List<PSC_Juntion__c> lst_junction = new List<PSC_Juntion__c>();
                    For (Id t_id : lst_success){
                        For (Product_Service_campaign_work_Type__c t_wt : lst_wt){
                            PSC_Juntion__c n = new PSC_Juntion__c();
                            n.Product_Service_Campaign_Item__c = t_id;
                            n.Product_Service_Campaign_Work_Type__c = t_wt.id;
                            n.External_ID__c = PscId + '_' + t_id + '_' + t_wt.id;
                            lst_junction.add(n);
                        }
                    }
                    Insert lst_junction;
                }
            }
        } catch(Exception e) {
            SYS_Exception__c ex = new SYS_Exception__c(Need_FollowUp__c = TRUE, Description__c = 'PSC_Vin_Batch failed.', Exception_Message__c = e.getMessage(), Type__c = e.getTypeName(), SYS_StackTrace_c__c = e.getStackTraceString()); Insert ex;
        }         
    }   
    
    global void finish(Database.BatchableContext BC) {
        System.enqueueJob(new PSC_CallFAMS_Queueable(PscId));
    }
}