/*****Class Description*********************
* Name 					: PSC_REST
* Test Class			: PSC_BatchChain_Test
* Date Created 			: (dd/mm/yyyy) 04/04/2025
* Purpose				: REST API for MS to call after creating raw PSC
* Author				: NinhNN
* Modification History	: NA
* Version	Author			DateModified	Description						
* 0.1		NinhNN       	04/04/2025      Initial Development
* *******************************************/

@RestResource(urlMapping='/psc-batch')
global with sharing class PSC_REST{
	global class InputWrapper {
        public string pscId;
    }
    
    @HttpPost
    global static String runBatchJob() {
        try {
            RestRequest req = RestContext.request;
    		String body = req.requestBody.toString();
            InputWrapper input = (InputWrapper) JSON.deserialize(body, InputWrapper.class);
            system.debug('Product Service Campaign Id to run batch job: ' + input);
            if (String.isBlank(input.pscId)) {
                throw new CalloutException('Missing pscId parameter');
            }
            Id jobId = Database.executeBatch(new PSC_workType_Batch(input.pscId), 200);
            return 'Batch started. Job Id: ' + jobId;
        } catch (Exception ex) {throw new CalloutException('Failed to start batch: ' + ex.getMessage());
        }
    }
}