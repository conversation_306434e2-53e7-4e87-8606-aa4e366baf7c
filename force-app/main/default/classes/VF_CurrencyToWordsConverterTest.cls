@isTest
private class VF_CurrencyToWordsConverterTest {

    @isTest static void testConvertINR() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = 12345678910L;
        input.currencyCode = 'INR';

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }

    @isTest static void testConvertUSD() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = 12345678910L;
        input.currencyCode = 'USD';

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }

    @isTest static void testConvertUnknownCurrency() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = 12345678910L;
        input.currencyCode = 'JPY';

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }

    @isTest static void testConvertCurrencyNegativeDefault() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = -12345678910L;

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }
    @isTest static void testConvertCurrencyNegativeUSD() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = -12345678910L;
        input.currencyCode = 'USD';

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }
    @isTest static void testConvertCurrencyNegativeINR() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = -12345678910L;
        input.currencyCode = 'INR';

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }

    @isTest static void testConvertCurrencyWithPartDefault() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = 6789.10;

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }
    @isTest static void testConvertCurrencyWithPartUSD() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = 6789.10;
        input.currencyCode = 'USD';

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }
    @isTest static void testConvertCurrencyWithPartINR() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = 6789.10;
        input.currencyCode = 'INR';

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }

    @isTest static void testConvertCurrencyWithZero() {
        VF_CurrencyToWordsConverter.Input input = new VF_CurrencyToWordsConverter.Input();
        input.amount = 0;

        List<VF_CurrencyToWordsConverter.Input> inputs = new List<VF_CurrencyToWordsConverter.Input>{ input };
        Test.startTest();
        List<VF_CurrencyToWordsConverter.Output> results = VF_CurrencyToWordsConverter.convert(inputs);
        Test.stopTest();

        System.assertEquals(1, results.size());
    }
}