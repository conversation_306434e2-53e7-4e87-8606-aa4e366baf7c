/*****Class Description*******************
* Name           : SalesOrderInvoicePdfService
* Test Class     : SalesOrderInvoicePdfServiceTest
* Purpose        : Proivide invocable method to generate PDF from order
* Version  Author      DateModified     Description
* 1.0      thangnv12    15/04/2025       Init
* *******************************************/
public with sharing class SalesOrderInvoicePdfService {
    private static final String DEFAULT_SHARE_TYPE = 'V';
    private static final String DEFAULT_SHARE_VISIBILITY = 'AllUsers';

    @InvocableMethod(label='Generate PDF File - Invoice Sales Order' description='Generate a Proforma or Tax Invoice PDF for the vehicle sales order.')
    public static List<Output> generate(List<Input> inputs) {
        List<Output> results = new List<Output>();
        for (Input input : inputs) {
            String invoiceType = input.invoiceType;
            String invoiceNumber = generateInvoiceNumber(input.recordId, input.invoiceType);
            String invoiceName = input.invoiceType == 'PIV' ? 'Vehicle Proforma Invoice' : 'Vehicle Tax Invoice';
            String pageUrl = '/apex/SalesOrderInvoicePdf?id=' + input.recordId + '&invoiceNumber=' + invoiceNumber + '&invoiceName=' + invoiceName;
            String pdfFileName = invoiceNumber + '.pdf';
            String contentDocumentId = VF_PDFGeneratorService.generateAndSavePDF(input.recordId, pdfFileName, pageUrl, input.shareType, input.visibility);
            createOrderInvoiceHistory(input.recordId, invoiceNumber, invoiceType, contentDocumentId);
            results.add(new Output(contentDocumentId));
        }
        return results;
    }

    public class Input {
        @InvocableVariable(
            required=true  
            label='Record Id'  
            description='Order ID.'
        )
        public String recordId;

        @InvocableVariable(
            required=true 
            label='Invoice Type' 
            description='Select the type of invoice to generate as a PDF. Available options: PIV and TIV (PIV - Vehicle Proforma Invoice; TIV - Vehicle Tax Invoice).'
        )
        public String invoiceType;

        @InvocableVariable(
            label='Share Type' 
            defaultValue='V' 
            description='(Optional) Sharing type for the file. Options: V = Viewer, C = Collaborator, I = Inferred.'
        )
        public String shareType;
    
        @InvocableVariable(
            label='Visibility' 
            defaultValue='AllUsers' 
            description='(Optional) Who can see the uploaded file. Options: AllUsers, InternalUsers, SharedUsers.'
        )
        public String visibility;
    }

    public class Output {
        @InvocableVariable(label='Content Document Id')
        public String contentDocumentId;
        public Output(String contentDocumentId) {
            this.contentDocumentId = contentDocumentId;
        }
    }

    /**
     * Get the Order Invoice History
     * @param recordId Id of the Sales Order record
     * @param invoiceNumber String of the invoice number
     * @param invoiceType String invoiceType of the file corresponding to the Sales Order Invoice Type ('PIV' for Proforma Invoice, 'TIV' for Tax Invoice)
     * @param contentDocumentId Id of the ContentDocument of the generated file
     * @return String Id of the Order Invoice History record
     */
    public static String createOrderInvoiceHistory(Id recordId, String invoiceNumber, String invoiceType, String contentDocumentId) {
        Order_Invoice_History__c orderInvoiceHistory = new Order_Invoice_History__c();
        Order ord = [SELECT Id, TotalAmount, Invoiced_Date__c FROM Order WHERE Id = :recordId];
        orderInvoiceHistory.Invoice_Amount__c = ord.TotalAmount ;
        orderInvoiceHistory.Invoice_Date__c = invoiceType == 'TIV' ? ord.Invoiced_Date__c : System.now();
        orderInvoiceHistory.Name = invoiceNumber;
        orderInvoiceHistory.Invoice_Type__c = invoiceType;
        orderInvoiceHistory.Order__c = recordId;
        insert orderInvoiceHistory;

        ContentDocumentLink cdl = new ContentDocumentLink();
        cdl.ContentDocumentId = contentDocumentId;
        cdl.LinkedEntityId = orderInvoiceHistory.Id;
        cdl.ShareType = DEFAULT_SHARE_TYPE;
        cdl.Visibility = DEFAULT_SHARE_VISIBILITY;
        insert cdl;
        
        return orderInvoiceHistory.Id;
    }

    /**
     * Generate the next available file title based on invoiceType and existing ContentDocuments.
     * @param recordId Id of the Sales Order record
     * @param invoiceType String invoiceType of the file corresponding to the Sales Order Invoice Type ('PIV-' for Proforma Invoice, 'TIV-' for Tax Invoice)
     * @return String suffix of the file to be generated
     */
    public static String generateInvoiceNumber(Id recordId, String invoiceType) {
        List<Order> orders = [SELECT Id, OrderNumber FROM Order WHERE Id = :recordId];
        if (orders.isEmpty()) return null;
        Order ord = orders[0];
        List<ContentDocumentLink> links = [
            SELECT ContentDocumentId, ContentDocument.Title
            FROM ContentDocumentLink
            WHERE LinkedEntityId = :recordId
            AND ContentDocument.Title LIKE :invoiceType + '%'
            ORDER BY ContentDocument.Title DESC
            LIMIT 1
        ];

        Integer nextSuffix = 1;
        if (!links.isEmpty()) {
            String lastTitle = links[0].ContentDocument.Title;
            Integer suffixLength = 3;
            // Step 1: Remove file extension (e.g., .pdf, .docx, etc.)
            Integer dotIndex = lastTitle.lastIndexOf('.');
            if (dotIndex != -1) {
                lastTitle = lastTitle.substring(0, dotIndex);
            }

            // Step 2: Get the suffix after the last '-'
            Integer lastDashIndex = lastTitle.lastIndexOf('-');
            if (lastDashIndex != -1 && lastDashIndex < lastTitle.length() - 1) {
                String suffixPart = lastTitle.substring(lastDashIndex + 1);
                if (suffixPart != null && Pattern.matches('\\d+', suffixPart)) {
                    nextSuffix = Integer.valueOf(suffixPart) + 1;
                    suffixLength = suffixPart.length(); 
                }
            }
        }

        String suffixStr = '000' + String.valueOf(nextSuffix);
        suffixStr = suffixStr.substring(suffixStr.length() - 3);
        
        return invoiceType + '-' + ord.OrderNumber + '-' + suffixStr;
    }
}