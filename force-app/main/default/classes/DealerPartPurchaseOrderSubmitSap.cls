/*****Class Description*******************
* Name           : DealerPartPurchaseOrderSubmitSap
* Test Class     : DealerPartPurchaseOrderSubmitSapTest
* Purpose        : Synchronize part purchase to SAP
* Version  Author      DateModified     Description
* 1.0      thangnv12    28/04/2025       Init
* *******************************************/
public without sharing class DealerPartPurchaseOrderSubmitSap {
    
    @InvocableMethod(label='Dealer Purchase Order - Synchronize Part Purchase to SAP' description='Callout SAP API to Synchronize Part Purchase Order' category='Purchase Order')
    public static List<Output> execute(List<Input> requests) {
        if (requests == null || requests.isEmpty()) return null;
        Set<Id> orderIds = new Set<Id>();
        List<Output> outputList = new List<Output>();
        for (Input ord : requests) {
            orderIds.add(ord.orderId);
        }
        // The list of logs to be inserted
        List<Outbound_Log__c> logs = new List<Outbound_Log__c>();
        // The list of orders to be updated
        List<Order> orderToUpdates = new List<Order>();
        
        for (Order ord :[SELECT BillingCountryCode, Customer_Order_Number__c, Purchase_Order_Category__c, RecordType.Name, Account.VF_Level__c, Account.Parent.VF_DealerCode__c, Account.VF_DealerCode__c, OrderNumber, CreatedDate, Expected_Delivery_Date__c, Account.Parent.Plant_Part__c, Account.Parent.Plant_Vehicle__c, (SELECT OrderItemNumber, Product2.VF_ProductId__c, Quantity, PricebookEntry.VF_UOM__c, Product2.VF_Model__c, Product2.VF_Trim__c, Product2.Family, Product2.VF_FamilyValue__c FROM OrderItems) FROM Order WHERE Id IN :orderIds]) {
            DealerPurchaseOrderSapHelper.SyncOrderResult rs = DealerPurchaseOrderSapHelper.makeCalloutSyncOrder(ord);
            if (rs.log != null) {
                logs.add(rs.log);
            }
            if (rs.order != null) {
                orderToUpdates.add(rs.order);
            } 
        }
        for (Order upd :orderToUpdates) {
            outputList.add(new Output(upd.Sync2SAP__c, upd.SAP_Response_message__c));
        }
        insertLog(logs);
        updateOrder(orderToUpdates);
        return outputList;
    }

    /**
     * Update the orders
     * 
     * @param orderToUpdates The list of orders to be updated
     */
    public static void updateOrder (List<Order> orderToUpdates){
        if (!orderToUpdates.isEmpty()) {
            Database.SaveResult[] results = Database.update(orderToUpdates, false);
            List<String> errorMessages = new List<String>();
    
            for (Integer i = 0; i < results.size(); i++) {
                if (results[i].isSuccess()) continue;
                for (Database.Error err : results[i].getErrors()) errorMessages.add('Record Id: ' + orderToUpdates[i].Id + ' - Error: ' + err.getMessage());
            }
            if (!errorMessages.isEmpty()) throw new DealerPurchaseOrderException('Failed on update Order: ' + String.join(errorMessages, ' | '));
        }
    }

    /**
     * Insert the logs
     * 
     * @param logs The list of logs to be inserted
     */
    public static void insertLog(List<Outbound_Log__c> logs){
        if (!logs.isEmpty()) {
            try {
                Database.insert(logs, false);
            } catch (Exception e) {
                System.debug(e);
            }
        }
    }
        
    public class Input {
        @InvocableVariable(
            label='Order Id' 
            required = true
            description='Id of the Order'
        )
        public Id orderId;
    }

    public class Output {
        @InvocableVariable(label='Is Success')
        public Boolean success;
        @InvocableVariable(label='Result Message')
        public String message;
        public Output(Boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }

    public class DealerPurchaseOrderException extends Exception {}
}