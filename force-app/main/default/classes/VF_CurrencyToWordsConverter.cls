/*****Class Description*******************
* Name           : VF_CurrencyToWordsConverter
* Test Class     : VF_CurrencyToWordsConverterTest
* Purpose        : Provide helper methods to interact with SAP
* Version  Author      DateModified     Description
* 1.0      thangnv12    15/04/2025       Init
* *******************************************/
public with sharing class VF_CurrencyToWordsConverter {
    
    /**
     * Invocable method to call from Flow
     * @param amount Decimal
     * @param currency code (ex: USD, VND, INR, ...). Optional, ignore to default Indian English
     */
    @InvocableMethod(label='Convert Money to Words' description='Convert decimal money amount to words')
    public static List<Output> convert(List<Input> inputs) {
        List<Output> results = new List<Output>();
        for (Input input : inputs) {
            String words = convertAmountToWords(input.amount, input.currencyCode);
            results.add(new Output(words));
        }
        return results;
    }

    public static String convertAmountToWords(Decimal amount, String currencyCode) {
        switch on currencyCode {
            when 'INR' {
                // indian
                return VF_CurrencyToWordsIndianHindi.convert(amount);
            }
            when 'USD' {
                // indian
                return VF_CurrencyToWordsEnglish.convert(amount);
            }
            when else {
                // default english indian
                return VF_CurrencyToWordsIndian.convert(amount);
            }
        }
    }

    public class Output {
        @InvocableVariable
        public String amountInWords;
        public Output (String words){
            this.amountInWords = words;
        }
    }

    public class Input {
        @InvocableVariable(required=true)
        public Decimal amount;

        @InvocableVariable(required=true)
        public String currencyCode;
    }
}