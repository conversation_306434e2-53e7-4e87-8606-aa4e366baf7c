public with sharing class VF_CurrencyToWordsIndian {
    // Convert full decimal amount to words
    public static String convert(Decimal amount) {
        if (amount == null || amount == 0) {
            return 'Zero Rupees';
        }
        String result = '';
        Boolean isNegative = amount < 0;
        if (isNegative) {
            result = 'Minus ';
            amount = amount.abs();
        }

        Long rupees = amount.longValue();
        Integer paisa = ((amount - rupees) * 100).round(System.RoundingMode.HALF_UP).intValue();
        result += convertDecimalToWords(rupees) + ' Rupees';

        if (paisa > 0) {
            result += ' and ' + convertDecimalToWords(paisa) + ' Paisa';
        }

        return result;
    }

    // Convert integer part to words in Indian system
    private static String convertDecimalToWords(Long input) {
        String[] units = new String[]{ '', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine',
                                       'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen',
                                       'Seventeen', 'Eighteen', 'Nineteen' };

        String[] tens = new String[]{ '', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety' };
        String words = '';
        if ((input / 10000000) > 0) {
            words += convertDecimalToWords(input / 10000000) + ' Crore ';
            input = Math.mod(input, 10000000);
        }

        if ((input / 100000) > 0) {
            words += convertDecimalToWords(input / 100000) + ' Lakh ';
            input = Math.mod(input, 100000);
        }

        if ((input / 1000) > 0) {
            words += convertDecimalToWords(input / 1000) + ' Thousand ';
            input = Math.mod(input, 1000);
        }

        if ((input / 100) > 0) {
            words += convertDecimalToWords(input / 100) + ' Hundred ';
            input = Math.mod(input, 100);
        }

        if (input > 0) {
            if (input < 20) {
                words += units[(Integer)input];
            } else {
                words += tens[(Integer)input / 10];
                if ((Math.mod(input, 10)) > 0) {
                    words += '-' + units[(Integer)Math.mod(input, 10)];
                }
            }
        }

        return words.trim();
    }
}