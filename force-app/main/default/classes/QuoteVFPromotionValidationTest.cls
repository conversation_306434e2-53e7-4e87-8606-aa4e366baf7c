@isTest
public with sharing class QuoteVFPromotionValidationTest {

    // Mock class for successful HTTP response
    private class MockHttpSuccessResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{"success":true,"data":{"promotions":[{"promotionId":"P1","code":"PROMO1"}]}}');
            return res;
        }
    }

    // Mock class for failed HTTP response
    private class MockHttpFailedResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(400);
            res.setBody('{"success":false,"message":"Error occurred"}');
            return res;
        }
    }

    @testSetup
    static void setupTestData() {
        Id dealerRecordType =  Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Dealer').getRecordTypeId();
        Account dealer = new Account(Name = 'Test Dealer', RecordTypeId = dealerRecordType,  VF_Level__c = 'Dealer Company', BillingStreet = 'street',BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert dealer;

        Account businessUnit = new Account(Name = 'Test Business Unit', ParentId = dealer.Id, RecordTypeId = dealerRecordType, BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert businessUnit;

        Account customer = new Account(LastName = 'Test Customer', Phone = '**********', VF_Business_Unit__c = dealer.Id, RecordTypeId = VF_Constants.B2C_ACCOUNT_RECORDTYPE,BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert customer;

        Pricebook2 standardPB = new Pricebook2(
            Id = Test.getStandardPricebookId(),
            IsActive = true
        );
        update standardPB;

        Product2 product = new Product2(Name = 'Test Vehicle Product', ProductCode = 'EC15_2023_GC15N', Family = 'MOD', IsActive = true);
        insert product;

        PricebookEntry pbe = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = product.Id, UnitPrice = 100, IsActive = true);
        insert pbe;

        // Option
        Product2 productOption = new Product2(Name = 'Test Options', ProductCode = 'EC15_2023_GC15N_CE11', IsActive = true);
        insert productOption;

        PricebookEntry pbeOption = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = productOption.Id, UnitPrice = 125, IsActive = true);
        insert pbeOption;

        Accessory_Kit__c kit = new Accessory_Kit__c(Kit_Name__c = 'AK11', Kit_Pricing__c = 17000, Active__c = true);
        insert kit;

        Contact con = [SELECT ID FROM Contact WHERE AccountId = :customer.Id  LIMIT 1];
        Quote testQuote = new Quote(
            Name = 'Test Quote',
            QuoteAccountId = customer.Id,
            ContactId = con.Id,
            Pricebook2Id = standardPB.Id,
            Grand_Total_Without_Additional_Fees__c = 12345.67,
            ExpirationDate = Date.today(),
            Accessory_Kit__c = kit.Id,
            BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH'
        );
        insert testQuote;

        List<QuoteLineItem> quoteLineItems = new List<QuoteLineItem>();
        quoteLineItems.add(new QuoteLineItem(
            QuoteId = testQuote.Id,
            PricebookEntryId = pbe.Id,
            Quantity = 1,
            UnitPrice = 35000.0
        ));

        quoteLineItems.add(new QuoteLineItem(
            QuoteId = testQuote.Id,
            PricebookEntryId = pbeOption.Id,
            Quantity = 1,
            UnitPrice = 200.0
        ));

        // Insert the QuoteLineItem records
        insert quoteLineItems;

        // Create test VinFast Promotions
        VinFast_Promotion__c promotion = new VinFast_Promotion__c(
            VF_SQ_No__c = testQuote.Id,
            VF_Promotion_Code__c = 'PROMO1',
            VF_Promotion_ID__c = 'P2',
            VF_Status__c = 'Potential'
        );
        insert promotion;
    }

    @isTest
    static void testCheckPromotion_Success() {
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Prepare input
        Quote testQuote = [SELECT Id FROM Quote LIMIT 1];
        QuoteVFPromotionValidation.InputWrapper input = new QuoteVFPromotionValidation.InputWrapper();
        input.quoteId = testQuote.Id;
        List<QuoteVFPromotionValidation.InputWrapper> inputs = new List<QuoteVFPromotionValidation.InputWrapper>{ input };

        // Call the method
        Test.startTest();
        List<QuoteVFPromotionValidation.OutputWrapper> results = QuoteVFPromotionValidation.checkPromotion(inputs);
        Test.stopTest();

        // Assert results
        System.assertEquals(1, results.size(), 'There should be one result');
        // System.assert(results[0].isValid, 'The result should be valid');
    }

    @isTest
    static void testCheckPromotion_Failure() {
        Test.setMock(HttpCalloutMock.class, new MockHttpFailedResponse());

        // Prepare input
        Quote testQuote = [SELECT Id FROM Quote LIMIT 1];
        QuoteVFPromotionValidation.InputWrapper input = new QuoteVFPromotionValidation.InputWrapper();
        input.quoteId = testQuote.Id;
        List<QuoteVFPromotionValidation.InputWrapper> inputs = new List<QuoteVFPromotionValidation.InputWrapper>{ input };

        // Call the method
        Test.startTest();
        List<QuoteVFPromotionValidation.OutputWrapper> results = QuoteVFPromotionValidation.checkPromotion(inputs);
        Test.stopTest();

        // Assert results
        System.assertEquals(1, results.size(), 'There should be one result');
        System.assert(!results[0].isValid, 'The result should not be valid');
    }

    @isTest
    static void testCheckPromotion_NoPromotions() {
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Prepare input for a quote with no promotions
        Quote testQuote = [SELECT Id FROM Quote LIMIT 1];
        delete [SELECT Id FROM VinFast_Promotion__c WHERE VF_SQ_No__c = :testQuote.Id];

        QuoteVFPromotionValidation.InputWrapper input = new QuoteVFPromotionValidation.InputWrapper();
        input.quoteId = testQuote.Id;
        List<QuoteVFPromotionValidation.InputWrapper> inputs = new List<QuoteVFPromotionValidation.InputWrapper>{ input };

        // Call the method
        Test.startTest();
        List<QuoteVFPromotionValidation.OutputWrapper> results = QuoteVFPromotionValidation.checkPromotion(inputs);
        Test.stopTest();

        // Assert results
        System.assertEquals(1, results.size(), 'There should be one result');
        System.assert(results[0].isValid, 'The result should be valid');
    }
}
