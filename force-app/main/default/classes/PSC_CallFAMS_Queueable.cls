/*****Class Description*********************
* Name 					: PSC_CallFAMS_Queueable
* Test Class			: PSC_BatchChain_Test
* Date Created 			: (dd/mm/yyyy) 04/04/2025
* Purpose				: Apex class to call out to <PERSON><PERSON> for sending the result of PSC creation.
* Author				: NinhNN
* Modification History	: NA
* Version	Author			DateModified	Description						
* 0.1		NinhNN       	04/04/2025      Initial Development
* *******************************************/

public class PSC_CallFAMS_Queueable implements Queueable, Database.AllowsCallouts{
    private Id recordId;  // Input parameter

    // Constructor to accept input
    public PSC_CallFAMS_Queueable(Id recordId) {
        this.recordId = recordId;
    }

    public void execute(QueueableContext context) {
        system.debug(recordId);
        String payload = '';
        System_Default__c cs = System_Default__c.getOrgDefaults();
        List<ProductServiceCampaign> lst_psc = [SELECT id, ProductServiceCampaignName, Campaign_number__c
                                                FROM ProductServiceCampaign 
                                                WHERE id =: recordId];
        List<ProductServiceCampaignItem> lst_success = [SELECT id, Asset.Name
                                                        FROM ProductServiceCampaignItem
                                                        WHERE ProductServiceCampaignId =: recordId
                                                        AND Is_scan__c = TRUE
                                                        AND AssetID !=: cs.Dummy_Asset__c];
        List<ProductServiceCampaignItem> lst_fail = [SELECT id, Asset.Name
                                                     FROM ProductServiceCampaignItem
                                                     WHERE ProductServiceCampaignId =: recordId
                                                     AND Is_scan__c = TRUE
                                                     AND AssetID =: cs.Dummy_Asset__c];
        // build header
        Map<String, Object> params = new Map<String,Object>();
        params.put('fieldActionNumber', lst_psc[0].Campaign_number__c);
        params.put('campaignId', lst_psc[0].ProductServiceCampaignName);
        // build result
        Map<String, Object> result = new Map<String, Object>();
        result.put('code', '000000');
        result.put('status', 'Success');
        result.put('message', 'Sync completed');
        result.put('createdDate', Datetime.now());
        // build successed items
        List<Map<String, Object>> succeededItems = new List<Map<String, Object>>();              
        for (ProductServiceCampaignItem vin : lst_success) {
            Map<String, Object> itm = new Map<String, Object>(); 
            itm.put('key', vin.Asset.Name);
            itm.put('value', '');
            succeededItems.add(itm);
        }
        result.put('succeededItems',succeededItems);
        // build failed items
        List<Map<String, Object>> FailedItems = new List<Map<String, Object>>();              
        for (ProductServiceCampaignItem vin : lst_fail) {
            Map<String, Object> itm = new Map<String, Object>(); 
            itm.put('key', vin.Asset.Name);
            itm.put('value', 'VIN not found.');
            FailedItems.add(itm);
        }
        result.put('failedItems',FailedItems);
        params.put('result', result);
        payload = JSON.serialize(params);
        system.debug(payload);
        
        // Define the endpoint and headers using NameCredential
        String url = 'callout:MuleSoft_Credential/dms/api/v1/campaign/status';
        String method = 'POST';
        Map<String, String> headers = new Map<String, String>{'Content-Type' => 'application/json'};
            try {
                HttpResponse response = VF_MakeCalloutGeneric.makeCallout(url, method, headers, payload);
                System.debug(response.getBody());
                FAMSResponseWrapper responseMS = (FAMSResponseWrapper)JSON.deserialize(response.getbody(), FAMSResponseWrapper.class);
                System.debug(JSON.serialize(responseMS));
                if (payload != null && payload.length() > 32000) {
                    payload = payload.substring(0, 32000);
                }
                VF_Utility.createOutboundLog(url, method, payload, response, null);
                if (response.getStatusCode() == VF_Constants.SUCCESS_CODE) {
                    if (responseMS.success == TRUE){
                        ProductServiceCampaign psc = new ProductServiceCampaign();
                        psc.Id = recordId;
                        psc.Is_Completed__c = true;
                        Update psc;
                    }
                    else{
                        SYS_Exception__c se = new SYS_Exception__c(Type__c = 'Batch', Exception_Message__c = responseMS.message, Description__c = 'Product Service Campaign is failed to send status to FAMS : ' + recordId, Need_FollowUp__c = TRUE); Insert se;
                    }
                }else{
                    SYS_Exception__c se = new SYS_Exception__c(Type__c = 'Batch', Exception_Message__c = responseMS.message, Description__c = 'Product Service Campaign is failed to send status to FAMS : ' + recordId, Need_FollowUp__c = TRUE); Insert se;
                }
            } catch (Exception ex) {
                SYS_Exception__c se = new SYS_Exception__c(Type__c = 'Batch', Exception_Message__c = ex.getMessage(), Description__c = 'Product Service Campaign is failed to send status to FAMS : ' + recordId, Need_FollowUp__c = TRUE); Insert se;
            }
    }
    public class FAMSResponseWrapper {
        @AuraEnabled public String message {get; set;}
        @AuraEnabled public Boolean success {get; set;}
    }
}