/*****Class Description*******************
* Name           : OrderTaxesController
* Test Class     : 
* Purpose        : Get taxes, prices for an order from SAP
* Modification History  : NA
* Version  Author      DateModified     Description
* 1.0      thanhnm     31/03/2025       Init
* *******************************************/
public with sharing class OrderTaxesController {

    @AuraEnabled(cacheable=false)
    public static String getOrderTaxesFromSAP(String recordId) {
        Order objOrder = [SELECT Id,Status,EffectiveDate,BillingStateCode,Business_Unit__r.VF_DealerCode__c, Business_Unit__r.BillingStateCode,Business_Unit__r.BillingCountryCode,VF_Discount_Amount__c,(SELECT Id,OrderItemNumber,Order_Product_Family_Code__c,Product2.ProductCode,Product2.VF_FamilyValue__c,Product2.RecordType.DeveloperName,PricebookEntry.VF_UOM__c from OrderItems) FROM Order WHERE Id = :recordId LIMIT 1];

        // check Order status
        if (objOrder.Status != 'New') {
            return 'The Order status cannot be sent to SAP.';
        }

        String requestBody = buildPayload(objOrder); System.debug(requestBody);
        // Define the endpoint and headers
        String url = 'callout:MuleSoft_Credential/dms/api/v1/taxes-pricing';
        String method = 'POST';
        Map<String, String> headers = new Map<String, String>{
            'Content-Type' => 'application/json'
        };
        try {
            HttpResponse response = VF_MakeCalloutGeneric.makeCallout(url, method, headers, requestBody);
            System.debug(response.getBody());
            SAPResponseWrapper responseMS = (SAPResponseWrapper)JSON.deserialize(response.getbody(), SAPResponseWrapper.class);
            System.debug(JSON.serialize(responseMS));
            VF_Utility.createOutboundLog(url, method, requestBody, response, null);
            if (response.getStatusCode() == VF_Constants.SUCCESS_CODE) {
                // check SAP status code
                if (responseMS.result.status == 'S') { // success
                    for(cls_items item : responseMS.data.items) {
                        for(cls_priceObject price : item.priceObject) {
                            if (price.priceType == 'JTCB') { 
                                objOrder.Vehicle_Ex_Showroom_Price__c = price.priceValue;
                            }
                            if (price.priceType == 'JOSG') { 
                                objOrder.SGST_Rate__c = price.priceAmount;
                                objOrder.SGST_Amount__c = price.priceValue;
                            }
                            if (price.priceType == 'JOCG') { 
                                objOrder.CGST_Rate__c = price.priceAmount;
                                objOrder.CGST_Amount__c = price.priceValue;
                            }
                            if (price.priceType == 'JOIG') { 
                                objOrder.IGST_Rate__c = price.priceAmount;
                                objOrder.IGST_Amount__c = price.priceValue;
                            }
                            if (price.priceType == 'JOUG') { 
                                objOrder.UGST_Rate__c = price.priceAmount;
                                objOrder.UGST_Amount__c = price.priceValue;
                            }
                            if (price.priceType == 'JTC1') { 
                                objOrder.TCS_Rate__c = price.priceAmount;
                                objOrder.TCS_Amount__c = price.priceValue;
                            }
                        }
                    }
                    update objOrder;

                    return 'Get Tax succesfully';
                } else {
                    throw new CalloutException('SAP message: ' + responseMS.result.message);
                }
            } else {
                throw new CalloutException('Status Code: ' + response.getStatusCode());
            }
        } catch (Exception ex) {
            throw new AuraHandledException('Please check with Mulesoft team: ' + ex.getMessage());
        }
    }

    public static String buildPayload(Order rec) {
        String payload = '';

        // Sprint 21 : get custom metadata with getAll
        Map<String, String> stateSAPCode = new Map<String, String>();
        Map<String, State_Masterdata__mdt> mapStateMetadata = State_Masterdata__mdt.getAll();
        for(String mapKey : mapStateMetadata.keySet()) {
            stateSAPCode.put(mapStateMetadata.get(mapKey).State_Code__c, mapStateMetadata.get(mapKey).State_Code_SAP__c);
        }

        Map<String, Object> params = new Map<String,Object>();
        DateTime currentDateTime = DateTime.now();
        // build header
        params.put('requestID', String.format('{0}_{1}', new List<String>{rec.Id, currentDateTime.format('yyyyMMdd_HHmmss')}));
        params.put('transactionID', rec.Id);
        params.put('transactionType', '1'); // Order
        // params.put('deliveryPriority', '00'); // ???
        String formatDate = 
            String.valueOf(rec.EffectiveDate.day()).leftPad(2, '0') + '.' +
            String.valueOf(rec.EffectiveDate.month()).leftPad(2, '0') + '.' +
            String.valueOf(rec.EffectiveDate.year());
        params.put('transactionDate', formatDate);
        params.put('buyerLocation', stateSAPCode.get(rec.BillingStateCode));
        params.put('sellerLocation', rec.Business_Unit__r.VF_DealerCode__c);
        params.put('currency', 'INR');
        params.put('country', rec.Business_Unit__r.BillingCountryCode);

        // build items
        List<Map<String, Object>> items = new List<Map<String, Object>>();
        Map<String, Object> itm = new Map<String, Object>();
        itm.put('quantity', 1);
        itm.put('basedPrice', null);
        itm.put('discountAmount', rec.VF_Discount_Amount__c);
        itm.put('billClassification', 'C');
        List<Map<String, Object>> vehicleOptions = new List<Map<String, Object>>();

        // get lineitem number of Vehicle product
        String last6Digits = ''; // last 6 digits of Vehicle product lineitemnumber
        for (OrderItem oItem : rec.OrderItems) {
            if(oItem.Order_Product_Family_Code__c == 'MOD') { // Vehicle
                String lineItemNumber = oItem.OrderItemNumber;
                last6Digits = lineItemNumber.substring(lineItemNumber.length() - 6);
                itm.put('transactionItem', last6Digits);
                itm.put('productCode', oItem.Product2.ProductCode);
                itm.put('productType', VF_Constants.SAP_TAX_PRODUCT_TYPE.get(oItem.Product2.RecordType.DeveloperName));
                itm.put('UOM', oItem.PricebookEntry.VF_UOM__c);
                break;
            }      
        }
        
        for (OrderItem oItem : rec.OrderItems) {
            // Vehcile + Options
            Map<String, Object> option = new Map<String, Object>();
            option.put('itemID', last6Digits);
            option.put('productFamily', oItem.Order_Product_Family_Code__c);
            option.put('familyValue', oItem.Product2.VF_FamilyValue__c);
            vehicleOptions.add(option);
        }
        itm.put('vehicleOptions', vehicleOptions);
        items.add(itm);
        params.put('items', items);

        payload = JSON.serialize(params);

        return payload;
    }

    public class SAPResponseWrapper {
        @AuraEnabled public cls_result result {get; set;}
        @AuraEnabled public cls_data data {get; set;}
    }
    public class cls_result {
		@AuraEnabled public String code;
		@AuraEnabled public String message;
		@AuraEnabled public String status;
    }
    public class cls_data {
		@AuraEnabled public String transactionID;
		@AuraEnabled public Integer transactionType;
		@AuraEnabled public cls_items[] items;
    }
    public class cls_items {
		@AuraEnabled public String productCode;
		@AuraEnabled public Decimal quantity;
		@AuraEnabled public cls_priceObject[] priceObject;
	}
	public class cls_priceObject {
		@AuraEnabled public String priceType;
		@AuraEnabled public Decimal priceAmount;
		@AuraEnabled public Decimal priceValue;
		@AuraEnabled public Decimal unitPerPrice;
	}
}