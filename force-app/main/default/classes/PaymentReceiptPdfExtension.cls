public class PaymentReceiptPdfExtension {
    public VF_PaymentInformation__c receipt { get; set; }

    public PaymentReceiptPdfExtension(ApexPages.StandardController stdCtrl) {
        this.receipt = (VF_PaymentInformation__c)stdCtrl.getRecord();
        this.receipt = [SELECT Id, Name, Issued_Date__c, Receipt_Amount__c, Bank_Name__c, Payment_Method__c, Transaction_ID__c, Transaction_Date__c, Payment_Type__c, VF_Order__r.Vehicle_Information__c, VF_Order__r.Account.Name, VF_Order__r.Account.BillingAddress, VF_Order__r.Account.BillingStreet, VF_Order__r.Account.BillingCity, VF_Order__r.Account.BillingState, VF_Order__r.Account.BillingCountry,VF_Order__r.Account.VF_IdentificationNo__c, VF_Order__r.PAN__c, VF_Order__r.Account.Phone, VF_Order__r.Account.VF_Hotline__c, VF_Order__r.Account.AccountNumber FROM VF_PaymentInformation__c WHERE Id = :receipt.Id];
    }

    public String getAccountBillingAddress() {
        String str = '';
        if (this.receipt.VF_Order__r.Account.BillingStreet != null) {
            str += this.receipt.VF_Order__r.Account.BillingStreet + ', ';
        }
        if (this.receipt.VF_Order__r.Account.BillingCity != null) {
            str += this.receipt.VF_Order__r.Account.BillingCity + ', '; 
        }
        if (this.receipt.VF_Order__r.Account.BillingState != null) {
            str += this.receipt.VF_Order__r.Account.BillingState + ', ';
        }
        if (this.receipt.VF_Order__r.Account.BillingCountry != null) {
            str += this.receipt.VF_Order__r.Account.BillingCountry;   
        }
        return str;
    }

    public String getGstNo() {
        return this.receipt.VF_Order__r.Account.VF_IdentificationNo__c != null ? this.receipt.VF_Order__r.Account.VF_IdentificationNo__c : this.receipt.VF_Order__r.PAN__c;
    }

    public String getPhone() {
        return this.receipt.VF_Order__r.Account.Phone != null ? this.receipt.VF_Order__r.Account.Phone : this.receipt.VF_Order__r.Account.VF_Hotline__c;
    }
    
    public String getPaymentDate() {
        Datetime dateTimeTmp = Datetime.newInstance(this.receipt.Issued_Date__c, Time.newInstance(0, 0, 0, 0));
        return dateTimeTmp != null ? dateTimeTmp.format('dd/MM/yyyy') : null; 
    }

    public String getTransactionDate() {
        return this.receipt.Transaction_Date__c != null ? this.receipt.Transaction_Date__c.format('dd/MM/yyyy') : null; 
    }

    public String getTotalAmount() {
        return formatDecimal(this.receipt.Receipt_Amount__c );
    }

    public String getTotalAmountWords() {
        if (this.receipt.Receipt_Amount__c != null) {
            String currencyCode = ApexPages.currentPage().getParameters().get('currencyCode');
            String words = VF_CurrencyToWordsConverter.convertAmountToWords(this.receipt.Receipt_Amount__c, currencyCode);
            System.debug(words);
            return words;
        }
        return null;
    }

    public String formatDecimal(Decimal value) {
        if (value == null) return '';
        value = value.setScale(2, RoundingMode.HALF_UP);
        return value.stripTrailingZeros().format();
    }

}