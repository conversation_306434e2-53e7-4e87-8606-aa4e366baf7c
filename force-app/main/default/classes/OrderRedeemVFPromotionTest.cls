@isTest
public with sharing class OrderRedeemVFPromotionTest {
    
    // Mock class for successful HTTP response
    private class MockHttpSuccessResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{"success":true,"data":{"sessionId":"12345"}}');
            return res;
        }
    }

    // Mock class for failed HTTP response
    private class MockHttpFailedResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(400);
            res.setBody('{"success":false,"message":"Error occurred"}');
            return res;
        }
    }

    @testSetup
    static void setupTestData() {
        Id dealerRecordType =  Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Dealer').getRecordTypeId();
        Account dealer = new Account(Name = 'Test Dealer', RecordTypeId = dealerRecordType,  VF_Level__c = 'Dealer Company', BillingStreet = 'street',BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert dealer;

        Account businessUnit = new Account(Name = 'Test Business Unit', ParentId = dealer.Id, RecordTypeId = dealerRecordType, BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert businessUnit;

        Account customer = new Account(LastName = 'Test Customer', Phone = '**********', VF_Business_Unit__c = dealer.Id, RecordTypeId = VF_Constants.B2C_ACCOUNT_RECORDTYPE,BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert customer;

        Pricebook2 standardPB = new Pricebook2(
            Id = Test.getStandardPricebookId(),
            IsActive = true
        );
        update standardPB;

        Product2 product = new Product2(Name = 'Test Vehicle Product', ProductCode = 'EC15_2023_GC15N', Family = 'MOD', IsActive = true);
        insert product;

        PricebookEntry pbe = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = product.Id, UnitPrice = 100, IsActive = true);
        insert pbe;

        // Option
        Product2 productOption = new Product2(Name = 'Test Options', ProductCode = 'EC15_2023_GC15N_CE11', IsActive = true);
        insert productOption;

        PricebookEntry pbeOption = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = productOption.Id, UnitPrice = 125, IsActive = true);
        insert pbeOption;

        Accessory_Kit__c kit = new Accessory_Kit__c(Kit_Name__c = 'AK11', Kit_Pricing__c = 17000, Active__c = true);
        insert kit;

        // Create test Order
        Order testOrder = new Order(
            AccountId = customer.Id,
            Status = 'New',
            EffectiveDate = Date.today(),
            Pricebook2Id = standardPB.Id,
            Accessory_Kit__c = kit.Id
        );
        insert testOrder;

        List<OrderItem> orderItems = new List<OrderItem>();
        orderItems.add(new OrderItem(
            OrderId = testOrder.Id,
            PricebookEntryId = pbe.Id,
            Quantity = 1,
            UnitPrice = 37000.0
        ));

        orderItems.add(new OrderItem(
            OrderId = testOrder.Id,
            PricebookEntryId = pbeOption.Id,
            Quantity = 1,
            UnitPrice = 300.0
        ));
        insert orderItems;


        // Create test VinFast Promotions
        VinFast_Promotion__c promotion = new VinFast_Promotion__c(
            VF_SO_No__c = testOrder.Id,
            VF_Promotion_Code__c = 'PROMO1',
            VF_Promotion_ID__c = 'P1',
            VF_Status__c = 'Potential'
        );
        insert promotion;
    }

    @isTest
    static void testRedeemPromotion_Success() {
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Prepare input
        Order testOrder = [SELECT Id FROM Order LIMIT 1];

        OrderRedeemVFPromotion.InputWrapper input = new OrderRedeemVFPromotion.InputWrapper();
        input.orderId = testOrder.Id;
        List<OrderRedeemVFPromotion.InputWrapper> inputs = new List<OrderRedeemVFPromotion.InputWrapper>{ input };

        // Call the method
        Test.startTest();
        List<OrderRedeemVFPromotion.OutputWrapper> results = OrderRedeemVFPromotion.redeemPromotion(inputs);
        Test.stopTest();

        // Assert results
        System.assertEquals(1, results.size(), 'There should be one result');
        System.assert(results[0].isValid, 'The result should be valid');
        System.assertEquals('Redeemed', [SELECT VF_Status__c FROM VinFast_Promotion__c LIMIT 1].VF_Status__c, 'Promotion status should be updated to Redeemed');
    }

    @isTest
    static void testRedeemPromotion_Failure() {
        Test.setMock(HttpCalloutMock.class, new MockHttpFailedResponse());

        // Prepare input
        Order testOrder = [SELECT Id FROM Order LIMIT 1];
        OrderRedeemVFPromotion.InputWrapper input = new OrderRedeemVFPromotion.InputWrapper();
        input.orderId = testOrder.Id;
        List<OrderRedeemVFPromotion.InputWrapper> inputs = new List<OrderRedeemVFPromotion.InputWrapper>{ input };

        // Call the method
        Test.startTest();
        List<OrderRedeemVFPromotion.OutputWrapper> results = OrderRedeemVFPromotion.redeemPromotion(inputs);
        Test.stopTest();

        // Assert results
        System.assertEquals(1, results.size(), 'There should be one result');
        System.assert(!results[0].isValid, 'The result should not be valid');
    }

    @isTest
    static void testRedeemPromotion_NoPromotions() {
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Prepare input for an order with no promotions
        Order testOrder = [SELECT Id FROM Order LIMIT 1];
        delete [SELECT Id FROM VinFast_Promotion__c WHERE VF_SO_No__c = :testOrder.Id];

        OrderRedeemVFPromotion.InputWrapper input = new OrderRedeemVFPromotion.InputWrapper();
        input.orderId = testOrder.Id;
        List<OrderRedeemVFPromotion.InputWrapper> inputs = new List<OrderRedeemVFPromotion.InputWrapper>{ input };

        // Call the method
        Test.startTest();
        List<OrderRedeemVFPromotion.OutputWrapper> results = OrderRedeemVFPromotion.redeemPromotion(inputs);
        Test.stopTest();

        // Assert results
        System.assertEquals(1, results.size(), 'There should be one result');
        System.assert(results[0].isValid, 'The result should be valid');
    }
}
