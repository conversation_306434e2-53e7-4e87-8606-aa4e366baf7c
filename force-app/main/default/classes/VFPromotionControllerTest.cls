@isTest
public with sharing class VFPromotionControllerTest {

    // Mock class for successful HTTP response
    private class MockHttpSuccessResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"success":true,"data":{"sessionId":"12345","promotions":[{"name":"Promo1","code":"PROMO1","termsApply":"Terms1","promotionId":"P1","discountChanged":100.0,"promotionStartDate":1681929600000,"promotionExpiryDate":1684512000000,"budgetApplied":500.0,"metadata":{"eligibleForVehicle":"YES","accessoryKitInfo":"Info1"}}]}}');
            return res;
        }
    }

    // Mock class for failed HTTP response
    private class MockHttpFailedResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(400);
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"success":false,"result":{"message":"Error occurred","status":"E","description":"Invalid request"}}');
            return res;
        }
    }

    @testSetup
    static void setupTestData() {
        Id dealerRecordType =  Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Dealer').getRecordTypeId();
        Account dealer = new Account(Name = 'Test Dealer', RecordTypeId = dealerRecordType,  VF_Level__c = 'Dealer Company', BillingStreet = 'street',BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert dealer;

        Account businessUnit = new Account(Name = 'Test Business Unit', ParentId = dealer.Id, RecordTypeId = dealerRecordType, BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert businessUnit;

        Account customer = new Account(LastName = 'Test Customer', Phone = '**********', VF_Business_Unit__c = dealer.Id, RecordTypeId = VF_Constants.B2C_ACCOUNT_RECORDTYPE,BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH', ShippingStreet = 'street', ShippingCity = 'Mumbai', ShippingCountry = 'India', ShippingCountryCode = 'IN', ShippingPostalCode = '400001', ShippingStateCode = 'MH');
        insert customer;

        Pricebook2 standardPB = new Pricebook2(
            Id = Test.getStandardPricebookId(),
            IsActive = true
        );
        update standardPB;

        Product2 product = new Product2(Name = 'Test Vehicle Product', ProductCode = 'EC15_2023_GC15N', Family = 'MOD', IsActive = true);
        insert product;

        PricebookEntry pbe = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = product.Id, UnitPrice = 100, IsActive = true);
        insert pbe;

        // Option
        Product2 productOption = new Product2(Name = 'Test Options', ProductCode = 'EC15_2023_GC15N_CE11', IsActive = true);
        insert productOption;

        PricebookEntry pbeOption = new PricebookEntry(Pricebook2Id = standardPB.Id, Product2Id = productOption.Id, UnitPrice = 125, IsActive = true);
        insert pbeOption;

        Accessory_Kit__c kit = new Accessory_Kit__c(Kit_Name__c = 'AK11', Kit_Pricing__c = 17000, Active__c = true);
        insert kit;

        Contact con = [SELECT ID FROM Contact WHERE AccountId = :customer.Id  LIMIT 1];
        Quote testQuote = new Quote(
            Name = 'Test Quote',
            QuoteAccountId = customer.Id,
            ContactId = con.Id,
            Pricebook2Id = standardPB.Id,
            Grand_Total_Without_Additional_Fees__c = 12345.67,
            ExpirationDate = Date.today(),
            Accessory_Kit__c = kit.Id,
            BillingStreet = 'street', BillingCity = 'Mumbai', BillingCountry = 'India', BillingCountryCode = 'IN', BillingPostalCode = '400001', BillingStateCode = 'MH'
        );
        insert testQuote;

        List<QuoteLineItem> quoteLineItems = new List<QuoteLineItem>();
        quoteLineItems.add(new QuoteLineItem(
            QuoteId = testQuote.Id,
            PricebookEntryId = pbe.Id,
            Quantity = 1,
            UnitPrice = 35000.0
        ));

        quoteLineItems.add(new QuoteLineItem(
            QuoteId = testQuote.Id,
            PricebookEntryId = pbeOption.Id,
            Quantity = 1,
            UnitPrice = 200.0
        ));

        // Insert the QuoteLineItem records
        insert quoteLineItems;

        // Create test Order
        Order testOrder = new Order(
            AccountId = customer.Id,
            Status = 'New',
            EffectiveDate = Date.today(),
            Pricebook2Id = standardPB.Id,
            Accessory_Kit__c = kit.Id
        );
        insert testOrder;

        List<OrderItem> orderItems = new List<OrderItem>();
        orderItems.add(new OrderItem(
            OrderId = testOrder.Id,
            PricebookEntryId = pbe.Id,
            Quantity = 1,
            UnitPrice = 37000.0
        ));

        orderItems.add(new OrderItem(
            OrderId = testOrder.Id,
            PricebookEntryId = pbeOption.Id,
            Quantity = 1,
            UnitPrice = 300.0
        ));
        insert orderItems;
    }

    @isTest
    static void testQuoteGetVinFastPromotions_Success() {
        Test.startTest();

        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Fetch promotions for Quote
        Quote testQuote = [SELECT Id FROM Quote LIMIT 1];
        List<VFPromotionController.PromotionWrapper> promotions = VFPromotionController.getVinFastPromotions(testQuote.Id, 'Quote');

        System.assertNotEquals(null, promotions, 'Promotions should not be null');
        System.assertEquals(1, promotions.size(), 'There should be one promotion');
        System.assertEquals('Promo1', promotions[0].name, 'Promotion name should match');

        Test.stopTest();
    }

    @isTest static void testQuoteGetVinFastPromotions_Fail() {
        Test.startTest();
        
        Test.setMock(HttpCalloutMock.class, new MockHttpFailedResponse());
        
        try {
            Quote testQuote = [SELECT Id FROM Quote LIMIT 1];
            List<VFPromotionController.PromotionWrapper> promotions = VFPromotionController.getVinFastPromotions(testQuote.Id, 'Quote');
        } catch (Exception ex) {
            System.assertEquals('Script-thrown exception', ex.getMessage(), 'Error message should match the failure scenario');
        }
        
        Test.stopTest();
    }

    @isTest
    static void testOrderGetVinFastPromotions_Success() {
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Fetch promotions for Order
        Order testOrder = [SELECT Id FROM Order LIMIT 1];
        List<VFPromotionController.PromotionWrapper> promotions = VFPromotionController.getVinFastPromotions(testOrder.Id, 'Order');
        System.assertNotEquals(null, promotions, 'Promotions should not be null');
        Test.stopTest();
    }

    @isTest
    static void testRedeemVinFastPromotions_Success() {
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Redeem promotions for Order
        Order testOrder = [SELECT Id FROM Order LIMIT 1];
        String sessionId = VFPromotionController.redeemVinFastPromotions(testOrder.Id);

        System.assertNotEquals(null, sessionId, 'Session ID should not be null');
        System.assertEquals('12345', sessionId, 'Session ID should match');
        Test.stopTest();
    }

    @isTest
    static void testRedeemVinFastPromotions_Failure() {
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new MockHttpFailedResponse());

        // Redeem promotions for Order
        Order testOrder = [SELECT Id FROM Order LIMIT 1];
        try {
            VFPromotionController.redeemVinFastPromotions(testOrder.Id);
        } catch (AuraHandledException ex) {
            System.assertEquals('Script-thrown exception', ex.getMessage(), 'Error message should match the failure scenario');
        }
        Test.stopTest();
    }

    @isTest
    static void testUpsertData() {
        // Prepare test data
        VFPromotionController.PromotionWrapper promotion = new VFPromotionController.PromotionWrapper();
        promotion.name = 'Promo2';
        promotion.code = 'P003';
        promotion.termsApply = 'Terms 3';
        promotion.promotionId = '11223';
        promotion.discountChanged = 150;
        promotion.promotionStartDate = 1682604000000L;
        promotion.promotionExpiryDate = 1683112400000L;
        promotion.metadata = new VFPromotionController.cls_metadata();
        promotion.metadata.eligibleForVehicle = 'YES';

        // Create mock selectedPromotion list
        List<VFPromotionController.PromotionWrapper> selectedPromotion = new List<VFPromotionController.PromotionWrapper>{ promotion };

        // Mock upsert call

        // Call upsertData
        Quote testQuote = [SELECT Id FROM Quote LIMIT 1];
        Test.startTest();
        VFPromotionController.upsertData('Quote', testQuote.Id, selectedPromotion);
        Test.stopTest();

        // Verify inserted records
        List<VinFast_Promotion__c> insertedPromotions = [SELECT VF_Promotion_Name__c, VF_Promotion_Code__c FROM VinFast_Promotion__c];
        System.assertEquals(1, insertedPromotions.size(), 'One promotion should have been inserted');
        System.assertEquals('Promo2', insertedPromotions[0].VF_Promotion_Name__c, 'Promotion name should match');
    }
}
