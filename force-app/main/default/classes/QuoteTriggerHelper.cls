/**********************************************************************
Name: QuoteTriggerHelper
=======================================================================
Purpose:                                                            
The class holds all the business logic for Case Trigger Handler class.
=======================================================================
History                                                            
-------                                                            
VERSION     AUTHOR              DATE                 DETAIL
1.0         thanhnm             18/04/2025           Initial Development
***********************************************************************/
public with sharing class QuoteTriggerHelper {

    public static void convertAmountToWords(Map<ID, Quote> mapOfIdQuoteNew, Map<ID, Quote> mapOfIdQuoteOld) {
        for(Quote rec : mapOfIdQuoteNew.values()) {
            if (rec.Total_With_Additional_Fees__c > 0) {
                rec.QT_Grand_Total_Text__c = VF_CurrencyToWordsIndian.convert(rec.Total_With_Additional_Fees__c);
            }
        }
    }

}