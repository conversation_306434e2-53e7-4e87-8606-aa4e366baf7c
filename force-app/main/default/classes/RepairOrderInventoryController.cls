/*****Class Description*******************
* Name           : RepairOrderInventoryController
* Test Class     : RepairOrderInventoryControllerTest
* Purpose        : Handle Goods Issue/Receive to SAP
* Modification History  :
* Version  Author      DateModified     Description
* 1.0      NinhNN      11/04/2025       Init
* *******************************************/
public with sharing class RepairOrderInventoryController{
    @InvocableMethod(label='Inventory GI/GR' description='Handle Goods Issue/Receive to SAP' )
    public static List<String> Process_GIGR(List<List<Inventory_Transaction__c>> lst_records) {
        String SAPDoc = '';
        List<String> results = new List<String>();
        Set<Id> set_pc = new Set<Id>();
        List<Inventory_Transaction__c> records = lst_records[0];
        For (Inventory_Transaction__c it : records){
            set_pc.add(it.Product_Consumed__c);
        }
        Map<String, ProductConsumed> map_pc = new Map<String, ProductConsumed>();
        List<ProductConsumed> lst_pc = [SELECT id, SAP_Reservation_ID__c, SAP_Item_ID__c, QuantityUnitOfMeasure, PricebookEntry.Product2.ProductCode, 
                                        WorkOrderId, WorkOrder.Account.VF_DealerCode__c
                                        FROM ProductConsumed 
                                        WHERE id IN :set_pc];
        String Transaction_Type = records[0].Type__c == 'In' ? '2' : '1'; 
        String Dealer_Code = lst_pc[0].WorkOrder.Account.VF_DealerCode__c;
        String TransId = lst_pc[0].WorkOrderId + '_' + datetime.now().format('yyyyMMddhhmmss');
        For (ProductConsumed pc : lst_pc){
            map_pc.put(pc.id, pc);
        }
        
        String requestBody = buildPayload(Transaction_Type, Dealer_Code, TransId, records, map_pc); 
        System.debug(requestBody);
        
        // Define the endpoint and headers using NameCredential
        String url = 'callout:MuleSoft_Credential/dms/api/v1/inventory-adjustment';
        String method = 'POST';
        String zbody;
        Map<String, String> headers = new Map<String, String>{
            'Content-Type' => 'application/json'
                };
                    try {
                        HttpResponse response = VF_MakeCalloutGeneric.makeCallout(url, method, headers, requestBody);
                        System.debug(response.getBody());
                        SAPResponseWrapper responseMS = (SAPResponseWrapper)JSON.deserialize(response.getbody(), SAPResponseWrapper.class);
                        System.debug(JSON.serialize(responseMS));
                        VF_Utility.createOutboundLog(url, method, requestBody, response, null);
                        zbody = response.getBody();
                        if (response.getStatusCode() == VF_Constants.SUCCESS_CODE) {                           
                            system.debug(responseMS.data);    
                            if (responseMS.result.status == 'S'){
                                if (responseMS.data.status == 'S'){                                		    
                                    For(Inventory_Transaction__c rc : records){
                                        rc.SAP_Doc__c = responseMS.data.SAPDoc;
                                    }
                                    SAPDoc = responseMS.data.SAPDoc;
                                    Database.SaveResult[] srList = Database.insert(records, true);
                                    results.add('Status: Success - '+responseMS.data.message);
                                }else{
                                    results.add('Status: Error - '+responseMS.data.message);
                                }                                
                            }else{
                                results.add('Status: Error - '+responseMS.result.message);
                            }           
                        } else {
                            results.add('Status: Error - '+zbody);
                        }
                        system.debug(results);
                        return results;                                         
                    } catch (Exception ex) {
                        results.add('Status: Error - '+ex.getMessage());
                        SYS_Exception__c se = new SYS_Exception__c();
                        se.Type__c = 'Flow-Inventory Transaction';
                        se.Exception_Message__c = ex.getMessage();
                        se.Developer_Message__c = JSON.serialize(records);
                        se.Description__c = 'SAP Doc: ' + SAPdoc;
                        se.Need_FollowUp__c = TRUE;
                        Insert se;
                        system.debug(results);
                        return results;
                    }
    }
    //private static String buildPayload(String type, String DealerCode, String TransId, Map<String, Inventory_Transaction__c> map_it, Map<String, ProductConsumed> map_pc) {
    private static String buildPayload(String type, String DealerCode, String TransId, List<Inventory_Transaction__c> lst_it, Map<String, ProductConsumed> map_pc) {
        String payload = '';             
        Map<String, Object> params = new Map<String,Object>();
        // build header
        params.put('dealer', DealerCode);
        params.put('sfTransactionID', TransId);
        params.put('market', 'IN'); 
        params.put('transactionDate', datetime.now().format('dd-MM-yyyy'));       
        params.put('transactionType', '1');
        params.put('goodMovementType', type);
        
        // build items
        List<Map<String, Object>> items = new List<Map<String, Object>>();
        // build itm for items                 
        for (Inventory_Transaction__c it : lst_it) {
            Map<String, Object> itm = new Map<String, Object>(); 
            ProductConsumed t_pc = map_pc.get(it.Product_Consumed__c);
            itm.put('reservationID', t_pc.SAP_Reservation_ID__c);
            itm.put('itemID', t_pc.SAP_Item_ID__c);
            itm.put('partCode', t_pc.PricebookEntry.Product2.ProductCode);
            itm.put('quantity', it.Quantity__c);
            itm.put('uom', t_pc.QuantityUnitOfMeasure); 
            items.add(itm);
        }
        params.put('partRequest', items);
        payload = JSON.serialize(params);    
        return payload;
    }
    
    public class SAPResponseWrapper {
        @AuraEnabled public cls_result result {get; set;}
        @AuraEnabled public cls_data data {get; set;}
    }
    public class cls_result {
        @AuraEnabled public String code;
        @AuraEnabled public String message;
        @AuraEnabled public String status;
        @AuraEnabled public String description;
    }
    public class cls_data {
        @AuraEnabled public String status;
        @AuraEnabled public String message;
        @AuraEnabled public String SAPDoc;
    }
}