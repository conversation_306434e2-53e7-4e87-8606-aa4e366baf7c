/*****Class Description*******************
* Name           : RepairOrderTaxesControllerTest
* Test Class     : 
* Purpose        : Get taxes, prices for an Work order from SAP
* Modification History  :
* Version  Author      DateModified     Description
* 1.0      NinhNN      17/04/2025       Init
* *******************************************/
@isTest
private class RepairOrderTaxesControllerTest {
    // Step 1: Create Mock Response
    class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HTTPResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setStatusCode(200);
            String t_res = '{"result":{"status":"S","message":null,"code":"S000"},"data":{"transactionType":2,"transactionID":"0WOC1000003F7M1OAK","items":[{"transactionItemId":"000001","quantity":0.000,"productCode":"000000000030000047","priceObject":[{"unitPerPrice":0,"priceValue":62500.00,"priceType":"ZSP2","priceAmount":100.00},{"unitPerPrice":0,"priceValue":1562.50,"priceType":"JOSG","priceAmount":2.50},{"unitPerPrice":0,"priceValue":1562.50,"priceType":"JOCG","priceAmount":2.50},{"unitPerPrice":0,"priceValue":65625.00,"priceType":"JTCB","priceAmount":100.00}]},{"transactionItemId":"100057","quantity":0.000,"productCode":"BEX70001111PA","priceObject":[{"unitPerPrice":0,"priceValue":1661.22,"priceType":"ZSP2","priceAmount":100.00},{"unitPerPrice":0,"priceValue":41.53,"priceType":"JOSG","priceAmount":2.50},{"unitPerPrice":0,"priceValue":41.53,"priceType":"JOCG","priceAmount":2.50},{"unitPerPrice":0,"priceValue":1744.28,"priceType":"JTCB","priceAmount":100.00}]},{"transactionItemId":"PC-034","quantity":0.000,"productCode":"BEX71002008","priceObject":[{"unitPerPrice":0,"priceValue":990.00,"priceType":"ZSP2","priceAmount":100.00},{"unitPerPrice":0,"priceValue":24.75,"priceType":"JOSG","priceAmount":2.50},{"unitPerPrice":0,"priceValue":24.75,"priceType":"JOCG","priceAmount":2.50},{"unitPerPrice":0,"priceValue":1039.50,"priceType":"JTCB","priceAmount":100.00}]}]}}';
            String mockResponse = t_res;            
            res.setBody(mockResponse);
            return res;
        }
    }
    
    @isTest
    static void testGetROTaxesFromSAP() {
        // Step 2: Insert required data
        Account acc = new Account(Name='Test Account', BillingStateCode='DL', BillingCountryCode='IN', VF_DealerCode__c='D123');
        insert acc;
        
        Contact con = new Contact(LastName='Tester', AccountId=acc.Id);
        insert con;
                
        Pricebook2 pb = new Pricebook2();
        pb.Id = Test.getStandardPricebookId();
                       
        WorkOrder wo = new WorkOrder(
            Subject = 'Test WO',
            Status = 'New',
            ContactId = con.Id,
            AccountId = acc.Id,
            Pricebook2Id = pb.Id
        );
        insert wo;
        
        Concern__c cc = new Concern__c();
        cc.Work_Order__c = wo.id;
        cc.Bill_classification__c = 'C';
        Insert cc;
        
        WorkType wt = new WorkType(Name='Test WorkType', EstimatedDuration=1, External_Id__c='EXT123', DurationType='Hours');
        insert wt;
        
        WorkOrderLineItem woli = new WorkOrderLineItem(
            WorkOrderId = wo.Id,
            WorkTypeId = wt.Id,
            Classification__c = 'C',
            Concern__c = cc.Id
        );
        insert woli;
        
        Product2 prod = new Product2(Name='Test Product', ProductCode='P123', QuantityUnitOfMeasure='EA', IsActive=true);
        insert prod;
        
        PricebookEntry pbe = new PricebookEntry();
        pbe.Product2Id = prod.id;
        pbe.Pricebook2Id = pb.id;
        pbe.IsActive = true;
        pbe.UnitPrice = 50;
       	Insert pbe;
        
        ProductConsumed pc = new ProductConsumed(
            WorkOrderId = wo.Id,
			PricebookentryId = pbe.id,            
            Request_Quantity__c = 1,
            Classification__c = 'C',
            Concern__c = cc.Id,
            QuantityConsumed = 1
        );
        insert pc;
        // Step 4: Set mock and call method
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        
        Test.startTest();
        List<String> result = RepairOrderTaxesController.getROTaxesFromSAP(new List<String>{wo.Id});
        Test.stopTest();
    }
}