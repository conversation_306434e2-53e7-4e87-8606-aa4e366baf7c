/*****Class Description*******************
* Name                  : VF_Constants
* Purpose               : Utility class to hold constant values for other business logic classes
* Modification History	: NA
* Version		Author			DateModified	Description
* 1.0		    thanhnm		    29/03/2025		Init
* *******************************************/
public with sharing class VF_Constants {

    public static final Integer SUCCESS_CODE = 200;
    public static final Integer CLIENT_ERROR_CODE = 400;
    public static final Integer NOTFOUND_ERROR_CODE = 404;
    public static final Integer SERVER_ERROR_CODE = 500;

    // mapping Product record type to SAP product type for get prices and taxes
    public static final Map<String, String> SAP_TAX_PRODUCT_TYPE = new Map<String, String> {
        'Vehicle' => '20',
        'Part' => '50'
    };

    // record type
    public static final Id BOOKING_ORDER_RECORDTYPE = Schema.SObjectType.Order.getRecordTypeInfosByDeveloperName().get('Booking').getRecordTypeId();

    public static final Id B2C_ACCOUNT_RECORDTYPE = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId();

}