/*****Class Description*******************
* Name           : OrderRedeemVFPromotion
* Test Class     : OrderRedeemVFPromotionTest
* Purpose        : Order redeem VinFast promotions from Loyalty
* Modification History  : NA
* Version  Author      DateModified     Description
* 1.0      thanhnm     25/04/2025       Init
* *******************************************/

public with sharing class OrderRedeemVFPromotion {

    public class InputWrapper {
        @InvocableVariable(required=true) public String orderId;
    }

    public class OutputWrapper {
        @InvocableVariable public Boolean isValid;
        @InvocableVariable public String message;
    }

    @InvocableMethod(label='Redeem VF Promotion for Order')
    public static List<OutputWrapper> redeemPromotion(List<InputWrapper> inputs) {
        List<OutputWrapper> results = new List<OutputWrapper>();

        for (InputWrapper input : inputs) {
            OutputWrapper output = new OutputWrapper();
            output.isValid = true;

            VFPromotionController.MS_Response msResponse = VFPromotionController.redeemPromotionsByOrder(input.orderId);

            if(!msResponse.success || msResponse.data == null || String.isBlank(msResponse.data.sessionId)) {
                output.isValid = false;
                output.message = msResponse.message;
                results.add(output);
                break;
            }

            updatePromotions(input.orderId, msResponse.data.sessionId);
            
            results.add(output);
        }

        return results;
    }

    // Helper method to update promotions for a given order ID
    private static void updatePromotions(String orderId, String sessionId) {
        List<VinFast_Promotion__c> promotionsToUpdate = new List<VinFast_Promotion__c>();

        for (VinFast_Promotion__c promotion : [
            SELECT Id FROM VinFast_Promotion__c
            WHERE VF_SO_No__c = :orderId
        ]) {
            promotion.VF_Status__c = 'Redeemed';
            promotion.VF_Redeemed_Date__c = Date.today();
            promotion.VF_Session_Id__c = sessionId;

            promotionsToUpdate.add(promotion);
        }

        if (!promotionsToUpdate.isEmpty()) {
            update promotionsToUpdate;
        }
    }
}