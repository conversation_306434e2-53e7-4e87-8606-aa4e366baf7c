@isTest
private class VF_MakeCalloutGenericTest {
    // Mock class for HTTP Success response
    private class MockHttpSuccessResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();
            res.setStatusCode(200);
            res.setBody('{"success":true}');

            return res;
        }
    }

    // Mock class for HTTP Fail response
    private class MockHttpFailedResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();

            res.setStatusCode(400);
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"success": false}');

            return res;
        }
    }

    // Mock class for HTTP Exception response
    private class MockHttpUnauthorizedResponse implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            CalloutException e = (CalloutException)CalloutException.class.newInstance();
            e.setMessage('Unauthorized endpoint, please check Setup->Security->Remote site settings.');
            throw e;
        }
    }

    @isTest
    static void testMakeCallout() {
        Test.startTest();
        // Set up mock callout
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Define test inputs
        String testUrl = 'https://example.com/api';
        Map<String, String> testHeaders = new Map<String, String>{ 'Content-Type' => 'application/json' };
        String testBody = '{"key":"value"}';

        // Call the method
        HttpResponse response = VF_MakeCalloutGeneric.makeCallout(testUrl, 'POST', testHeaders, testBody);

        // Assert the response
        System.assertEquals(200, response.getStatusCode());
        System.assertEquals('{"success":true}', response.getBody());
        Test.stopTest();
    }

    @isTest
    static void testMakeCalloutWithEmptyHeaders() {
        Test.startTest();
        // Set up mock callout
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Define test inputs
        String testUrl = 'https://example.com/api';
        Map<String, String> testHeaders = new Map<String, String>(); // Empty headers
        String testBody = null; // No body for GET request

        // Call the method
        HttpResponse response = VF_MakeCalloutGeneric.makeCallout(testUrl, 'GET', testHeaders, testBody);

        // Assert the response
        System.assertEquals(200, response.getStatusCode());
        System.assertEquals('{"success":true}', response.getBody());
        Test.stopTest();
    }

    @isTest
    static void testMakeCalloutWithNullInputs() {
        Test.startTest();
        // Set up mock callout
        Test.setMock(HttpCalloutMock.class, new MockHttpSuccessResponse());

        // Call the method with null inputs
        HttpResponse response = VF_MakeCalloutGeneric.makeCallout(null, null, null, null);

        // Assert the response
        System.assertEquals(200, response.getStatusCode());
        System.assertEquals('{"success":true}', response.getBody());
        Test.stopTest();
    }

    @IsTest
    static void testMakeCalloutFailed() {
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new MockHttpFailedResponse());
        // Define test inputs
        String testUrl = 'https://example.com/api';
        Map<String, String> testHeaders = new Map<String, String>{ 'Content-Type' => 'application/json' };
        String testBody = '{"key":"value"}';

        // Call the method
        HttpResponse response = VF_MakeCalloutGeneric.makeCallout(testUrl, 'POST', testHeaders, testBody);
        Test.stopTest();
    }

    @IsTest
    static void testMakeCalloutException() {
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new MockHttpUnauthorizedResponse());
        try {
            // Call the method with null inputs
            HttpResponse response = VF_MakeCalloutGeneric.makeCallout(null, null, null, null);
        } catch (Exception e) {
            System.debug('Exception: ' + e.getMessage() + ' - Line: ' + e.getLineNumber());
        }
        
        Test.stopTest();
    }
}


