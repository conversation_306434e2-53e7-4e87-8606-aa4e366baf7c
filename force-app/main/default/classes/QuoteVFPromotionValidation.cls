/*****Class Description*******************
* Name           : QuoteVFPromotionValidation
* Test Class     : QuoteVFPromotionValidationTest
* Purpose        : Quote validate VinFast promotions from Loyalty
* Modification History  : NA
* Version  Author      DateModified     Description
* 1.0      thanhnm     23/04/2025       Init
* *******************************************/

public with sharing class QuoteVFPromotionValidation {

    public class InputWrapper {
        @InvocableVariable(required=true) public String quoteId;
    }

    public class OutputWrapper {
        @InvocableVariable public Boolean isValid;
    }

    @InvocableMethod(label='Check VF Promotion for Quote')
    public static List<OutputWrapper> checkPromotion(List<InputWrapper> inputs) {
        List<OutputWrapper> results = new List<OutputWrapper>();

        for (InputWrapper input : inputs) {
            OutputWrapper output = new OutputWrapper();
            output.isValid = true;
            VFPromotionController.MS_Response msResponse = VFPromotionController.getVinFastPromotionsByQuote(input.quoteId);

            if(!msResponse.success || msResponse.data == null || msResponse.data.promotions.isEmpty()) {
                output.isValid = false;
                results.add(output);
                break;
            }

            // get all VF promotions for this Quote
            List<VinFast_Promotion__c> quotePromotions = [
                SELECT Id,VF_Promotion_Code__c,VF_Promotion_ID__c
                FROM VinFast_Promotion__c
                WHERE VF_SQ_No__c = :input.quoteId
            ];

            Set<String> loyaltyCampaign = new Set<String>();
            for (VFPromotionController.PromotionWrapper pw : msResponse.data.promotions) {
                String loyalty_key = String.format('{0}_{1}', new List<String>{ pw.promotionId, pw.code });
                loyaltyCampaign.add(loyalty_key);
            }

            // compare Loyalty with the existing VinFast promotions
            for (VinFast_Promotion__c item : quotePromotions) {       
                String sf_key = String.format('{0}_{1}', new List<String>{ item.VF_Promotion_ID__c, item.VF_Promotion_Code__c });

                if (!loyaltyCampaign.contains(sf_key)) {
                    output.isValid = false;
                    break;
                }
            }
            
            results.add(output);
        }

        return results;
    }
        
}