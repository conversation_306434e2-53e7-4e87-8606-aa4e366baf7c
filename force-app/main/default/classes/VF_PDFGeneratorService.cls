/*****Class Description*******************
* Name           : VF_PDFGeneratorService
* Test Class     : VF_PDFGeneratorServiceTest
* Purpose        : Proivide a way to generate PDF from Apex
* Version  Author      DateModified     Description
* 1.0      thangnv12    11/04/2025       Init
* *******************************************/
public class VF_PDFGeneratorService {
    private static final String DEFAULT_SHARE_TYPE = 'V';
    private static final String DEFAULT_SHARE_VISIBILITY = 'AllUsers';
    @InvocableMethod(label='Generate PDF File' description='Generate a PDF file and save it to the database, linked to the record.')
    public static List<Output> convert(List<Input> inputs) {
        List<Output> results = new List<Output>();
        for (Input input : inputs) {
            String contentDocumentId = generateAndSavePDF(input.recordId, input.pdfFileName, input.pageUrl, input.shareType, input.visibility);
            results.add(new Output(contentDocumentId));
        }
        return results;
    }

    /**
     * Generate PDF from Visualforce page
     * @param recordId Record Id
     * @param pdfFileName Name of the PDF file
     * @param pageUrl url to the Visualforce page (e.g. /apex/MyVisualforcePage?id=001XXX&param1=ABCDE&param2=123)
     * @param shareType ('V' = Viewer, 'C' = Collaborator, 'I' = Inferred) 
     * @param visibility ('AllUsers', 'InternalUsers', 'SharedUsers')
     * @return ContentDocumentId
     */
    public static String generateAndSavePDF(String recordId, String pdfFileName, String pageUrl, String shareType, String visibility) {
        PageReference pageRef = new PageReference(pageUrl);
        if (pageRef == null) {
            throw new VF_PDFGeneratorException('The Visualforce page could not be found at this URL: ' + pageUrl);
        }
        Blob pdfBlob = Test.isRunningTest() ? Blob.valueOf('Test') : pageRef.getContentAsPDF();
        if (!pdfFileName.toLowerCase().endsWith('.pdf')) {
            pdfFileName += '.pdf';
        }

        // Save as ContentVersion (File)
        ContentVersion cv = new ContentVersion();
        cv.Title = pdfFileName;
        cv.PathOnClient = '/' + pdfFileName;
        cv.VersionData = pdfBlob;
        insert cv;

        // Link the file to the record
        ContentDocumentLink cdl = new ContentDocumentLink();
        cdl.ContentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id LIMIT 1].ContentDocumentId;
        cdl.LinkedEntityId = recordId;
        cdl.ShareType = shareType != null ? shareType : DEFAULT_SHARE_TYPE;
        cdl.Visibility = visibility != null ? visibility : DEFAULT_SHARE_VISIBILITY;
        insert cdl;

        return cdl.ContentDocumentId;
    }

    public class Input {
        @InvocableVariable(
            required=true  
            label='Record Id'  
            description='The Id of the record to be passed to the Visualforce page. Example: "001XXXXXXXXXXXXXXX".'
        )
        public String recordId;
    
        @InvocableVariable(
            required=true 
            label='PDF File Name' 
            description='The name of the PDF file to be generated (with or without extension ".pdf"). Example: "Invoice" or "Invoice.pdf".'
        )
        public String pdfFileName;
    
        @InvocableVariable(
            required=true 
            label='Page URL' 
            description='The Visualforce page URL to render as PDF. Include query parameters as needed. Example: "/apex/MyVisualforcePage?id=001XXX&param1=ABCDE&param2=123".'
        )
        public String pageUrl;
    
        @InvocableVariable(
            label='Share Type' 
            defaultValue='V' 
            description='(Optional) Sharing type for the file. Options: V = Viewer, C = Collaborator, I = Inferred.'
        )
        public String shareType;
    
        @InvocableVariable(
            label='Visibility' 
            defaultValue='AllUsers' 
            description='(Optional) Who can see the uploaded file. Options: AllUsers, InternalUsers, SharedUsers.'
        )
        public String visibility;
    }

    public class Output {
        @InvocableVariable(label='Content Document Id')
        public String contentDocumentId;
        public Output(String contentDocumentId) {
            this.contentDocumentId = contentDocumentId;
        }
    }

    public class VF_PDFGeneratorException extends Exception{}
}