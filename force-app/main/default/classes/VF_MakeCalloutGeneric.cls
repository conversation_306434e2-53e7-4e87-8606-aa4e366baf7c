/*****Class Description*******************
* Name           : VF_MakeCalloutGeneric
* Test Class     : VF_MakeCalloutGenericTest
* Date Created   : 30-Nov-2021
* Purpose        : This class handles the general callout logic for all APIs
* Author         : (TCS)
* Modification History  : NA
* Version  Author      DateModified  Description
* 1.0      TCS         30-Nov-2021
* *******************************************/

public class VF_MakeCalloutGeneric {
    
    // This method takes URL, HTTP method, request headers, and request body as input parameters and makes a callout.
    public static HttpResponse makeCallout(String url, String httpMethod, Map<String, String> mapHttpHeader, String requestJSONBody) {        
        Http http = new Http();
        HttpRequest request = new HttpRequest();
        HttpResponse response;
        Integer timeOut = 120000; // 2 minutes

        try {
            // Set endpoint URL if input parameter is not blank.
            if (String.isNotBlank(url)) {
                request.setEndpoint(url);
            }

            // Set HTTP method if input parameter is not blank.
            if (String.isNotBlank(httpMethod)) {
                request.setMethod(httpMethod);
            }

            // Set HTTP request headers if the map is not null or empty.
            if (mapHttpHeader != null && !mapHttpHeader.isEmpty()) {
                for (String mapHttpHeaderKey : mapHttpHeader.keySet()) {                   
                    request.setHeader(mapHttpHeaderKey, mapHttpHeader.get(mapHttpHeaderKey));
                }
            }

            // Set timeout for the request.
            request.setTimeout(timeOut);

            // Set request body if input parameter is not blank.
            if (String.isNotBlank(requestJSONBody)) {
                request.setBody(requestJSONBody);
            }

            // Make HTTP callout.
            response = http.send(request);  
        } catch (Exception ex) {
            // System.debug('Error during HTTP callout:'+ ex.getMessage());
            throw ex;
        }
        
        return response;
    }     
}