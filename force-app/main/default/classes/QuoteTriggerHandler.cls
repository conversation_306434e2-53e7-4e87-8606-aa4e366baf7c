/**********************************************************************
Name:  QuoteTriggerHandler
=======================================================================
Purpose:                                                            
This is a single handler class for all Quote Triggers.
All the business logic is written in helper class.
=======================================================================
History                                                            
-------                                                            
VERSION     AUTHOR              DATE            DETAIL
1.0         thanhnm            18/04/2025      Initial Development
*************************************************************************/

public class QuoteTriggerHandler implements VF_ITriggerHandler {
    
    public static Boolean isTriggerDisabled = true;
    
    /*******************************************************************
    Purpose:  Checks to see if the trigger has been disabled in Custom Setting
    Parameters: List<SObject>
    Returns: void
    Throws : None
    ********************************************************************/
    public Boolean isDisabled() {
        SYS_GlobalOnOffSwitch__c triggerGlobalSwitchObj = SYS_GlobalOnOffSwitch__c.getInstance();
        if((null != triggerGlobalSwitchObj) && (triggerGlobalSwitchObj.SYS_IsTriggerActive__c == true)) {
            isTriggerDisabled=false;
        }
        return isTriggerDisabled;
    }
    
    /*******************************************************************
    Purpose:  This method is called before new records are inserted.
    Parameters: List<SObject> listOfSObjectNew
    Returns: void
    Throws : None
    ********************************************************************/   
    public void beforeInsert(List<SObject> listOfSObjectNew) {
    }
    
    /*******************************************************************
    Purpose:  This method is called before records are updated.
    Parameters: 1. Map<Id, sObject> mapOfIdSObjectNew
                2. Map<Id, sObject> mapOfIdSObjectOld
    Returns: void
    Throws : None
    ********************************************************************/  
    public void beforeUpdate(Map<Id, sObject> mapOfIdSObjectNew, Map<Id, sObject> mapOfIdSObjectOld) {
        Map<Id, Quote> mapOfIdQuoteNew = (Map<Id, Quote>) mapOfIdSObjectNew;
        Map<Id, Quote> mapOfIdQuoteOld = (Map<Id, Quote>) mapOfIdSObjectOld; 
        
        // Validate Case details based on the category selected.
        QuoteTriggerHelper.convertAmountToWords(mapOfIdQuoteNew, mapOfIdQuoteOld);

    }
     
    /*******************************************************************
    Purpose:  This method is called before records are deleted.
    Parameters: Map<Id, SObject> mapOfIdSObjectOld
    Returns: void
    Throws : None
    ********************************************************************/ 
    public void beforeDelete(Map<Id, SObject> mapOfIdSObjectOld) {
        // write business logic to be implemented in before delete operation
    }
    
    /*******************************************************************
    Purpose:  This method is called after new records are inserted.
    Parameters: Map<Id, SObject> mapOfIdSurveysNew
    Returns: void
    Throws : None
    ********************************************************************/   
    public void afterInsert(Map<Id, SObject> mapOfIdSObjectNew) {
       // write business logic to be implemented in after insert operation
    }
    
    /*******************************************************************
    Purpose:  This method is called after records are updated.
    Parameters: 1. Map<Id, sObject> mapOfIdSObjectNew
                2. Map<Id, sObject> mapOfIdSObjectOld
    Returns: void
    Throws : None
    ********************************************************************/     
    public void afterUpdate(Map<Id, sObject> mapOfIdSObjectNew, Map<Id, sObject> mapOfIdSObjectOld) {
        // write business logic to be implemented in after update operation
    }
    
    /*******************************************************************
    Purpose:  This method is called after records are deleted.
    Parameters: Map<Id, SObject> mapOfIdSObjectOld
    Returns: void
    Throws : None
    ********************************************************************/ 
    public void afterDelete(Map<Id, SObject> mapOfIdSObjectOld) {
        // write business logic to be implemented in after delete operation
    }
    
    /*******************************************************************
    Purpose:  This method is called after records are un-deleted.
    Parameters: Map<Id, SObject> mapOfIdSObjectOld
    Returns: void
    Throws : None
    ********************************************************************/ 
    public void afterUndelete(Map<Id, SObject> mapOfIdSObjectOld) {
        // write business logic to be implemented in after undelete operation
    }

}