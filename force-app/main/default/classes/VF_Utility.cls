/*****Class Description*******************
* Name           : VF_Utility
* Test Class     : 
* Purpose        : Contains common reusable utility methods
* Modification History  : NA
* Version  Author      DateModified     Description
* 1.0      thanhnm     31/03/2025       Init
* *******************************************/
public with sharing class VF_Utility {

    /**
     * Create Outbound Log
     * @description Create Outbound Log (insert to DB)
     * @param url
     * @param httpMethod
     * @param requestBody
     * @param response
     * @param errorMessage
     * @return Outbound_Log__c
     */
    public static Outbound_Log__c createOutboundLog(String url, String httpMethod, String requestBody, HttpResponse response, String errorMessage) {
        Outbound_Log__c log = buildOutboundLog(url, httpMethod, requestBody, response, errorMessage);
        insert log;
        return log;
    }

    public static void updateOutboundLog(String logId, HttpResponse response, String errorMessage) {
        Outbound_Log__c log = new Outbound_Log__c();
        log.Id = logId;
        if (response != null) {
            log.Response_Status_Code__c = response.getStatusCode();
            log.Response_Body__c = response.getBody();
        }

        if (String.isNotBlank(errorMessage)) {
            log.Error_Message__c = errorMessage;
        }

        update log;
    }

    public static SYS_Exception__c createCustomException(String developerMsg, String exceptionMsg, String stackTrace, String type) {
        SYS_Exception__c objException = new SYS_Exception__c( Developer_Message__c = developerMsg, Exception_Message__c = exceptionMsg,
        SYS_StackTrace_c__c = stackTrace, Type__c = type );
        insert objException;

        return objException;
    }

    public static void saveException(Exception exceptionObject) {      
        SYS_Exception__c newExceptionRecord = new SYS_Exception__c();
        newExceptionRecord.Type__c = exceptionObject.getTypeName();
        newExceptionRecord.Exception_Message__c = exceptionObject.getMessage();
        newExceptionRecord.SYS_StackTrace_c__c = exceptionObject.getStackTraceString();
        Database.insert(newExceptionRecord, false);
    }

    /**
     * Build Outbound Log
     * @description Create Outbound Log (not insert to DB)
     * @param url: request endpoint
     * @param httpMethod: http method
     * @param requestBody: body request
     * @param response: HttpResponse Object
     * @param errorMessage: Error Message
     * @return Outbound_Log__c
     */
    public static Outbound_Log__c buildOutboundLog(String url, String httpMethod, String requestBody, HttpResponse response, String errorMessage){
        Outbound_Log__c log = new Outbound_Log__c();
        log.Endpoint__c = url;
        log.HTTP_Method__c = httpMethod;
        // log.Request_Body__c = requestBody;
        if (requestBody != null) {
            requestBody = (requestBody !=null && requestBody.length() > 131072) ? requestBody.subString(0, 131072) : requestBody;
            log.Request_Body__c = requestBody;
        }
        if (response != null) {
            log.Response_Status_Code__c = response.getStatusCode();
            // log.Response_Body__c = response.getBody();
            String responseBody = response.getBody();
            responseBody = (responseBody != null && responseBody.length() > 131072) ? responseBody.subString(0, 131072) : responseBody;
            log.Response_Body__c = responseBody;
        }
        if (String.isNotBlank(errorMessage)) {
            log.Error_Message__c = errorMessage;
        }
        return log;
    }
    
    
    /**
     * Convert Date or DateTime to dd.MM.yyyy format
     * @param dt: Date or DateTime object
     * @return String: dd.MM.yyyy format
     */
    public static String formatDateTimeToDDMMYYYY(Object dt) {
        if (dt == null) return null;
        if (dt instanceof Date) {
            return String.valueOf(((Date) dt).day()).leftPad(2, '0') + '.' + String.valueOf(((Date) dt).month()).leftPad(2, '0') + '.' + String.valueOf(((Date) dt).year());
        } else if (dt instanceof DateTime) {
            return String.valueOf(((DateTime) dt).day()).leftPad(2, '0') + '.' + String.valueOf(((DateTime) dt).month()).leftPad(2, '0') + '.' + String.valueOf(((DateTime) dt).year());
        }
        return null;
    }
    
}