/*****Class Description*******************
* Name           : DealerVehiclePurchaseOrderController
* Test Class     : DealerVehiclePurchaseOrderControllerTest
* Purpose        : Create Dealership_Purchase_Order__c
* Version  Author      DateModified     Description
* 1.0      thangnv12    08/04/2025       Init
* *******************************************/
public with sharing class DealerVehiclePurchaseOrderController {
    /**
     * Wrapper class for product family Master Data
     */
    public class MasterData {
        @AuraEnabled public String familyValue;
        @AuraEnabled public String familyValueName;
        @AuraEnabled public String family;
        
        public MasterData(String familyValue, String familyValueName, String family) {
            this.familyValue = familyValue;
            this.familyValueName = familyValueName;
            this.family = family;
        }
    }

    /**
     * Get Master Data
     * @return List<MasterData> Master data for dealer vehicle purchase order page
     */
    @AuraEnabled(cacheable=true)
    public static List<MasterData> getMasterData(String modelCode, List<String> families) {
        String model = modelCode + '%';
        System.debug(model);
        System.debug(families);
        List<AggregateResult> results = [
            SELECT Product2.VF_FamilyValue__c, Product2.VF_FamilyValueName__c, Product2.Family 
            FROM PricebookEntry 
            WHERE Product2.RecordType.Name = 'Vehicle' 
            AND Product2.Family IN :families
            AND Product2.IsActive = true AND Product2.VF_FamilyValue__c != null AND Product2.VF_FamilyValueName__c != null AND Product2.Family != null
            AND ProductCode LIKE :model
            GROUP BY Product2.VF_FamilyValue__c, Product2.VF_FamilyValueName__c, Product2.Family
        ];
        
        List<MasterData> masterDataList = new List<MasterData>();
        for (AggregateResult ar : results) {
            masterDataList.add(new MasterData(
                (String)ar.get('VF_FamilyValue__c'), 
                (String)ar.get('VF_FamilyValueName__c'), 
                (String)ar.get('Family')
            ));
        }
        System.debug(masterDataList);
        return masterDataList;
    }
    /**
     * Get SAP Order Price
     * @return DealerPurchaseOrderSapHelper.GetPriceResponseWrapper SAP order price response
     */
    @AuraEnabled
    public static DealerPurchaseOrderSapHelper.GetPriceResponseWrapper getSapOrderPrice(List<Dealership_Purchase_Order_Item__c> orderItems) {
        try {
            return DealerPurchaseOrderSapHelper.getVehicleOrderPriceFromSAP(orderItems);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    /**
     * Get Total Product Price
     * @param productCodes Product Codes
     * @return Decimal Total price
     */
    @AuraEnabled(cacheable=true)
    public static Decimal getTotalProductPrice(List<String> productCodes) {
        Decimal totalPrice = 0;
        try {
            // Get the Standard Price Book ID
            Id standardPriceBookId = Test.isRunningTest() ? Test.getStandardPricebookId() : [SELECT Id FROM Pricebook2 WHERE IsStandard = TRUE LIMIT 1]?.Id;
            // Get PricebookEntry for the products from the Standard Price Book
            List<PricebookEntry> priceEntries = [
                SELECT UnitPrice, Product2Id 
                FROM PricebookEntry 
                WHERE Pricebook2Id = :standardPriceBookId 
                AND ProductCode IN :productCodes
                AND IsActive = TRUE
            ];

            if (priceEntries.isEmpty()) {
                throw new DealerPurchaseOrderException('UNIT_PRICE_NOT_FOUND');
            }

            // Calculate the total price by summing up the prices
            for (PricebookEntry entry : priceEntries) {
                totalPrice += entry.UnitPrice;
            }

        } catch (DealerPurchaseOrderException e) {
            throw new AuraHandledException(e.getMessage()); 
        } catch (Exception e) {
            throw new AuraHandledException('An unexpected error occurred: ' + e.getMessage());
        }

        return totalPrice;
    }
    
    /**
     * Create Dealer Purchase Order
     * @param recordOrder Dealer Purchase Order record
     * @param orderItems Dealer Purchase Order Item records
     * @return Id Dealer Purchase Order record Id
     */
    @AuraEnabled
    public static Id createDealerPurchaseOrder(Dealership_Purchase_Order__c recordOrder, List<Dealership_Purchase_Order_Item__c> orderItems){
        try {
            // Query the Account through Contact
            User u = [
                SELECT AccountId
                FROM User
                WHERE Id = :UserInfo.getUserId()
                LIMIT 1
            ];
            if (u.AccountId == null) throw new DealerPurchaseOrderException('No account found for the current user.');
            recordOrder.Account__c = u.AccountId;
            recordOrder.Status__c = 'Draft';
            System.debug(recordOrder);
            if (recordOrder.Id != null) {
                update recordOrder;
            } else {
                insert recordOrder;
            }
            // delete old items
            List<Dealership_Purchase_Order_Item__c> items = [SELECT Id FROM Dealership_Purchase_Order_Item__c WHERE Dealership_Purchase_Order__c = :recordOrder.Id];
            if (!items.isEmpty()){
                delete items;
            }
            // insert new items
            for (Dealership_Purchase_Order_Item__c item : orderItems) {
                item.Dealership_Purchase_Order__c = recordOrder.Id;
            }
            insert orderItems;
            return recordOrder.Id;
        } catch (Exception e) {
            System.debug(e.getMessage());
            System.debug(e.getLineNumber());
            throw new DealerPurchaseOrderException(e.getMessage());
        }
    }

    /**
     * Get Dealer Purchase Order
     * @param recordId Id of the Dealer Purchase Order record
     * @return DealerPurchaseOrderData
     */
    @AuraEnabled(cacheable=true)
    public static DealerPurhaseOrderData getDealerPurchaseOrder(String recordId){
        try {
            List<Dealership_Purchase_Order__c> orders = [SELECT Id, Customer_Order_Number__c, Description__c, Expected_Delivery_Date__c, Purchase_Order_Category__c, Estimated_Wholesales_Amount__c, (SELECT Id, Battery__c, Exterior_Color__c, Interior_Color__c, Model__c, Product_Code__c, Quantity__c, Unit_Price__c, Sunroof__c, Interior_Equipment__c FROM Dealership_Purchase_Order_Items__r) FROM Dealership_Purchase_Order__c WHERE Id = :recordId];
            if (orders.isEmpty()) throw new DealerPurchaseOrderException('No purchase order found.');
            return new DealerPurhaseOrderData(orders[0]);
        } catch (Exception e) {
            System.debug(e.getMessage());
            System.debug(e.getLineNumber());
            throw new DealerPurchaseOrderException(e.getMessage());
        }
    }

    /**
     * Wrapper class for Dealer Purchase Order Data
     * @param {Dealership_Purchase_Order__c} recordOrder: order record
     */
    public class DealerPurhaseOrderData {
        @AuraEnabled
        public Dealership_Purchase_Order__c header {get; set;}
        @AuraEnabled
        public List<Dealership_Purchase_Order_Item__c> items {get; set;}
        public DealerPurhaseOrderData(Dealership_Purchase_Order__c header) {
            this.header = header;
            this.items = header.Dealership_Purchase_Order_Items__r;
        }
    }

    /**
     * Exception class for Dealer Purchase Order
     */
    public class DealerPurchaseOrderException extends Exception {}
}