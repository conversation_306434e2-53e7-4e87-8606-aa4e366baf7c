<?xml version="1.0" encoding="UTF-8"?>
<DuplicateRule xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionOnInsert>Block</actionOnInsert>
    <actionOnUpdate>Block</actionOnUpdate>
    <alertText>Duplicate Contact Detected. Please review existing records.</alertText>
    <description xsi:nil="true"/>
    <duplicateRuleFilter>
        <booleanFilter xsi:nil="true"/>
        <duplicateRuleFilterItems>
            <field>RecordType</field>
            <operation>equals</operation>
            <value>Customer</value>
            <sortOrder>1</sortOrder>
            <table>Contact</table>
        </duplicateRuleFilterItems>
    </duplicateRuleFilter>
    <duplicateRuleMatchRules>
        <matchRuleSObjectType>Contact</matchRuleSObjectType>
        <matchingRule>Customer_Contact_Matching_Rule</matchingRule>
        <objectMapping xsi:nil="true"/>
    </duplicateRuleMatchRules>
    <isActive>true</isActive>
    <masterLabel>Contact Phone Duplicate Rule</masterLabel>
    <securityOption>BypassSharingRules</securityOption>
    <sortOrder>3</sortOrder>
</DuplicateRule>
