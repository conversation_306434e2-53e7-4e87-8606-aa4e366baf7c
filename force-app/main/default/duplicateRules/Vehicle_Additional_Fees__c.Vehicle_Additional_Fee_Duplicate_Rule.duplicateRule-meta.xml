<?xml version="1.0" encoding="UTF-8"?>
<DuplicateRule xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionOnInsert>Block</actionOnInsert>
    <actionOnUpdate>Block</actionOnUpdate>
    <alertText>A record with the same  already exists. Please ensure that there are no duplicate entries before proceeding.</alertText>
    <description xsi:nil="true"/>
    <duplicateRuleFilter xsi:nil="true"/>
    <duplicateRuleMatchRules>
        <matchRuleSObjectType>Vehicle_Additional_Fees__c</matchRuleSObjectType>
        <matchingRule>Vehicle_Additional_Fee_Matching_Rule</matchingRule>
        <objectMapping xsi:nil="true"/>
    </duplicateRuleMatchRules>
    <isActive>true</isActive>
    <masterLabel>Vehicle Additional Fee Duplicate Rule</masterLabel>
    <securityOption>BypassSharingRules</securityOption>
    <sortOrder>1</sortOrder>
</DuplicateRule>
