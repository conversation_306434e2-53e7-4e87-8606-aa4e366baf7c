<?xml version="1.0" encoding="UTF-8"?>
<DuplicateRule xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionOnInsert>Block</actionOnInsert>
    <actionOnUpdate>Block</actionOnUpdate>
    <alertText>Duplicate Person Account Detected. Please review the existing records.</alertText>
    <description>Unique Person Account</description>
    <duplicateRuleFilter xsi:nil="true"/>
    <duplicateRuleMatchRules>
        <matchRuleSObjectType>PersonAccount</matchRuleSObjectType>
        <matchingRule>Person_Account_Matching_Rule</matchingRule>
        <objectMapping xsi:nil="true"/>
    </duplicateRuleMatchRules>
    <isActive>true</isActive>
    <masterLabel>Unique Person Account</masterLabel>
    <securityOption>BypassSharingRules</securityOption>
    <sortOrder>2</sortOrder>
</DuplicateRule>
