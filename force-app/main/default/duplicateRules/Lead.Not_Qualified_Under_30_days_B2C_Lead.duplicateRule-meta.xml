<?xml version="1.0" encoding="UTF-8"?>
<DuplicateRule xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionOnInsert>Block</actionOnInsert>
    <actionOnUpdate>Block</actionOnUpdate>
    <alertText>The lead with the same mobile phone number was created less than 30 days old and not yet qualified. Please review and update on the old lead.</alertText>
    <description>Not Qualified &amp; Under 30 days B2C Lead</description>
    <duplicateRuleFilter>
        <booleanFilter xsi:nil="true"/>
        <duplicateRuleFilterItems>
            <field>Potential_Duplicate__c</field>
            <operation>equals</operation>
            <value>false</value>
            <sortOrder>1</sortOrder>
            <table>Lead</table>
        </duplicateRuleFilterItems>
        <duplicateRuleFilterItems>
            <field>Lead_Age__c</field>
            <operation>lessOrEqual</operation>
            <value>30.0</value>
            <sortOrder>2</sortOrder>
            <table>Lead</table>
        </duplicateRuleFilterItems>
        <duplicateRuleFilterItems>
            <field>Status</field>
            <operation>equals</operation>
            <value>New, Verified</value>
            <sortOrder>3</sortOrder>
            <table>Lead</table>
        </duplicateRuleFilterItems>
    </duplicateRuleFilter>
    <duplicateRuleMatchRules>
        <matchRuleSObjectType>Lead</matchRuleSObjectType>
        <matchingRule>Lead_Matching_Rule</matchingRule>
        <objectMapping xsi:nil="true"/>
    </duplicateRuleMatchRules>
    <isActive>true</isActive>
    <masterLabel>Not Qualified &amp; Under 30 days B2C Lead</masterLabel>
    <securityOption>EnforceSharingRules</securityOption>
    <sortOrder>3</sortOrder>
</DuplicateRule>
