<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <submitter>Dealer_Submitter</submitter>
        <type>group</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Asset__c</field>
        <field>Category__c</field>
        <field>Type__c</field>
        <field>Claim_to__c</field>
        <field>Main_Part__c</field>
        <field>Main_Work_Type__c</field>
        <field>Issue_Description__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_WC_Status_to_HO_Approval</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <type>adhoc</type>
            </approver>
        </assignedApprover>
        <description>Step 1 - SM</description>
        <label>Step 1 Approval</label>
        <name>Step_1_Approval</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_WC_Status_to_MD_Approval</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>AFS_HO_Approver</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Step 2 - HO</description>
        <entryCriteria>
            <criteriaItems>
                <field>Warranty_Claim__c.Status__c</field>
                <operation>equals</operation>
                <value>HO Approval</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>ApproveRecord</ifCriteriaNotMet>
        <label>Step 2 Approval</label>
        <name>Step_2_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>AFS_MD_Approver</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Step 3 - MD</description>
        <entryCriteria>
            <criteriaItems>
                <field>Warranty_Claim__c.Status__c</field>
                <operation>equals</operation>
                <value>MD Approval</value>
            </criteriaItems>
        </entryCriteria>
        <label>Step 3 Approval</label>
        <name>Step_3_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <formula>AND(
				ISPICKVAL(Status__c, &apos;Open&apos;),
				Number_of_rejection__c &lt; 3,
				Concern__r.Grand_Total__c &gt; $Setup.System_Default__c.Warranty_Claim_Rank_B__c
)</formula>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Set_WC_Status_To_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Increase_Rejection</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Set_WC_Status_to_Open</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Set_WC_Status_to_SM_Approval</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>WC Level 3</label>
    <processOrder>3</processOrder>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>false</showApprovalHistory>
</ApprovalProcess>
