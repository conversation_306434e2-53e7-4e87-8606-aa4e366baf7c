<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <submitter>Dealer_Submitter</submitter>
        <type>group</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Asset__c</field>
        <field>Category__c</field>
        <field>Type__c</field>
        <field>Claim_to__c</field>
        <field>Main_Part__c</field>
        <field>Main_Work_Type__c</field>
        <field>Issue_Description__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <type>adhoc</type>
            </approver>
        </assignedApprover>
        <description>Step 1 - SM</description>
        <label>Step 1 Approval</label>
        <name>Step_1_Approval</name>
    </approvalStep>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <formula>AND(
				ISPICKVAL(Status__c, &apos;Open&apos;),
    Number_of_rejection__c &lt; 3,
    Concern__r.Grand_Total__c &lt;= $Setup.System_Default__c.Warranty_Claim_Rank_A__c
)</formula>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Set_WC_Status_To_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Increase_Rejection</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Set_WC_Status_to_Open</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Set_WC_Status_to_SM_Approval</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>WC Level 1</label>
    <processOrder>1</processOrder>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>false</showApprovalHistory>
</ApprovalProcess>
